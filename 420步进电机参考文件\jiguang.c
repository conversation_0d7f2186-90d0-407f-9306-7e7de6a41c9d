#include "bujin.h"
#include "shineng.h"
#include "jiguang.h"
#include "main.h"
#include "usart.h"
#include <stdbool.h>
#include <stdint.h>
#include <math.h>
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif
// 定义方向
bool dir_yaw;    // true表示正向，false表示反向
bool dir_pitch;  // true表示正向，false表示反向
float time_step = 0.1f;  // 时间步长，可以根据需要调整
float time = 0;
int oo=1000;

// 当前角度记录
int  current_clks_pitch = 0;  // 俯仰轴当前clk数
int current_clks_yaw = 0;  // 偏航轴当前clk数
float pitch_angle;
int target_clks_pitch;
float yaw_angle;
int target_clks_yaw;
int yanchi=10;
float xxxx=135.0;
/**
 * @brief 控制Yaw轴电机移动指定的脉冲数
 * @param target_clks 目标脉冲数
 */
int move_yaw(uint32_t target_clks) {
    // 计算方向
    if (target_clks > current_clks_yaw) {
        dir_yaw = true;  // 正向
    } else if (target_clks < current_clks_yaw) {
        dir_yaw = false; // 反向
    } else {
        // 目标与当前相同，无需移动
        return dir_yaw;
    }}


/**
 * 计算当前角度（带零点校准）
 * @param current_clks 当前clk数
 * @param zero_clks 零点clk数
 * @param is_pitch_axis 是否为俯仰轴（true=俯仰轴，false=偏航轴）
 * @return 当前角度（度），全为正（0°~最大角度）
 */
float calculate_current_angle(int current_clks, int zero_clks, bool is_pitch_axis) {
    // 计算clk差（当前 - 零点）
    int clks_diff = current_clks - zero_clks;

    // 计算角度（正转=角度增加，反转=角度减少）
    float angle = clks_diff * ANGLE_PER_CLK;

    // 确保角度在 0°~最大角度范围内
    if (is_pitch_axis) {
        if (angle < 0.0f) angle = 0.0f;
        if (angle > PITCH_MAX_ANGLE) angle = PITCH_MAX_ANGLE;
    } else {
        if (angle < 0.0f) angle = 0.0f;
        if (angle > YAW_MAX_ANGLE) angle = YAW_MAX_ANGLE;
    }

    return angle;
}

int angle_to_clks(float angle, bool is_pitch_axis){
     int clks ;
    // 确保角度在有效范围内
	if (is_pitch_axis) {
        if (angle > PITCH_MAX_ANGLE) angle = PITCH_MAX_ANGLE;
		else if(angle <= PITCH_MAX_ANGLE && angle>=0) clks = (int)(angle / ANGLE_PER_CLK);
	}
    
	else {
        if (angle > YAW_MAX_ANGLE) angle = YAW_MAX_ANGLE;
    else if(angle <= YAW_MAX_ANGLE && angle>=0) clks = (int)(angle / ANGLE_PER_CLK);}
    return clks;
	}

void move_to_clks(int target_clks,bool is_pitch_axis){

    if (is_pitch_axis) {
          Emm_V5_Pos_Control(pitch_addr,pitch_dir,30,0,target_clks,1,0);
          HAL_Delay(yanchi);
        current_clks_pitch = target_clks;
    } 
		else {
         Emm_V5_Pos_Control(rotation_addr,rotation_dir, 30,0,target_clks,1,0);
            HAL_Delay(yanchi);
        current_clks_yaw = target_clks;
    }}
	
void draw_sine_wave(float amplitude, float frequency, float duration) {
    //time_step = 0.1;  // 时间步长，可以根据需要调整
    time = 0;
    while (time <= duration) {
        // 计算当前角度
        pitch_angle = 90+amplitude * sin(2 * M_PI * frequency * time);
        // 转换为clk值
        target_clks_pitch = angle_to_clks(pitch_angle,1);
        yaw_angle=time;
        target_clks_yaw = angle_to_clks(yaw_angle,0);

        // 移动电机
        move_to_clks(target_clks_pitch, true);
        move_to_clks(target_clks_yaw+yaw_mid__clk, false);

        // 等待一段时间
        HAL_Delay(1);  // 可以根据需要调整延迟时间

        // 更新时间
        time += time_step;
    }
	}
