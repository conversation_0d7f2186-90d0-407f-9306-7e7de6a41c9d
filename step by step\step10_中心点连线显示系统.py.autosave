# bilibili搜索学不会电磁场看教程
# K230矩形中心激光定位系统 - 第十步：中心点连线显示系统
# 基于Step9混合优化系统，新增矩形中心点和屏幕中心点的可视化连线功能
#
# 新增功能：
# 1. 屏幕中心点标记 - 黄色圆点标记屏幕中心位置
# 2. 中心点连线功能 - 矩形中心点与屏幕中心点之间的实时连线
# 3. 坐标信息显示 - 实时显示矩形中心点坐标
# 4. 可视化反馈 - 为STM32控制系统提供直观的视觉反馈

import time
import os
import sys
import math
from media.sensor import *
from media.display import *
from media.media import *
from machine import TOUCH
from machine import UART
from machine import FPIOA

# 常量定义
CAM_CHN_ID_0 = 0

# ==================== Step10新增：可视化配置 ====================
# 屏幕中心点配置
SCREEN_CENTER_MARKER = {
    'x': 200,                    # 屏幕中心X坐标
    'y': 120,                    # 屏幕中心Y坐标
    'radius': 6,                 # 圆点半径
    'color': (255, 255, 0),      # 黄色
    'thickness': 3,              # 线条粗细
    'cross_size': 10             # 十字标记大小
}

# 连线配置
CENTER_LINE_CONFIG = {
    'color': (0, 255, 255),      # 浅蓝色连线
    'thickness': 2,              # 线条粗细
    'show_distance': True        # 是否显示距离信息
}

# 坐标显示配置
COORDINATE_DISPLAY = {
    'font_size': 12,             # 字体大小
    'color': (0, 255, 0),        # 浅绿色
    'position_x': 250,           # 显示位置X
    'position_y': 25,            # 显示位置Y
}

# ==================== 继承Step9的所有配置 ====================
# Step8核心：性能目标配置
TARGET_FPS = 20              # 目标帧率 ≥20FPS
TARGET_SUCCESS_RATE = 85     # 提高目标识别率到85%
TARGET_LATENCY = 50          # 目标延迟 ≤50ms

# Step8快速检测模式配置
FAST_MODE = True             # 启用快速模式
SIMPLIFIED_VALIDATION = True # 简化特征验证
ADAPTIVE_ROI = True          # 启用自适应ROI
MOTION_PREDICTION = True     # 启用运动预测

# Step7核心：LAB阈值系统
threshold_edit_mode = "rect"
threshold_current = [13, 35, 20, -5, -44, 16]  # 用户调整的LAB阈值

# 阈值存储机制 - Step7的稳定设计
threshold_dict = {
    'rect': [(13, 35, 20, -5, -44, 16)],  # 黑色矩形的LAB阈值
    'laser': [(47, 80, 9, 91, -55, 63), (16, 37, 23, 74, -48, 52)]  # 激光点LAB阈值
}

# Step8核心：统计系统
rect_detection_stats = {
    'total_attempts': 0,
    'successful_detections': 0,
    'success_rate': 0.0,
    'recent_attempts': [],    # 最近50次检测记录
    'recent_success_rate': 0.0,
    'fps_history': [],        # FPS历史记录
    'avg_fps': 0.0,
    'processing_times': [],   # 处理时间历史
    'avg_processing_time': 0.0
}

# Step8核心：快速多帧稳定性检测系统（Step10运动优化版）
fast_multi_frame_detection = {
    'frame_buffer': [],           # 存储最近帧的检测结果
    'buffer_size': 1,            # 增加缓冲区大小，提高运动稳定性
    'stable_threshold': 1,       # 调整稳定阈值（5帧中至少3帧检测成功）
    'position_threshold': 30.0,  # 增加位置变化阈值，适应运动
    'last_stable_center': None,
    'last_send_center': None,
    'send_threshold': 3.0,       # 减少发送阈值，提高响应性
    'confidence_threshold': 0.5, # 降低置信度阈值，适应运动模糊
    'motion_adaptive_threshold': True,  # 新增：运动自适应阈值
    'velocity_weight': 0.3,      # 新增：速度权重
    'position_weight': 0.7,      # 新增：位置权重
    'temporal_consistency': 0.8, # 新增：时间一致性权重
}

# Step8核心：自适应ROI系统（Step10运动优化版）
adaptive_roi_config = {
    'enabled': ADAPTIVE_ROI,
    'roi_x': 0,
    'roi_y': 0,
    'roi_width': 400,
    'roi_height': 240,
    'expansion_factor': 2.0,     # 增加ROI扩展因子，适应运动
    'min_roi_size': 120,         # 增加最小ROI尺寸
    'max_roi_size': 400,         # 最大ROI尺寸
    'update_threshold': 3,       # 减少ROI更新阈值，更快响应
    'frames_since_update': 0,
    'motion_expansion_factor': 2.2,  # 新增：运动状态下的扩展因子
    'velocity_prediction_factor': 1.5, # 新增：基于速度的预测扩展
    'roi_smoothing': 0.7,        # 新增：ROI位置平滑因子
    'last_roi_center': None,     # 新增：上一次ROI中心
    'roi_stability_counter': 0,  # 新增：ROI稳定性计数器
}

# Step8核心：运动预测系统（Step10优化版）
motion_prediction_config = {
    'enabled': MOTION_PREDICTION,
    'history_length': 8,         # 增加运动历史长度，提高预测精度
    'position_history': [],      # 位置历史
    'velocity_history': [],      # 速度历史
    'acceleration_history': [],  # 新增：加速度历史
    'predicted_position': None,  # 预测位置
    'prediction_confidence': 0.0,# 预测置信度
    'max_velocity': 150.0,       # 增加最大速度限制
    'velocity_smoothing': 0.8,   # 优化速度平滑因子
    'acceleration_smoothing': 0.6, # 新增：加速度平滑因子
    'prediction_steps': 2,       # 新增：预测步数
    'motion_stability_threshold': 3.0,  # 新增：运动稳定性阈值
    'direction_change_threshold': 45.0, # 新增：方向变化阈值（度）
}

# Step8核心：串口通信配置
uart = None
SERIAL_PORT = UART.UART2
BAUD_RATE = 115200
TX_PIN = 11
RX_PIN = 12

PACKET_HEADER = [0xAA, 0x55]
PACKET_TAIL = [0x0D, 0x0A]
SCREEN_CENTER_X = 200
SCREEN_CENTER_Y = 120

# 检测参数配置
RECT_THRESHOLD = [(13, 35, 20, -5, -44, 16)]
MIN_RECT_AREA = 1500
MAX_RECT_AREA = 80000
MIN_RECT_ASPECT_RATIO = 0.5
MAX_RECT_ASPECT_RATIO = 2.5

LASER_THRESHOLD = [
    (47, 80, 9, 91, -55, 63),
    (16, 37, 23, 74, -48, 52)
]
MIN_LASER_PIXELS = 15

# 图像和显示参数
picture_width = 400
picture_height = 240
sensor_id = 2
DISPLAY_MODE = "LCD"
DISPLAY_WIDTH = 800
DISPLAY_HEIGHT = 480
SCALE_FACTOR = 2.0
SCALED_WIDTH = int(picture_width * SCALE_FACTOR)
SCALED_HEIGHT = int(picture_height * SCALE_FACTOR)

# 按键定义（支持4个按钮）
BUTTON_WIDTH = 150
BUTTON_HEIGHT = 60
BUTTON_Y = SCALED_HEIGHT - BUTTON_HEIGHT - 10
BUTTON_SPACING = 160  # 减小间距以容纳4个按钮
BUTTON_START_X = 20   # 调整起始位置
BUTTON1_POS = (BUTTON_START_X, BUTTON_Y)
BUTTON2_POS = (BUTTON_START_X + BUTTON_SPACING, BUTTON_Y)
BUTTON3_POS = (BUTTON_START_X + BUTTON_SPACING * 2, BUTTON_Y)
BUTTON4_POS = (BUTTON_START_X + BUTTON_SPACING * 3, BUTTON_Y)  # 第四个按钮
BUTTON_ACTIVE_COLOR = (0, 255, 0)
BUTTON_INACTIVE_COLOR = (80, 80, 80)
BUTTON_TEXT_COLOR = (255, 255, 255)

# 全局变量
sensor = None
frame_count = 0
current_mode = 1
last_send_time = 0
SEND_INTERVAL = 30  # Step8的高频发送间隔

# ==================== Step10新增：可视化绘制函数 ====================
def draw_screen_center_marker(img):
    """绘制屏幕中心点标记 - Step10核心功能"""
    try:
        center_x = SCREEN_CENTER_MARKER['x']
        center_y = SCREEN_CENTER_MARKER['y']
        radius = SCREEN_CENTER_MARKER['radius']
        color = SCREEN_CENTER_MARKER['color']
        thickness = SCREEN_CENTER_MARKER['thickness']
        cross_size = SCREEN_CENTER_MARKER['cross_size']

        # 绘制中心圆点
        img.draw_circle(center_x, center_y, radius, color=color, thickness=thickness)

        # 绘制十字标记
        img.draw_line(center_x - cross_size, center_y, center_x + cross_size, center_y,
                     color=color, thickness=2)
        img.draw_line(center_x, center_y - cross_size, center_x, center_y + cross_size,
                     color=color, thickness=2)

        # 绘制标签
        img.draw_string_advanced(center_x - 25, center_y - 25, 10, "屏幕中心", color=color)

    except Exception as e:
        print(f"绘制屏幕中心点错误: {e}")

def draw_center_connection_line(img, rect_center):
    """绘制中心点连线 - Step10核心功能"""
    if rect_center is None:
        return

    try:
        screen_center_x = SCREEN_CENTER_MARKER['x']
        screen_center_y = SCREEN_CENTER_MARKER['y']
        rect_x, rect_y = int(rect_center[0]), int(rect_center[1])

        line_color = CENTER_LINE_CONFIG['color']
        line_thickness = CENTER_LINE_CONFIG['thickness']

        # 绘制连线
        img.draw_line(rect_x, rect_y, screen_center_x, screen_center_y,
                     color=line_color, thickness=line_thickness)

        # 如果启用距离显示
        if CENTER_LINE_CONFIG['show_distance']:
            distance = math.sqrt((rect_x - screen_center_x)**2 + (rect_y - screen_center_y)**2)
            mid_x = (rect_x + screen_center_x) // 2
            mid_y = (rect_y + screen_center_y) // 2
            img.draw_string_advanced(mid_x - 15, mid_y - 10, 10, f"{distance:.1f}", color=line_color)

        # 绘制方向箭头（简化版）
        arrow_size = 8
        dx = screen_center_x - rect_x
        dy = screen_center_y - rect_y
        length = math.sqrt(dx*dx + dy*dy)

        if length > 0:
            # 单位向量
            ux = dx / length
            uy = dy / length

            # 箭头位置（距离屏幕中心点一定距离）
            arrow_x = screen_center_x - ux * 15
            arrow_y = screen_center_y - uy * 15

            # 绘制简单箭头
            img.draw_circle(int(arrow_x), int(arrow_y), 3, color=line_color, thickness=2)

    except Exception as e:
        print(f"绘制连线错误: {e}")

def draw_coordinate_display(img, rect_center):
    """显示坐标信息 - Step10核心功能"""
    try:
        pos_x = COORDINATE_DISPLAY['position_x']
        pos_y = COORDINATE_DISPLAY['position_y']
        font_size = COORDINATE_DISPLAY['font_size']
        color = COORDINATE_DISPLAY['color']

        if rect_center is not None:
            rect_x, rect_y = int(rect_center[0]), int(rect_center[1])

            # 详细格式显示
            coord_text = f"矩形中心: X={rect_x}, Y={rect_y}"
            img.draw_string_advanced(pos_x, pos_y, font_size, coord_text, color=color)

            # 显示与屏幕中心的偏差
            offset_x = rect_x - SCREEN_CENTER_X
            offset_y = rect_y - SCREEN_CENTER_Y
            offset_text = f"偏差: ΔX={offset_x:+d}, ΔY={offset_y:+d}"
            img.draw_string_advanced(pos_x, pos_y + 15, font_size - 2, offset_text, color=(255, 255, 0))
        else:
            # 未检测到时的显示
            img.draw_string_advanced(pos_x, pos_y, font_size, "矩形中心: 未检测到", color=(255, 0, 0))

    except Exception as e:
        print(f"显示坐标信息错误: {e}")

def draw_visual_feedback_info(img):
    """绘制可视化反馈信息 - Step10核心功能（运动优化版）"""
    try:
        # 显示可视化功能说明
        info_y = picture_height - 80
        img.draw_string_advanced(10, info_y, 10, "Step10可视化:", color=(255, 255, 0))
        img.draw_string_advanced(10, info_y + 12, 10, "黄色圆点=屏幕中心", color=(255, 255, 0))
        img.draw_string_advanced(10, info_y + 24, 10, "蓝色连线=中心连接", color=(0, 255, 255))
        img.draw_string_advanced(10, info_y + 36, 10, "绿色文字=坐标信息", color=(0, 255, 0))

        # 新增：运动状态显示
        motion_info = get_enhanced_motion_info()
        motion_states = ["静止", "慢速", "中速", "快速", "极快"]
        motion_state_text = motion_states[min(motion_info['state'], 4)]
        motion_color = [(0, 255, 0), (255, 255, 0), (255, 165, 0), (255, 0, 0), (255, 0, 255)][min(motion_info['state'], 4)]

        img.draw_string_advanced(10, info_y + 48, 10, f"运动状态: {motion_state_text}", color=motion_color)
        img.draw_string_advanced(10, info_y + 60, 10, f"速度: {motion_info['speed']:.1f} 稳定性: {motion_info['stability']:.2f}", color=(255, 255, 255))
        img.draw_string_advanced(10, info_y + 72, 10, "为STM32控制提供视觉反馈", color=(255, 255, 255))

    except Exception as e:
        print(f"显示可视化信息错误: {e}")

# ==================== 继承Step9的所有核心算法 ====================
# Step8核心：自适应ROI函数（Step10运动优化版）
def update_adaptive_roi(detection_center):
    """根据检测结果更新自适应ROI - Step10运动优化算法"""
    global adaptive_roi_config

    if not adaptive_roi_config['enabled'] or detection_center is None:
        return

    center_x, center_y = detection_center

    # 获取运动信息
    motion_info = get_enhanced_motion_info()
    motion_state = motion_info['state']
    speed = motion_info['speed']
    stability = motion_info['stability']

    # 根据运动状态选择扩展因子
    if motion_state == 0:  # 静止
        expansion = adaptive_roi_config['expansion_factor']
    else:  # 运动状态
        expansion = adaptive_roi_config['motion_expansion_factor']
        # 根据速度和稳定性动态调整
        speed_factor = min(2.0, 1.0 + speed / 20.0)
        stability_factor = max(0.8, stability)
        expansion = expansion * speed_factor / stability_factor

    min_size = adaptive_roi_config['min_roi_size']
    max_size = adaptive_roi_config['max_roi_size']

    # 计算新的ROI尺寸（考虑运动预测）
    base_roi_size = int(200 * expansion)

    # 如果有运动预测，扩展ROI以包含预测位置
    predicted_pos, pred_confidence = get_predicted_position()
    if predicted_pos and pred_confidence > 0.3:
        pred_x, pred_y = predicted_pos
        # 计算预测位置与当前位置的距离
        pred_distance = math.sqrt((pred_x - center_x)**2 + (pred_y - center_y)**2)
        # 根据预测距离调整ROI大小
        velocity_expansion = int(pred_distance * adaptive_roi_config['velocity_prediction_factor'])
        base_roi_size += velocity_expansion

    new_roi_size = min(max_size, max(min_size, base_roi_size))

    # ROI位置平滑（减少抖动）
    target_roi_x = center_x - new_roi_size // 2
    target_roi_y = center_y - new_roi_size // 2

    if adaptive_roi_config['last_roi_center'] is not None:
        last_center_x, last_center_y = adaptive_roi_config['last_roi_center']
        smoothing = adaptive_roi_config['roi_smoothing']

        # 平滑ROI中心位置
        smooth_center_x = last_center_x * (1 - smoothing) + center_x * smoothing
        smooth_center_y = last_center_y * (1 - smoothing) + center_y * smoothing

        target_roi_x = smooth_center_x - new_roi_size // 2
        target_roi_y = smooth_center_y - new_roi_size // 2

    # 确保ROI不超出图像边界
    new_roi_x = max(0, min(picture_width - new_roi_size, int(target_roi_x)))
    new_roi_y = max(0, min(picture_height - new_roi_size, int(target_roi_y)))
    new_roi_width = min(new_roi_size, picture_width - new_roi_x)
    new_roi_height = min(new_roi_size, picture_height - new_roi_y)

    # 更新ROI配置
    adaptive_roi_config['roi_x'] = new_roi_x
    adaptive_roi_config['roi_y'] = new_roi_y
    adaptive_roi_config['roi_width'] = new_roi_width
    adaptive_roi_config['roi_height'] = new_roi_height
    adaptive_roi_config['frames_since_update'] = 0
    adaptive_roi_config['last_roi_center'] = (center_x, center_y)

    # ROI稳定性计数
    if motion_state == 0:
        adaptive_roi_config['roi_stability_counter'] += 1
    else:
        adaptive_roi_config['roi_stability_counter'] = 0

def reset_adaptive_roi():
    """重置ROI到全图 - Step8核心算法"""
    global adaptive_roi_config
    adaptive_roi_config['roi_x'] = 0
    adaptive_roi_config['roi_y'] = 0
    adaptive_roi_config['roi_width'] = picture_width
    adaptive_roi_config['roi_height'] = picture_height
    adaptive_roi_config['frames_since_update'] = 0

def get_current_roi():
    """获取当前ROI区域 - Step8核心算法"""
    global adaptive_roi_config
    return (adaptive_roi_config['roi_x'], adaptive_roi_config['roi_y'],
            adaptive_roi_config['roi_width'], adaptive_roi_config['roi_height'])

# Step8核心：运动预测函数（Step10运动优化版）
def update_motion_prediction(current_center):
    """更新运动预测 - Step10运动优化算法"""
    global motion_prediction_config

    if not motion_prediction_config['enabled'] or current_center is None:
        return

    # 添加当前位置到历史
    motion_prediction_config['position_history'].append(current_center)

    # 保持历史长度
    max_length = motion_prediction_config['history_length']
    if len(motion_prediction_config['position_history']) > max_length:
        motion_prediction_config['position_history'].pop(0)

    # 计算速度和加速度
    if len(motion_prediction_config['position_history']) >= 2:
        recent_positions = motion_prediction_config['position_history'][-2:]
        velocity = (
            recent_positions[1][0] - recent_positions[0][0],
            recent_positions[1][1] - recent_positions[0][1]
        )

        # 速度平滑
        if motion_prediction_config['velocity_history']:
            smoothing = motion_prediction_config['velocity_smoothing']
            last_velocity = motion_prediction_config['velocity_history'][-1]
            velocity = (
                last_velocity[0] * (1 - smoothing) + velocity[0] * smoothing,
                last_velocity[1] * (1 - smoothing) + velocity[1] * smoothing
            )

        motion_prediction_config['velocity_history'].append(velocity)

        # 保持速度历史长度
        if len(motion_prediction_config['velocity_history']) > max_length:
            motion_prediction_config['velocity_history'].pop(0)

        # 计算加速度（新增）
        if len(motion_prediction_config['velocity_history']) >= 2:
            recent_velocities = motion_prediction_config['velocity_history'][-2:]
            acceleration = (
                recent_velocities[1][0] - recent_velocities[0][0],
                recent_velocities[1][1] - recent_velocities[0][1]
            )

            # 加速度平滑
            if motion_prediction_config['acceleration_history']:
                acc_smoothing = motion_prediction_config['acceleration_smoothing']
                last_acceleration = motion_prediction_config['acceleration_history'][-1]
                acceleration = (
                    last_acceleration[0] * (1 - acc_smoothing) + acceleration[0] * acc_smoothing,
                    last_acceleration[1] * (1 - acc_smoothing) + acceleration[1] * acc_smoothing
                )

            motion_prediction_config['acceleration_history'].append(acceleration)

            # 保持加速度历史长度
            if len(motion_prediction_config['acceleration_history']) > max_length:
                motion_prediction_config['acceleration_history'].pop(0)

        # 高级预测位置计算（考虑加速度）
        if len(motion_prediction_config['velocity_history']) >= 3:
            current_velocity = motion_prediction_config['velocity_history'][-1]
            current_acceleration = motion_prediction_config['acceleration_history'][-1] if motion_prediction_config['acceleration_history'] else (0, 0)

            # 多步预测
            prediction_steps = motion_prediction_config['prediction_steps']
            predicted_pos = current_center

            for step in range(1, prediction_steps + 1):
                # 考虑加速度的预测公式: pos = pos0 + v*t + 0.5*a*t^2
                predicted_x = predicted_pos[0] + current_velocity[0] * step + 0.5 * current_acceleration[0] * step * step
                predicted_y = predicted_pos[1] + current_velocity[1] * step + 0.5 * current_acceleration[1] * step * step
                predicted_pos = (predicted_x, predicted_y)

            # 速度和加速度限制
            speed = math.sqrt(current_velocity[0]**2 + current_velocity[1]**2)
            acceleration_magnitude = math.sqrt(current_acceleration[0]**2 + current_acceleration[1]**2)

            # 计算运动稳定性
            motion_stability = 1.0 / (1.0 + acceleration_magnitude)

            if speed <= motion_prediction_config['max_velocity']:
                motion_prediction_config['predicted_position'] = predicted_pos
                # 改进的置信度计算（考虑速度、加速度和稳定性）
                speed_confidence = 1.0 - min(1.0, speed / motion_prediction_config['max_velocity'])
                stability_confidence = motion_stability
                motion_prediction_config['prediction_confidence'] = (speed_confidence + stability_confidence) / 2.0
            else:
                motion_prediction_config['prediction_confidence'] = 0.0

def get_predicted_position():
    """获取预测位置 - Step8核心算法"""
    global motion_prediction_config
    return (motion_prediction_config['predicted_position'],
            motion_prediction_config['prediction_confidence'])

def get_motion_state():
    """获取运动状态 - Step10运动优化算法"""
    global motion_prediction_config

    if not motion_prediction_config['velocity_history']:
        return 0  # 静止

    # 获取最近的速度和加速度
    recent_velocity = motion_prediction_config['velocity_history'][-1]
    speed = math.sqrt(recent_velocity[0]**2 + recent_velocity[1]**2)

    # 计算加速度（如果有的话）
    acceleration_magnitude = 0.0
    if motion_prediction_config['acceleration_history']:
        recent_acceleration = motion_prediction_config['acceleration_history'][-1]
        acceleration_magnitude = math.sqrt(recent_acceleration[0]**2 + recent_acceleration[1]**2)

    # 计算运动稳定性（速度变化的一致性）
    motion_stability = 1.0
    if len(motion_prediction_config['velocity_history']) >= 3:
        velocities = motion_prediction_config['velocity_history'][-3:]
        speed_variations = []
        for i in range(len(velocities) - 1):
            v1 = velocities[i]
            v2 = velocities[i + 1]
            speed_diff = abs(math.sqrt(v2[0]**2 + v2[1]**2) - math.sqrt(v1[0]**2 + v1[1]**2))
            speed_variations.append(speed_diff)

        if speed_variations:
            avg_variation = sum(speed_variations) / len(speed_variations)
            motion_stability = 1.0 / (1.0 + avg_variation)

    # 改进的运动状态判断
    stability_threshold = motion_prediction_config['motion_stability_threshold']

    if speed < 3.0 and acceleration_magnitude < 2.0:
        return 0  # 静止
    elif speed < 12.0 and motion_stability > 0.7:
        return 1  # 慢速稳定运动
    elif speed < 25.0 and motion_stability > 0.5:
        return 2  # 中速运动
    elif speed < 40.0:
        return 3  # 快速运动
    else:
        return 4  # 极快速运动

def get_enhanced_motion_info():
    """获取增强的运动信息 - Step10新增功能"""
    global motion_prediction_config

    if not motion_prediction_config['velocity_history']:
        return {
            'state': 0,
            'speed': 0.0,
            'acceleration': 0.0,
            'stability': 1.0,
            'direction': 0.0,
            'prediction_confidence': 0.0
        }

    recent_velocity = motion_prediction_config['velocity_history'][-1]
    speed = math.sqrt(recent_velocity[0]**2 + recent_velocity[1]**2)

    # 计算运动方向（角度）
    direction = math.atan2(recent_velocity[1], recent_velocity[0]) * 180.0 / math.pi
    if direction < 0:
        direction += 360.0

    # 计算加速度
    acceleration_magnitude = 0.0
    if motion_prediction_config['acceleration_history']:
        recent_acceleration = motion_prediction_config['acceleration_history'][-1]
        acceleration_magnitude = math.sqrt(recent_acceleration[0]**2 + recent_acceleration[1]**2)

    # 计算运动稳定性
    motion_stability = 1.0
    if len(motion_prediction_config['velocity_history']) >= 3:
        velocities = motion_prediction_config['velocity_history'][-3:]
        speed_variations = []
        for i in range(len(velocities) - 1):
            v1 = velocities[i]
            v2 = velocities[i + 1]
            speed_diff = abs(math.sqrt(v2[0]**2 + v2[1]**2) - math.sqrt(v1[0]**2 + v1[1]**2))
            speed_variations.append(speed_diff)

        if speed_variations:
            avg_variation = sum(speed_variations) / len(speed_variations)
            motion_stability = 1.0 / (1.0 + avg_variation)

    return {
        'state': get_motion_state(),
        'speed': speed,
        'acceleration': acceleration_magnitude,
        'stability': motion_stability,
        'direction': direction,
        'prediction_confidence': motion_prediction_config['prediction_confidence']
    }

# Step8核心：快速矩形检测算法（集成Step10可视化）
def detect_rectangles_fast(img):
    """快速矩形检测算法 - Step8核心算法 + Step10可视化"""
    try:
        start_time = time.ticks_ms()

        # 获取当前ROI区域
        roi_x, roi_y, roi_width, roi_height = get_current_roi()

        # 创建检测图像副本
        if adaptive_roi_config['enabled'] and (roi_width < picture_width or roi_height < picture_height):
            detect_img = img.copy(roi=(roi_x, roi_y, roi_width, roi_height))
        else:
            detect_img = img.copy()

        # LAB二值化
        binary_img = detect_img.binary(RECT_THRESHOLD)

        # 快速形态学操作
        if FAST_MODE:
            binary_img.erode(1, threshold=1)
        else:
            binary_img.erode(1, threshold=1)
            binary_img.dilate(1, threshold=1)

        # 查找矩形
        rects = binary_img.find_rects(threshold=MIN_RECT_AREA)

        if not rects:
            processing_time = time.ticks_ms() - start_time
            # ==================== Step10新增：绘制屏幕中心点（即使检测失败也显示） ====================
            draw_screen_center_marker(img)
            draw_coordinate_display(img, None)
            draw_visual_feedback_info(img)
            return False, 0, [], None, processing_time

        # 快速特征验证
        valid_rects = []
        for rect in rects:
            area = rect.w() * rect.h()
            if MIN_RECT_AREA <= area <= MAX_RECT_AREA:
                if SIMPLIFIED_VALIDATION:
                    # 简化验证：只检查面积和基本宽高比
                    aspect_ratio = max(rect.w(), rect.h()) / min(rect.w(), rect.h())
                    if MIN_RECT_ASPECT_RATIO <= aspect_ratio <= MAX_RECT_ASPECT_RATIO:
                        valid_rects.append(rect)
                else:
                    # 完整验证
                    aspect_ratio = max(rect.w(), rect.h()) / min(rect.w(), rect.h())
                    if MIN_RECT_ASPECT_RATIO <= aspect_ratio <= MAX_RECT_ASPECT_RATIO:
                        valid_rects.append(rect)

        if len(valid_rects) < 2:
            processing_time = time.ticks_ms() - start_time
            # ==================== Step10新增：绘制屏幕中心点（即使检测失败也显示） ====================
            draw_screen_center_marker(img)
            draw_coordinate_display(img, None)
            draw_visual_feedback_info(img)
            return False, 0, [], None, processing_time

        # 快速配对算法
        valid_rects.sort(key=lambda r: r.w() * r.h(), reverse=True)

        # 限制检查的矩形对数量
        max_pairs_to_check = 3 if FAST_MODE else 5
        best_pair = None
        best_score = 0

        for i in range(min(len(valid_rects), max_pairs_to_check)):
            for j in range(i + 1, min(len(valid_rects), max_pairs_to_check)):
                rect1, rect2 = valid_rects[i], valid_rects[j]

                # 计算中心点
                center1 = (rect1.x() + rect1.w() // 2, rect1.y() + rect1.h() // 2)
                center2 = (rect2.x() + rect2.w() // 2, rect2.y() + rect2.h() // 2)

                # 快速距离检查
                distance = math.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)

                # 根据运动状态调整阈值（Step10运动优化）
                motion_info = get_enhanced_motion_info()
                motion_state = motion_info['state']
                speed = motion_info['speed']
                stability = motion_info['stability']

                # 动态调整匹配阈值
                if motion_state == 0:  # 静止
                    CENTER_MATCH_THRESHOLD = 6.0
                elif motion_state == 1:  # 慢速稳定运动
                    CENTER_MATCH_THRESHOLD = 10.0 + (speed * 0.5)
                elif motion_state == 2:  # 中速运动
                    CENTER_MATCH_THRESHOLD = 15.0 + (speed * 0.3) - (stability * 5.0)
                elif motion_state == 3:  # 快速运动
                    CENTER_MATCH_THRESHOLD = 25.0 + (speed * 0.2) - (stability * 8.0)
                else:  # 极快速运动
                    CENTER_MATCH_THRESHOLD = 35.0 + (speed * 0.1) - (stability * 10.0)

                # 限制阈值范围
                CENTER_MATCH_THRESHOLD = max(6.0, min(50.0, CENTER_MATCH_THRESHOLD))

                if distance <= CENTER_MATCH_THRESHOLD:
                    # 计算配对得分
                    area_score = min(rect1.w() * rect1.h(), rect2.w() * rect2.h())
                    distance_score = 1.0 / (1.0 + distance)
                    total_score = area_score * distance_score

                    if total_score > best_score:
                        best_score = total_score
                        best_pair = (rect1, rect2, center1, center2)

        if best_pair is None:
            processing_time = time.ticks_ms() - start_time
            # ==================== Step10新增：绘制屏幕中心点（即使检测失败也显示） ====================
            draw_screen_center_marker(img)
            draw_coordinate_display(img, None)
            draw_visual_feedback_info(img)
            return False, 0, [], None, processing_time

        rect1, rect2, center1, center2 = best_pair

        # 调整坐标到原图坐标系
        if adaptive_roi_config['enabled'] and (roi_width < picture_width or roi_height < picture_height):
            center1 = (center1[0] + roi_x, center1[1] + roi_y)
            center2 = (center2[0] + roi_x, center2[1] + roi_y)

        # 计算平均中心点
        avg_center = ((center1[0] + center2[0]) / 2, (center1[1] + center2[1]) / 2)

        # 绘制检测结果到原图
        # 调整矩形坐标
        if adaptive_roi_config['enabled'] and (roi_width < picture_width or roi_height < picture_height):
            rect1_adj = (rect1.x() + roi_x, rect1.y() + roi_y, rect1.w(), rect1.h())
            rect2_adj = (rect2.x() + roi_x, rect2.y() + roi_y, rect2.w(), rect2.h())
        else:
            rect1_adj = (rect1.x(), rect1.y(), rect1.w(), rect1.h())
            rect2_adj = (rect2.x(), rect2.y(), rect2.w(), rect2.h())

        # 绘制矩形框
        img.draw_rectangle(rect1_adj[0], rect1_adj[1], rect1_adj[2], rect1_adj[3],
                          color=(255, 0, 255), thickness=2, fill=False)
        img.draw_rectangle(rect2_adj[0], rect2_adj[1], rect2_adj[2], rect2_adj[3],
                          color=(0, 255, 0), thickness=2, fill=False)

        # 绘制中心点
        img.draw_circle(int(avg_center[0]), int(avg_center[1]), 8,
                       color=(255, 0, 0), thickness=3)
        img.draw_string_advanced(int(avg_center[0]) - 20, int(avg_center[1]) - 30,
                               14, "CENTER", color=(255, 0, 0))

        # ==================== Step10新增：绘制可视化元素 ====================
        # 绘制屏幕中心点标记
        draw_screen_center_marker(img)

        # 绘制中心点连线
        draw_center_connection_line(img, avg_center)

        # 显示坐标信息
        draw_coordinate_display(img, avg_center)

        # 显示可视化反馈信息
        draw_visual_feedback_info(img)

        # 更新自适应ROI
        update_adaptive_roi(avg_center)

        # 更新运动预测
        update_motion_prediction(avg_center)

        processing_time = time.ticks_ms() - start_time

        return True, 2, [center1, center2], avg_center, processing_time

    except Exception as e:
        print(f"快速矩形检测错误: {e}")
        processing_time = time.ticks_ms() - start_time
        # ==================== Step10新增：绘制屏幕中心点（即使出错也显示） ====================
        draw_screen_center_marker(img)
        draw_coordinate_display(img, None)
        draw_visual_feedback_info(img)
        return False, 0, [], None, processing_time

# Step8核心：多帧稳定性检测
def update_fast_multi_frame_buffer(detection_result, rect_centers, rect_total_center):
    """更新快速多帧缓冲区 - Step8核心算法"""
    global fast_multi_frame_detection

    frame_data = {
        'success': detection_result,
        'centers': rect_centers,
        'total_center': rect_total_center,
        'timestamp': time.ticks_ms()
    }

    fast_multi_frame_detection['frame_buffer'].append(frame_data)

    # 保持缓冲区大小
    if len(fast_multi_frame_detection['frame_buffer']) > fast_multi_frame_detection['buffer_size']:
        fast_multi_frame_detection['frame_buffer'].pop(0)

def get_fast_stable_result():
    """获取快速稳定检测结果 - Step10运动优化算法"""
    global fast_multi_frame_detection

    buffer = fast_multi_frame_detection['frame_buffer']
    if len(buffer) < fast_multi_frame_detection['stable_threshold']:
        return None, 0.0

    # 统计成功的检测
    successful_detections = [frame for frame in buffer if frame['success']]

    if len(successful_detections) < fast_multi_frame_detection['stable_threshold']:
        return None, 0.0

    # 获取运动信息
    motion_info = get_enhanced_motion_info()
    motion_state = motion_info['state']

    # 根据运动状态调整计算策略
    if motion_state == 0:  # 静止状态 - 使用更多历史数据
        recent_centers = [det['total_center'] for det in successful_detections[-3:] if det['total_center']]
        position_weight = fast_multi_frame_detection['position_weight']
        velocity_weight = fast_multi_frame_detection['velocity_weight']
    else:  # 运动状态 - 更重视最近的数据
        recent_centers = [det['total_center'] for det in successful_detections[-2:] if det['total_center']]
        position_weight = 0.8  # 增加位置权重
        velocity_weight = 0.2  # 减少速度权重

    if recent_centers:
        # 基础平均中心点
        avg_x = sum(center[0] for center in recent_centers) / len(recent_centers)
        avg_y = sum(center[1] for center in recent_centers) / len(recent_centers)

        # 运动预测修正
        predicted_pos, pred_confidence = get_predicted_position()
        if predicted_pos and pred_confidence > 0.4 and motion_state > 0:
            pred_x, pred_y = predicted_pos
            # 融合预测位置和检测位置
            final_x = avg_x * position_weight + pred_x * velocity_weight
            final_y = avg_y * position_weight + pred_y * velocity_weight
        else:
            final_x, final_y = avg_x, avg_y

        # 计算增强的置信度
        detection_confidence = len(successful_detections) / len(buffer)

        # 时间一致性检查
        temporal_consistency = fast_multi_frame_detection['temporal_consistency']
        if len(successful_detections) >= 2:
            recent_positions = [det['total_center'] for det in successful_detections[-2:]]
            if len(recent_positions) == 2:
                pos_diff = math.sqrt((recent_positions[1][0] - recent_positions[0][0])**2 +
                                   (recent_positions[1][1] - recent_positions[0][1])**2)
                # 根据位置变化调整置信度
                if motion_state == 0:  # 静止状态，位置变化应该很小
                    consistency_factor = max(0.5, 1.0 - pos_diff / 20.0)
                else:  # 运动状态，允许更大的位置变化
                    consistency_factor = max(0.7, 1.0 - pos_diff / 50.0)

                detection_confidence *= consistency_factor

        # 运动适应性置信度调整
        if fast_multi_frame_detection['motion_adaptive_threshold']:
            if motion_state > 0:
                # 运动状态下，适当降低置信度要求但增加稳定性权重
                motion_confidence_factor = max(0.8, 1.0 - motion_info['speed'] / 50.0)
                detection_confidence *= motion_confidence_factor

        final_confidence = detection_confidence * temporal_consistency

        return (final_x, final_y), final_confidence

    return None, 0.0

def should_send_fast_data(center, confidence):
    """判断是否应该发送数据 - Step10严格过滤版本"""
    global fast_multi_frame_detection

    # 严格的数据过滤条件
    if center is None:
        return False

    # 置信度检查 - 确保检测结果可靠
    if confidence < fast_multi_frame_detection['confidence_threshold']:
        return False

    # 坐标有效性检查 - 确保坐标在合理范围内
    center_x, center_y = center
    if (center_x < 0 or center_x >= picture_width or
        center_y < 0 or center_y >= picture_height):
        return False

    # 位置变化检查 - 避免发送重复数据
    last_center = fast_multi_frame_detection['last_send_center']
    if last_center is None:
        return True

    # 检查位置变化是否足够大
    distance = math.sqrt((center_x - last_center[0])**2 + (center_y - last_center[1])**2)
    return distance >= fast_multi_frame_detection['send_threshold']

def validate_detection_quality(rect_success, stable_center, stable_confidence):
    """验证检测质量 - Step10新增严格验证函数"""
    """
    返回值：
    - True: 检测质量满足发送要求
    - False: 检测质量不满足发送要求
    """

    # 条件1：矩形检测必须成功
    if not rect_success:
        return False, "矩形检测失败"

    # 条件2：必须有稳定的中心点
    if stable_center is None:
        return False, "无稳定中心点"

    # 条件3：置信度必须满足要求
    if stable_confidence < fast_multi_frame_detection['confidence_threshold']:
        return False, f"置信度不足({stable_confidence:.3f} < {fast_multi_frame_detection['confidence_threshold']})"

    # 条件4：中心点坐标必须在有效范围内
    center_x, center_y = stable_center
    if (center_x < 0 or center_x >= picture_width or
        center_y < 0 or center_y >= picture_height):
        return False, f"坐标超出范围({center_x:.1f}, {center_y:.1f})"

    # 条件5：检查数据变化是否足够（避免重复发送）
    if not should_send_fast_data(stable_center, stable_confidence):
        return False, f"位置变化不足或重复数据"

    # 所有条件都满足
    return True, "检测质量合格"

# ==================== Step10新增：串口通信状态监控 ====================
serial_communication_stats = {
    'total_attempts': 0,         # 总尝试发送次数
    'successful_sends': 0,       # 成功发送次数
    'failed_quality_checks': 0,  # 质量检查失败次数
    'failed_serial_sends': 0,    # 串口发送失败次数
    'last_send_time': 0,         # 最后发送时间
    'send_rate': 0.0,           # 发送频率
    'quality_failure_reasons': {}  # 质量失败原因统计
}

def update_serial_communication_stats(attempted, quality_ok, quality_reason, send_success=None):
    """更新串口通信统计 - Step10新增功能"""
    global serial_communication_stats

    if attempted:
        serial_communication_stats['total_attempts'] += 1

        if quality_ok:
            if send_success:
                serial_communication_stats['successful_sends'] += 1
                serial_communication_stats['last_send_time'] = time.ticks_ms()
            else:
                serial_communication_stats['failed_serial_sends'] += 1
        else:
            serial_communication_stats['failed_quality_checks'] += 1
            # 统计失败原因
            if quality_reason not in serial_communication_stats['quality_failure_reasons']:
                serial_communication_stats['quality_failure_reasons'][quality_reason] = 0
            serial_communication_stats['quality_failure_reasons'][quality_reason] += 1

    # 计算发送频率
    if serial_communication_stats['total_attempts'] > 0:
        serial_communication_stats['send_rate'] = (
            serial_communication_stats['successful_sends'] /
            serial_communication_stats['total_attempts'] * 100
        )

def get_serial_communication_status():
    """获取串口通信状态 - Step10新增功能"""
    global serial_communication_stats

    total = serial_communication_stats['total_attempts']
    success = serial_communication_stats['successful_sends']
    quality_fail = serial_communication_stats['failed_quality_checks']
    serial_fail = serial_communication_stats['failed_serial_sends']

    return {
        'total_attempts': total,
        'successful_sends': success,
        'success_rate': serial_communication_stats['send_rate'],
        'quality_failures': quality_fail,
        'serial_failures': serial_fail,
        'status': "正常" if serial_communication_stats['send_rate'] > 70 else "异常",
        'last_send_ago': time.ticks_ms() - serial_communication_stats['last_send_time'] if serial_communication_stats['last_send_time'] > 0 else -1
    }

# Step8核心：串口通信功能（修改为STM32兼容格式）
def init_uart():
    """初始化串口通信 - Step8核心功能"""
    global uart
    try:
        fpioa = FPIOA()
        fpioa.set_function(TX_PIN, FPIOA.UART2_TXD)
        fpioa.set_function(RX_PIN, FPIOA.UART2_RXD)
        uart = UART(SERIAL_PORT, BAUD_RATE)
        print(f"✅ 串口初始化成功: {BAUD_RATE}bps")
        return True
    except Exception as e:
        print(f"❌ 串口初始化失败: {e}")
        return False

def send_fast_coordinates(rect_center, screen_center):
    """发送快速坐标数据 - 修改为与STM32端完全一致的数据包格式"""
    global uart

    if uart is None:
        return False

    try:
        packet = []
        packet.extend(PACKET_HEADER)  # 0xAA, 0x55

        if rect_center is not None:
            rect_x, rect_y = int(rect_center[0]), int(rect_center[1])
        else:
            rect_x, rect_y = 0xFFFF, 0xFFFF

        screen_x, screen_y = int(screen_center[0]), int(screen_center[1])

        # 按照STM32端期望的数据包结构组装数据
        # 数据包格式：header[2] + rect_x[2] + rect_y[2] + screen_x[2] + screen_y[2] + checksum[1] + tail[2]
        packet.extend([(rect_x >> 8) & 0xFF, rect_x & 0xFF])        # 矩形中心X坐标（大端序）
        packet.extend([(rect_y >> 8) & 0xFF, rect_y & 0xFF])        # 矩形中心Y坐标（大端序）
        packet.extend([(screen_x >> 8) & 0xFF, screen_x & 0xFF])    # 屏幕中心X坐标（大端序）
        packet.extend([(screen_y >> 8) & 0xFF, screen_y & 0xFF])    # 屏幕中心Y坐标（大端序）

        # 计算校验和（从rect_x开始到screen_y结束的8个字节）
        checksum = sum(packet[2:]) % 256
        packet.append(checksum)
        packet.extend(PACKET_TAIL)  # 0x0D, 0x0A

        uart.write(bytes(packet))
        return True

    except Exception as e:
        print(f"❌ 串口发送失败: {e}")
        return False

# Step8核心：统计系统
def update_detection_stats(success):
    """更新检测统计 - Step8核心功能"""
    global rect_detection_stats

    rect_detection_stats['total_attempts'] += 1
    if success:
        rect_detection_stats['successful_detections'] += 1

    if rect_detection_stats['total_attempts'] > 0:
        rect_detection_stats['success_rate'] = (
            rect_detection_stats['successful_detections'] /
            rect_detection_stats['total_attempts'] * 100
        )

    rect_detection_stats['recent_attempts'].append(success)
    if len(rect_detection_stats['recent_attempts']) > 50:
        rect_detection_stats['recent_attempts'].pop(0)

    if len(rect_detection_stats['recent_attempts']) > 0:
        recent_successes = sum(rect_detection_stats['recent_attempts'])
        rect_detection_stats['recent_success_rate'] = (
            recent_successes / len(rect_detection_stats['recent_attempts']) * 100
        )

def update_performance_stats(fps, processing_time):
    """更新性能统计 - Step8核心功能"""
    global rect_detection_stats

    rect_detection_stats['fps_history'].append(fps)
    if len(rect_detection_stats['fps_history']) > 20:
        rect_detection_stats['fps_history'].pop(0)

    rect_detection_stats['processing_times'].append(processing_time)
    if len(rect_detection_stats['processing_times']) > 20:
        rect_detection_stats['processing_times'].pop(0)

    if rect_detection_stats['fps_history']:
        rect_detection_stats['avg_fps'] = sum(rect_detection_stats['fps_history']) / len(rect_detection_stats['fps_history'])

    if rect_detection_stats['processing_times']:
        rect_detection_stats['avg_processing_time'] = sum(rect_detection_stats['processing_times']) / len(rect_detection_stats['processing_times'])

def get_performance_status():
    """获取性能状态 - Step8核心功能"""
    global rect_detection_stats

    return {
        'fps': rect_detection_stats['avg_fps'],
        'fps_status': "优秀" if rect_detection_stats['avg_fps'] >= TARGET_FPS else "需优化",
        'processing_time': rect_detection_stats['avg_processing_time'],
        'time_status': "优秀" if rect_detection_stats['avg_processing_time'] <= TARGET_LATENCY else "需优化",
        'success_rate': rect_detection_stats['success_rate'],
        'rate_status': "优秀" if rect_detection_stats['success_rate'] >= TARGET_SUCCESS_RATE else "需优化"
    }

def get_detection_stats_text():
    """获取检测统计文本 - Step8核心功能"""
    global rect_detection_stats

    success_rate = rect_detection_stats['success_rate']
    recent_rate = rect_detection_stats['recent_success_rate']
    total_attempts = rect_detection_stats['total_attempts']

    return {
        'total_text': f"总计: {total_attempts}次",
        'success_rate_text': f"成功率: {success_rate:.1f}%",
        'recent_text': f"近50次: {recent_rate:.1f}%"
    }

# ==================== Step7核心：LAB阈值编辑界面（完整移植Step9） ====================
def show_threshold_edit_interface(img):
    """显示LAB阈值编辑界面 - 完整移植Step9的成熟设计"""
    global threshold_edit_mode, threshold_current, sensor

    # 清空背景
    img.draw_rectangle(0, 0, picture_width, picture_height,
                     color=(40, 40, 80), thickness=1, fill=True)

    if threshold_edit_mode == "rect":
        try:
            # ==================== Step7核心：全屏阈值预览 + 画中画彩色原图 ====================
            preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)

            # 1. 全屏LAB二值化预览作为背景界面
            preview_binary = preview_img.binary([tuple(threshold_current)])
            preview_fullscreen = preview_binary.to_rgb565()

            # 缩放二值化预览图到全屏400x240
            if preview_fullscreen.width() != picture_width or preview_fullscreen.height() != picture_height:
                # 计算缩放比例，保持宽高比
                scale_x = preview_fullscreen.width() / picture_width
                scale_y = preview_fullscreen.height() / picture_height
                scale = max(scale_x, scale_y)

                if scale > 1:
                    # 需要缩小
                    scale_factor = int(scale) + 1
                    preview_fullscreen.midpoint_pool(scale_factor, scale_factor)

                # 如果还是不匹配，进行裁剪或填充
                if preview_fullscreen.width() > picture_width or preview_fullscreen.height() > picture_height:
                    # 居中裁剪
                    crop_x = max(0, (preview_fullscreen.width() - picture_width) // 2)
                    crop_y = max(0, (preview_fullscreen.height() - picture_height) // 2)
                    crop_w = min(picture_width, preview_fullscreen.width())
                    crop_h = min(picture_height, preview_fullscreen.height())
                    preview_fullscreen = preview_fullscreen.copy(roi=(crop_x, crop_y, crop_w, crop_h))

            # 将全屏二值化预览作为背景，直接替换img的内容
            img.draw_image(preview_fullscreen, 0, 0)

            # 2. 叠加小尺寸彩色原图窗口（画中画效果）
            color_original = preview_img.copy()  # 保留彩色原图

            # 设置小窗口尺寸为100x75像素
            target_width = 100
            target_height = 75

            # 计算缩放比例
            if color_original.width() > target_width or color_original.height() > target_height:
                scale_x = color_original.width() // target_width + 1
                scale_y = color_original.height() // target_height + 1
                scale = max(scale_x, scale_y)
                color_original.midpoint_pool(scale, scale)

            # 如果缩放后仍然过大，进行裁剪
            if color_original.width() > target_width or color_original.height() > target_height:
                crop_x = max(0, (color_original.width() - target_width) // 2)
                crop_y = max(0, (color_original.height() - target_height) // 2)
                crop_w = min(target_width, color_original.width())
                crop_h = min(target_height, color_original.height())
                color_original = color_original.copy(roi=(crop_x, crop_y, crop_w, crop_h))

            # 放置在右上角，留出边距
            pip_x = picture_width - color_original.width() - 5
            pip_y = 5

            # 绘制小窗口边框（画中画效果）
            border_thickness = 2
            img.draw_rectangle(pip_x - border_thickness, pip_y - border_thickness,
                             color_original.width() + 2 * border_thickness,
                             color_original.height() + 2 * border_thickness,
                             color=(255, 255, 255), thickness=border_thickness, fill=True)

            # 叠加彩色原图
            img.draw_image(color_original, pip_x, pip_y)

            # 3. 叠加界面信息（半透明背景）
            # 标题区域背景
            img.draw_rectangle(0, 0, picture_width, 25, color=(0, 0, 0), thickness=1, fill=True)
            img.draw_string_advanced(5, 5, 16, "矩形LAB阈值编辑 - 全屏预览", color=(255, 255, 0))

            # 参数信息区域背景
            info_text = f"LAB: ({threshold_current[0]},{threshold_current[1]},{threshold_current[2]},{threshold_current[3]},{threshold_current[4]},{threshold_current[5]})"
            img.draw_rectangle(0, picture_height - 20, picture_width, 20, color=(0, 0, 0), thickness=1, fill=True)
            img.draw_string_advanced(5, picture_height - 18, 12, info_text, color=(255, 255, 255))

            # 画中画标签
            img.draw_string_advanced(pip_x, pip_y + color_original.height() + 3, 8, "彩色原图", color=(255, 255, 255))

        except Exception as e:
            print(f"矩形阈值预览错误: {e}")
            # 错误时显示简单界面
            img.draw_rectangle(0, 0, picture_width, picture_height, color=(40, 40, 80), thickness=1, fill=True)
            img.draw_string_advanced(60, 10, 18, "矩形LAB阈值编辑", color=(255, 255, 0))
            img.draw_string_advanced(10, 35, 12, f"LAB: ({threshold_current[0]},{threshold_current[1]},{threshold_current[2]},{threshold_current[3]},{threshold_current[4]},{threshold_current[5]})", color=(255, 255, 255))

    elif threshold_edit_mode == "laser":
        try:
            # 激光点LAB阈值预览 - 保持原有布局（激光点检测效果更适合彩色显示）
            preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)
            current_lab_threshold = [tuple(threshold_current)]
            blobs = preview_img.find_blobs(current_lab_threshold, False,
                                         x_stride=1, y_stride=1,
                                         pixels_threshold=MIN_LASER_PIXELS, margin=False)
            for blob in blobs:
                preview_img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                                         color=(0, 255, 255), thickness=2, fill=False)
                preview_img.draw_circle(blob.x() + blob.w()//2, blob.y() + blob.h()//2, 3,
                                       color=(255, 0, 255), thickness=2)
            if preview_img.width() > 150:
                scale = preview_img.width() // 150 + 1
                preview_img.midpoint_pool(scale, scale)
            img.draw_image(preview_img, picture_width - preview_img.width() - 10, 10)
            img.draw_string_advanced(60, 10, 18, "激光点LAB阈值编辑", color=(255, 255, 0))
            img.draw_string_advanced(10, 35, 12, f"LAB: ({threshold_current[0]},{threshold_current[1]},{threshold_current[2]},{threshold_current[3]},{threshold_current[4]},{threshold_current[5]})", color=(255, 255, 255))
        except Exception as e:
            print(f"激光点阈值预览错误: {e}")
            img.draw_string_advanced(60, 10, 18, "激光点LAB阈值编辑", color=(255, 255, 0))

    # 显示编辑模式和统计信息
    img.draw_string_advanced(120, 200, 14, "编辑模式: " + threshold_edit_mode.upper() + " (LAB)", color=(0, 255, 255))

    # 显示字典状态
    rect_count = len(threshold_dict['rect']) if 'rect' in threshold_dict else 0
    laser_count = len(threshold_dict['laser']) if 'laser' in threshold_dict else 0
    img.draw_string_advanced(100, 180, 12, f"LAB字典: 矩形{rect_count}个 激光点{laser_count}个", color=(0, 255, 0))

    # 绘制边框
    img.draw_rectangle(2, 2, picture_width-4, picture_height-4,
                     color=(255, 255, 0), thickness=2, fill=False)

def draw_threshold_edit_buttons(img):
    """绘制LAB阈值编辑按钮 - 完整移植Step9的成熟设计"""
    global threshold_edit_mode, threshold_current

    button_color = (150, 150, 150)
    text_color = (0, 0, 0)
    active_color = (0, 255, 0)

    # ==================== Step7核心：顶部控制按钮 ====================
    # 返回按钮
    img.draw_rectangle(20, 20, 120, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(50, 30, 20, "返回", color=text_color)

    # 切换按钮
    switch_color = active_color if threshold_edit_mode == "laser" else button_color
    img.draw_rectangle(660, 20, 120, 40, color=switch_color, thickness=2, fill=True)
    img.draw_string_advanced(690, 30, 20, "切换", color=text_color)

    # ==================== Step7核心：底部控制按钮 ====================
    # 重置按钮
    img.draw_rectangle(20, 420, 100, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(45, 430, 18, "重置", color=text_color)

    # 保存按钮
    img.draw_rectangle(140, 420, 100, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(165, 430, 18, "保存", color=text_color)

    # 保存到字典按钮
    img.draw_rectangle(540, 420, 120, 40, color=(100, 200, 100), thickness=2, fill=True)
    img.draw_string_advanced(555, 430, 16, "保存到字典", color=text_color)

    # 从字典加载按钮
    img.draw_rectangle(680, 420, 120, 40, color=(100, 100, 200), thickness=2, fill=True)
    img.draw_string_advanced(695, 430, 16, "从字典加载", color=text_color)

    # ==================== Step7核心：统一的6参数LAB编辑界面 ====================
    # 矩形和激光点都使用相同的6参数LAB编辑界面
    param_names = ["L_min", "L_max", "A_min", "A_max", "B_min", "B_max"]
    for i in range(6):
        y_pos = 80 + i * 55

        # 减少按钮 (左侧)
        img.draw_rectangle(20, y_pos, 60, 35, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(40, y_pos + 8, 16, "-", color=text_color)

        # 参数显示
        param_value = threshold_current[i] if i < len(threshold_current) else 0
        img.draw_string_advanced(100, y_pos + 8, 14, f"{param_names[i]}: {param_value}", color=(255, 255, 255))

        # 增加按钮 (右侧)
        img.draw_rectangle(720, y_pos, 60, 35, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(740, y_pos + 8, 16, "+", color=text_color)

def check_threshold_edit_click(x, y):
    """检测LAB阈值编辑界面的按钮点击 - 完整移植Step9的成熟设计"""
    global threshold_edit_mode, threshold_current, current_mode, RECT_THRESHOLD, LASER_THRESHOLD

    # ==================== Step7核心：顶部控制按钮 ====================
    # 返回按钮
    if 20 <= x <= 140 and 20 <= y <= 60:
        print("点击返回按钮")
        current_mode = 1
        return True

    # 切换按钮
    if 660 <= x <= 780 and 20 <= y <= 60:
        print("点击切换按钮")
        if threshold_edit_mode == "rect":
            threshold_edit_mode = "laser"
            threshold_current = list(LASER_THRESHOLD[0])  # 加载激光点LAB阈值
        else:
            threshold_edit_mode = "rect"
            threshold_current = list(RECT_THRESHOLD[0])   # 加载矩形LAB阈值
        print(f"切换到 {threshold_edit_mode} 模式，LAB阈值: {threshold_current}")
        return True

    # ==================== Step7核心：底部控制按钮 ====================
    # 重置按钮
    if 20 <= x <= 120 and 420 <= y <= 460:
        print("✅ 点击重置按钮")
        if threshold_edit_mode == "rect":
            threshold_current = [0, 100, -20, 20, -20, 20]  # 黑色矩形默认LAB阈值
            print("🔄 重置矩形LAB阈值为默认值")
        else:
            threshold_current = [47, 80, 9, 91, -55, 63]    # 激光点默认LAB阈值
            print("🔄 重置激光点LAB阈值为默认值")
        return True

    # 保存按钮
    if 140 <= x <= 240 and 420 <= y <= 460:
        print("✅ 点击保存按钮")
        if threshold_edit_mode == "rect":
            RECT_THRESHOLD[0] = tuple(threshold_current)
            print(f"💾 保存矩形LAB阈值到内存: {RECT_THRESHOLD[0]}")
        else:
            LASER_THRESHOLD[0] = tuple(threshold_current)
            print(f"💾 保存激光点LAB阈值到内存: {LASER_THRESHOLD[0]}")
        return True

    # 保存到字典按钮
    if 540 <= x <= 660 and 420 <= y <= 460:
        print("✅ 点击保存到字典按钮")
        if save_threshold_to_dict():
            print("📁 LAB阈值已成功保存到字典")
        else:
            print("❌ 保存到字典失败")
        return True

    # 从字典加载按钮
    if 680 <= x <= 800 and 420 <= y <= 460:
        print("✅ 点击从字典加载按钮")
        if load_threshold_from_dict():
            print("📁 LAB阈值已从字典加载")
        else:
            print("❌ 从字典加载失败")
        return True

    # ==================== Step7核心：统一的6参数调整逻辑 ====================
    # 矩形和激光点都使用相同的6参数调整逻辑
    for i in range(6):
        y_pos = 80 + i * 55
        # 减少按钮
        if 20 <= x <= 80 and y_pos <= y <= y_pos + 35:
            if i < 2:  # L参数 0-100
                threshold_current[i] = max(0, threshold_current[i] - 2)
            else:  # A,B参数 -128到127
                threshold_current[i] = max(-128, threshold_current[i] - 2)
            print(f"减少参数{i} ({['L_min','L_max','A_min','A_max','B_min','B_max'][i]}): {threshold_current[i]}")
            return True
        # 增加按钮
        if 720 <= x <= 780 and y_pos <= y <= y_pos + 35:
            if i < 2:  # L参数 0-100
                threshold_current[i] = min(100, threshold_current[i] + 2)
            else:  # A,B参数 -128到127
                threshold_current[i] = min(127, threshold_current[i] + 2)
            print(f"增加参数{i} ({['L_min','L_max','A_min','A_max','B_min','B_max'][i]}): {threshold_current[i]}")
            return True

    return False

# ==================== Step7核心：字典管理功能 ====================
def save_threshold_to_dict():
    """保存LAB阈值到字典 - Step7成熟功能"""
    global threshold_edit_mode, threshold_current, threshold_dict
    try:
        if threshold_edit_mode == "rect":
            if tuple(threshold_current) not in threshold_dict['rect']:
                threshold_dict['rect'].append(tuple(threshold_current))
                print(f"📁 矩形LAB阈值已保存到字典: {threshold_current}")
                return True
            else:
                print("⚠️ 该矩形LAB阈值已存在于字典中")
                return False
        else:
            if tuple(threshold_current) not in threshold_dict['laser']:
                threshold_dict['laser'].append(tuple(threshold_current))
                print(f"📁 激光点LAB阈值已保存到字典: {threshold_current}")
                return True
            else:
                print("⚠️ 该激光点LAB阈值已存在于字典中")
                return False
    except Exception as e:
        print(f"❌ 保存到字典失败: {e}")
        return False

def load_threshold_from_dict():
    """从字典加载LAB阈值 - Step7成熟功能"""
    global threshold_edit_mode, threshold_current, threshold_dict
    try:
        if threshold_edit_mode == "rect":
            if threshold_dict['rect']:
                threshold_current = list(threshold_dict['rect'][-1])  # 加载最后一个
                print(f"📁 从字典加载矩形LAB阈值: {threshold_current}")
                return True
            else:
                print("⚠️ 矩形LAB阈值字典为空")
                return False
        else:
            if threshold_dict['laser']:
                threshold_current = list(threshold_dict['laser'][-1])  # 加载最后一个
                print(f"📁 从字典加载激光点LAB阈值: {threshold_current}")
                return True
            else:
                print("⚠️ 激光点LAB阈值字典为空")
                return False
    except Exception as e:
        print(f"❌ 从字典加载失败: {e}")
        return False

# 主界面按钮功能（继承Step9）
def draw_main_buttons(img):
    """绘制主界面按钮 - 支持四个按钮布局"""
    global current_mode

    button_colors = [
        BUTTON_ACTIVE_COLOR if current_mode == 1 else BUTTON_INACTIVE_COLOR,
        BUTTON_ACTIVE_COLOR if current_mode == 2 else BUTTON_INACTIVE_COLOR,
        BUTTON_ACTIVE_COLOR if current_mode == 3 else BUTTON_INACTIVE_COLOR,
        BUTTON_ACTIVE_COLOR if current_mode == 4 else BUTTON_INACTIVE_COLOR  # 第四个按钮
    ]

    button_texts = ["中心连线", "性能监控", "阈值编辑", "高性能识别"]  # Step10修改第一个按钮名称

    for i, (pos, color, text) in enumerate(zip([BUTTON1_POS, BUTTON2_POS, BUTTON3_POS, BUTTON4_POS],
                                              button_colors, button_texts)):
        # 为高性能识别按钮使用特殊颜色
        if i == 3:  # 第四个按钮（高性能识别）
            special_color = (200, 100, 200) if current_mode == 4 else (120, 60, 120)
            img.draw_rectangle(pos[0], pos[1], BUTTON_WIDTH, BUTTON_HEIGHT,
                              color=special_color, thickness=2, fill=True)
        else:
            img.draw_rectangle(pos[0], pos[1], BUTTON_WIDTH, BUTTON_HEIGHT,
                              color=color, thickness=2, fill=True)

        # 调整文字位置以适应不同长度的按钮文本
        text_offset_x = 15 if len(text) > 4 else 30
        img.draw_string_advanced(pos[0] + text_offset_x, pos[1] + 20, 18, text, color=BUTTON_TEXT_COLOR)

def check_main_button_click(x, y):
    """检测主界面按钮点击 - 支持四个按钮"""
    if (BUTTON1_POS[0] <= x <= BUTTON1_POS[0] + BUTTON_WIDTH and
        BUTTON1_POS[1] <= y <= BUTTON1_POS[1] + BUTTON_HEIGHT):
        return 1
    if (BUTTON2_POS[0] <= x <= BUTTON2_POS[0] + BUTTON_WIDTH and
        BUTTON2_POS[1] <= y <= BUTTON2_POS[1] + BUTTON_HEIGHT):
        return 2
    if (BUTTON3_POS[0] <= x <= BUTTON3_POS[0] + BUTTON_WIDTH and
        BUTTON3_POS[1] <= y <= BUTTON3_POS[1] + BUTTON_HEIGHT):
        return 3
    if (BUTTON4_POS[0] <= x <= BUTTON4_POS[0] + BUTTON_WIDTH and
        BUTTON4_POS[1] <= y <= BUTTON4_POS[1] + BUTTON_HEIGHT):
        return 4  # 第四个按钮（高性能识别）
    return None

# ==================== 主程序 ====================
try:
    # 初始化系统
    print("🚀 Step10中心点连线显示系统启动中...")
    print("🔧 Step10核心功能:")
    print("   可视化显示: 屏幕中心点标记、中心点连线、坐标显示")
    print("   检测算法: Step8动态快速矩形识别（已验证高正确率）")
    print("   阈值编辑: Step7成熟LAB阈值界面（已验证稳定性）")
    print("   性能监控: Step8实时性能监控系统")
    print("   串口通信: Step8高频通信机制（STM32兼容格式）")
    print("🚀 性能目标：")
    print(f"   帧率: ≥{TARGET_FPS}FPS")
    print(f"   识别率: ≥{TARGET_SUCCESS_RATE}%")
    print(f"   延迟: ≤{TARGET_LATENCY}ms")

    # 初始化串口
    if not init_uart():
        print("⚠️  串口初始化失败，程序继续运行但无通信功能")

    # 传感器初始化（修复：添加chn参数）
    sensor = Sensor(id=sensor_id)
    sensor.reset()
    sensor.set_framesize(width=picture_width, height=picture_height, chn=CAM_CHN_ID_0)
    sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)

    # 显示器初始化
    Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
    MediaManager.init()
    sensor.run()

    # 触摸屏初始化（修复：添加参数0）
    tp = TOUCH(0)
    clock = time.clock()

    print("🎉 Step10系统初始化完成，开始主循环...")
    print("📱 界面模式: 1=中心连线 2=性能监控 3=阈值编辑 4=高性能识别")
    print("🎯 Step10新增功能: 屏幕中心点标记、中心点连线、坐标显示")

    # 主循环
    while True:
        clock.tick()
        os.exitpoint()

        try:
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            frame_count += 1
            frame_start_time = time.ticks_ms()

            # 处理触摸输入
            points = tp.read()
            if len(points) > 0:
                touch_x, touch_y = points[0].x, points[0].y

                if current_mode == 3:
                    # 阈值编辑模式 - 完整移植Step9功能
                    threshold_clicked = check_threshold_edit_click(touch_x, touch_y)
                    if threshold_clicked:
                        time.sleep_ms(200)
                else:
                    # 主界面模式
                    clicked_button = check_main_button_click(touch_x, touch_y)
                    if clicked_button:
                        current_mode = clicked_button
                        print(f"🔄 界面切换到模式: {current_mode}")
                        time.sleep_ms(200)

            # ==================== 核心功能处理 ====================
            if current_mode == 1:
                # 中心连线界面 - Step10核心功能
                img.draw_string_advanced(10, 10, 20, "K230中心点连线系统", color=(255, 255, 0))
                img.draw_string_advanced(10, 30, 16, "Step10: 可视化连线显示", color=(0, 255, 255))

                # 显示系统状态
                img.draw_string_advanced(10, 50, 12, f"帧数: {frame_count}", color=(255, 255, 255))
                img.draw_string_advanced(10, 65, 12, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))

                # 串口状态
                uart_status = "✅连接" if uart else "❌断开"
                img.draw_string_advanced(300, 10, 12, f"串口: {uart_status}", color=(0, 255, 0))

                # ==================== Step10方案B：单帧立即检测和发送 ====================
                rect_success, rect_count, rect_centers, rect_total_center, processing_time = detect_rectangles_fast(img)

                # 更新统计
                update_detection_stats(rect_success)
                update_performance_stats(clock.fps(), processing_time)

                # ==================== 方案B：单帧检测成功后立即发送数据 ====================
                current_time = time.ticks_ms()
                if current_time - last_send_time >= SEND_INTERVAL:
                    if rect_success and rect_total_center is not None:
                        # 单帧检测成功，立即发送矩形中心点数据
                        # 简化的质量验证：只检查基本条件
                        center_x, center_y = rect_total_center

                        # 基本坐标有效性检查
                        if (0 <= center_x < picture_width and 0 <= center_y < picture_height):
                            # 立即发送真实检测到的矩形中心点数据（STM32兼容格式）
                            send_success = send_fast_coordinates(rect_total_center, (SCREEN_CENTER_X, SCREEN_CENTER_Y))

                            # 更新统计信息
                            update_serial_communication_stats(True, True, "单帧检测成功", send_success)

                            if send_success:
                                # 详细的发送日志
                                if frame_count % 50 == 0:
                                    motion_info = get_enhanced_motion_info()
                                    serial_stats = get_serial_communication_status()
                                    print(f"📡 单帧立即发送数据:")
                                    print(f"   中心点: ({rect_total_center[0]:.1f}, {rect_total_center[1]:.1f})")
                                    print(f"   检测帧数: {rect_count}")
                                    print(f"   运动状态: {['静止','慢速','中速','快速','极快'][min(motion_info['state'], 4)]}")
                                    print(f"   发送成功率: {serial_stats['success_rate']:.1f}%")
                                    print(f"   响应模式: 单帧立即发送（最低延迟）")
                            else:
                                if frame_count % 100 == 0:
                                    print("❌ 串口发送失败 - 检查串口连接")
                        else:
                            # 坐标超出范围
                            update_serial_communication_stats(True, False, f"坐标超出范围({center_x:.1f}, {center_y:.1f})", None)
                            if frame_count % 100 == 0:
                                print(f"🚫 坐标超出范围 - X:{center_x:.1f}, Y:{center_y:.1f}")

                        last_send_time = current_time
                    else:
                        # 单帧检测失败时不发送任何数据
                        update_serial_communication_stats(True, False, "单帧检测失败", None)

                        if frame_count % 100 == 0:
                            serial_stats = get_serial_communication_status()
                            print(f"🚫 单帧检测失败 - 未发送数据")
                            print(f"   检测状态: 成功={rect_success}, 中心点={'有效' if rect_total_center else '无效'}")
                            print(f"   发送成功率: {serial_stats['success_rate']:.1f}%")
                        last_send_time = current_time

            elif current_mode == 2:
                # 性能监控界面 - 简化版本
                img.draw_string_advanced(10, 10, 20, "性能监控面板", color=(255, 255, 0))

                perf = get_performance_status()

                # 基础性能指标
                fps_color = (0, 255, 0) if perf['fps_status'] == "优秀" else (255, 0, 0)
                img.draw_string_advanced(10, 40, 16, f"当前FPS: {perf['fps']:.1f}", color=fps_color)

                time_color = (0, 255, 0) if perf['time_status'] == "优秀" else (255, 0, 0)
                img.draw_string_advanced(10, 60, 16, f"处理时间: {perf['processing_time']:.1f}ms", color=time_color)

                rate_color = (0, 255, 0) if perf['rate_status'] == "优秀" else (255, 0, 0)
                img.draw_string_advanced(10, 80, 16, f"识别成功率: {perf['success_rate']:.1f}%", color=rate_color)

                # 系统状态
                img.draw_string_advanced(10, 110, 14, f"总帧数: {frame_count}", color=(255, 255, 255))
                img.draw_string_advanced(10, 130, 14, f"成功检测: {rect_detection_stats['successful_detections']}", color=(0, 255, 0))

                # ROI效率
                roi_x, roi_y, roi_width, roi_height = get_current_roi()
                roi_efficiency = (roi_width * roi_height) / (picture_width * picture_height) * 100
                img.draw_string_advanced(10, 150, 14, f"ROI效率: {roi_efficiency:.1f}%", color=(0, 255, 255))

                # 运动状态
                motion_info = get_enhanced_motion_info()
                motion_states = ["静止", "慢速", "中速", "快速", "极快"]
                motion_text = motion_states[min(motion_info['state'], 4)]
                motion_colors = [(0, 255, 0), (255, 255, 0), (255, 165, 0), (255, 0, 0), (255, 0, 255)]
                motion_color = motion_colors[min(motion_info['state'], 4)]
                img.draw_string_advanced(10, 170, 14, f"运动状态: {motion_text}", color=motion_color)

                # 简化的串口状态
                serial_stats = get_serial_communication_status()
                serial_color = (0, 255, 0) if serial_stats['status'] == "正常" else (255, 0, 0)
                img.draw_string_advanced(10, 190, 14, f"串口状态: {serial_stats['status']}", color=serial_color)

                # 方案B：单帧检测模式说明
                img.draw_string_advanced(10, 210, 12, "检测模式: 单帧立即发送", color=(255, 255, 0))
                img.draw_string_advanced(10, 225, 12, "响应延迟: 最低（~30ms）", color=(0, 255, 0))

            elif current_mode == 3:
                # 阈值编辑界面 - 完整移植Step9功能
                show_threshold_edit_interface(img)

            elif current_mode == 4:
                # 高性能识别界面 - 预留功能
                img.draw_string_advanced(10, 10, 20, "高性能识别系统", color=(255, 255, 0))
                img.draw_string_advanced(10, 30, 16, "Step10: 高性能识别模式", color=(200, 100, 200))

                # 显示系统状态
                img.draw_string_advanced(10, 50, 12, f"帧数: {frame_count}", color=(255, 255, 255))
                img.draw_string_advanced(10, 65, 12, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))

                # 功能说明
                img.draw_string_advanced(10, 90, 14, "🚀 高性能识别功能开发中...", color=(255, 255, 0))
                img.draw_string_advanced(10, 110, 12, "此模式为后续高性能算法预留", color=(255, 255, 255))

            # 图像放大和显示
            img = img.scale(SCALE_FACTOR, SCALE_FACTOR)
            if current_mode == 3:
                draw_threshold_edit_buttons(img)
            else:
                draw_main_buttons(img)
            Display.show_image(img, x=0, y=0)

            # 性能监控输出
            if frame_count % 200 == 0:
                mode_names = {1: "中心连线", 2: "性能监控", 3: "阈值编辑", 4: "高性能识别"}
                mode_name = mode_names.get(current_mode, "未知")
                perf = get_performance_status()
                stats = get_detection_stats_text()

                print(f"📊 Step10中心连线状态 - 帧数: {frame_count}")
                print(f"📱 当前模式: {current_mode} ({mode_name})")
                print(f"🚀 性能指标: FPS={perf['fps']:.1f}({perf['fps_status']}) 延迟={perf['processing_time']:.1f}ms({perf['time_status']})")
                print(f"📈 检测统计: {stats['success_rate_text']}({perf['rate_status']}), {stats['recent_text']}")
                print("🎯 Step10可视化: 屏幕中心点+连线+坐标显示")
                print("=" * 60)

        except Exception as e:
            print(f"主循环错误: {e}")
            time.sleep_ms(50)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    print("清理资源...")

    # 输出最终性能报告
    if frame_count > 0:
        final_perf = get_performance_status()
        final_stats = get_detection_stats_text()
        final_serial_stats = get_serial_communication_status()

        print("📊 Step10中心连线最终报告:")
        print(f"   总帧数: {frame_count}")
        print(f"   平均FPS: {final_perf['fps']:.1f} ({final_perf['fps_status']}) (目标: ≥{TARGET_FPS})")
        print(f"   平均延迟: {final_perf['processing_time']:.1f}ms ({final_perf['time_status']}) (目标: ≤{TARGET_LATENCY}ms)")
        print(f"   {final_stats['success_rate_text']} ({final_perf['rate_status']}) (目标: ≥{TARGET_SUCCESS_RATE}%)")
        print(f"   {final_stats['recent_text']}")

        # ROI最终效率
        roi_x, roi_y, roi_width, roi_height = get_current_roi()
        roi_efficiency = (roi_width * roi_height) / (picture_width * picture_height) * 100
        print(f"   ROI最终效率: {roi_efficiency:.1f}%")

        # Step10新增：串口通信最终报告
        print(f"📡 串口通信报告:")
        print(f"   发送成功率: {final_serial_stats['success_rate']:.1f}% ({final_serial_stats['status']})")
        print(f"   总发送尝试: {final_serial_stats['total_attempts']}")
        print(f"   成功发送: {final_serial_stats['successful_sends']}")
        print(f"   质量检查失败: {final_serial_stats['quality_failures']}")
        print(f"   串口发送失败: {final_serial_stats['serial_failures']}")
        print(f"   数据策略: 只发送真实检测数据，禁用预测数据")

        # Step10可视化评级
        fps_ok = final_perf['fps_status'] == "优秀"
        time_ok = final_perf['time_status'] == "优秀"
        rate_ok = final_perf['rate_status'] == "优秀"

        score = sum([fps_ok, time_ok, rate_ok])
        if score == 3:
            print("🏆 Step10中心连线评级: 优秀 - 所有目标达成")
        elif score >= 2:
            print("🥈 Step10中心连线评级: 良好 - 大部分目标达成")
        elif score >= 1:
            print("🥉 Step10中心连线评级: 合格 - 部分目标达成")
        else:
            print("📈 Step10中心连线评级: 需改进 - 多项指标未达标")

    if isinstance(sensor, Sensor):
        sensor.stop()
    if uart:
        uart.deinit()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()

    print("第十步中心点连线显示系统测试完成")
    print("🎉 Step10核心功能总结:")
    print("   ✅ Step9混合优化系统（完整继承）")
    print("   ✅ 屏幕中心点标记（黄色圆点+十字）")
    print("   ✅ 中心点连线功能（蓝色连线+距离显示）")
    print("   ✅ 坐标信息显示（绿色文字+偏差计算）")
    print("   ✅ 可视化反馈信息（为STM32控制提供视觉反馈）")
    print("   ✅ 运动识别优化（多级运动状态+智能预测）")
    print("   ✅ 方案B单帧检测（移除多帧缓冲，最低延迟~30ms）")
    print("   ✅ 严格串口通信（只发送真实检测数据，禁用预测数据）")
    print("   ✅ 通信质量监控（实时统计发送成功率和失败原因）")
