[33mcommit 337b4de28d356422d7d2a26de9a76c01ad5d7df6[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmaster[m[33m)[m
Author: zengyi-thinking <<EMAIL>>
Date:   Sat Aug 2 19:35:47 2025 +0800

    第11版yolo

[33mcommit 02ae62578a7d629bfb707537c3194ee7b6626a64[m
Author: zengyi-thinking <<EMAIL>>
Date:   Sat Aug 2 01:22:06 2025 +0800

    修改参数

[33mcommit fbbde5ef0f3f654fba2b0d400165cfcd5c4ab83e[m
Author: zengyi-thinking <<EMAIL>>
Date:   Fri Aug 1 21:50:06 2025 +0800

    经典代码

[33mcommit 791894670ecd88d22d0dc371352f022981bbcc51[m
Author: zengyi-thinking <<EMAIL>>
Date:   Fri Aug 1 05:56:11 2025 +0800

    fix: 修改文件名大小写

[33mcommit dea4d9b5fcac478f7d3f4052b51cae1d481d31e9[m
Author: zengyi-thinking <<EMAIL>>
Date:   Wed Jul 30 22:18:41 2025 +0800

    feat(step4): 完善阈值编辑功能并支持文件持久化
    
    - 新增阈值编辑界面，支持矩形和激光点阈值的实时编辑和预览
    - 添加阈值文件保存和加载功能，实现配置的持久化
    - 优化触摸事件处理逻辑，支持阈值编辑按钮的点击检测
    - 重构代码结构，提高可读性和可维护性

[33mcommit abc591c3aea502adb2449a006791e9cb7243b365[m
Author: zengyi-thinking <<EMAIL>>
Date:   Wed Jul 30 20:01:59 2025 +0800

    feat: 添加矩形中心激光定位系统
    
    - 实现了基于K230开发板的矩形中心激光定位系统
    - 添加了矩形检测、激光点检测和PID控制算法
    - 集成了步进电机控制和脱机阈值调整功能
    - 编写了详细的使用说明文档

[33mcommit 96ed082d0ca9966ff5c68bade2543fd770006240[m
Author: zengyi-thinking <<EMAIL>>
Date:   Wed Jul 30 13:37:07 2025 +0800

    参考代码
