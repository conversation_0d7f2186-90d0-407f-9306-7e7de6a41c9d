# Design Document

## Overview

The step5 serial communication feature extends the existing step4 interactive interface with background UART communication capabilities. The system maintains complete backward compatibility with step4 while adding real-time coordinate transmission to STM32 microcontrollers. The design follows a non-blocking architecture where serial communication operates independently of the main UI thread.

## Architecture

### System Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Step5 Main Application                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   UI Controller │  │ Vision Pipeline │  │ Serial Manager  │ │
│  │   (from step4)  │  │   (from step4)  │  │     (new)       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│           │                     │                     │        │
│           │                     │                     │        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Touch Handler   │  │Rectangle/Laser  │  │ Packet Builder  │ │
│  │   (unchanged)   │  │   Detection     │  │     (new)       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│           │                     │                     │        │
│           └─────────────────────┼─────────────────────┘        │
│                                 │                              │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              Coordinate Data Flow                          │ │
│  │  Vision Results → Coordinate Extraction → Packet Format   │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                 │
                                 ▼
┌─────────────────────────────────────────────────────────────┐
│                    UART Hardware Layer                     │
│              (115200 baud, 8N1, GPIO 11/12)               │
└─────────────────────────────────────────────────────────────┘
                                 │
                                 ▼
┌─────────────────────────────────────────────────────────────┐
│                      STM32 Receiver                        │
│              (Packet Parser + Coordinate Handler)          │
└─────────────────────────────────────────────────────────────┘
```

### Data Flow Architecture

1. **Vision Processing**: Existing step4 rectangle and laser detection continues unchanged
2. **Coordinate Extraction**: New module extracts center coordinates from detection results
3. **Packet Formation**: Coordinates are formatted into standardized packets
4. **Serial Transmission**: Non-blocking UART transmission to STM32
5. **Error Handling**: Graceful degradation when serial communication fails

## Components and Interfaces

### Serial Manager Class

```python
class SerialManager:
    def __init__(self, uart_port=UART.UART2, baud_rate=115200):
        """Initialize serial communication with error handling"""
        
    def send_coordinates(self, rect_center, screen_center):
        """Send coordinate packet to STM32"""
        
    def build_packet(self, x1, y1, x2, y2):
        """Build standardized data packet"""
        
    def calculate_checksum(self, data):
        """Calculate packet checksum for integrity"""
        
    def is_connected(self):
        """Check serial connection status"""
```

### Coordinate Data Structure

```python
@dataclass
class CoordinateData:
    rect_x: int = 240      # Rectangle center X (default: screen center)
    rect_y: int = 120      # Rectangle center Y (default: screen center)  
    screen_x: int = 240    # Screen center X (constant)
    screen_y: int = 120    # Screen center Y (constant)
    
    def to_packet_bytes(self) -> List[int]:
        """Convert coordinates to 8-byte packet format"""
        return [
            (self.rect_x >> 8) & 0xFF,   # rect_x high byte
            self.rect_x & 0xFF,          # rect_x low byte
            (self.rect_y >> 8) & 0xFF,   # rect_y high byte  
            self.rect_y & 0xFF,          # rect_y low byte
            (self.screen_x >> 8) & 0xFF, # screen_x high byte
            self.screen_x & 0xFF,        # screen_x low byte
            (self.screen_y >> 8) & 0xFF, # screen_y high byte
            self.screen_y & 0xFF         # screen_y low byte
        ]
```

### Packet Format Specification

```
Packet Structure (13 bytes total):
┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐
│ Header1 │ Header2 │ Rect_X_H│ Rect_X_L│ Rect_Y_H│ Rect_Y_L│Scrn_X_H │Scrn_X_L │Scrn_Y_H │Scrn_Y_L │Checksum │ Tail1   │ Tail2   │
│  0xAA   │  0x55   │  uint8  │  uint8  │  uint8  │  uint8  │  uint8  │  uint8  │  uint8  │  uint8  │  uint8  │  0xFF   │  0xEE   │
└─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘
   Byte 0    Byte 1    Byte 2    Byte 3    Byte 4    Byte 5    Byte 6    Byte 7    Byte 8    Byte 9   Byte 10   Byte 11   Byte 12

Coordinate Encoding:
- Each coordinate is 16-bit (0-480 range)
- Split into high byte and low byte for transmission
- Example: X=300 → High=0x01, Low=0x2C (0x012C = 300)

Checksum Calculation:
- Sum of bytes 2-9 (coordinate data)
- Result modulo 256
- Provides basic error detection
```

## Data Models

### Enhanced Step4 Integration

The existing step4 data structures remain unchanged:

```python
# Existing step4 structures (preserved)
threshold_dict = {
    'rect': [(0, 80)],
    'laser': [(47, 80, 9, 91, -55, 63), (16, 37, 23, 74, -48, 52)]
}

# New serial communication structures
class SerialConfig:
    UART_PORT = UART.UART2
    BAUD_RATE = 115200
    TX_PIN = 11  # GPIO 11 - UART2_TXD
    RX_PIN = 12  # GPIO 12 - UART2_RXD
    
    PACKET_HEADER = [0xAA, 0x55]
    PACKET_TAIL = [0xFF, 0xEE]
    PACKET_SIZE = 13
    
    DEFAULT_RECT_X = 240  # Screen center when no rectangle detected
    DEFAULT_RECT_Y = 120
    SCREEN_CENTER_X = 240
    SCREEN_CENTER_Y = 120
```

### Detection Result Integration

```python
def extract_coordinates_from_detection(rect_success, rect_centers, rect_total_center):
    """Extract coordinate data from existing step4 detection results"""
    if rect_success and rect_total_center:
        # Use actual detected rectangle center
        rect_x = int(rect_total_center[0])
        rect_y = int(rect_total_center[1])
    else:
        # Use default center when no rectangle detected
        rect_x = SerialConfig.DEFAULT_RECT_X
        rect_y = SerialConfig.DEFAULT_RECT_Y
    
    return CoordinateData(
        rect_x=rect_x,
        rect_y=rect_y,
        screen_x=SerialConfig.SCREEN_CENTER_X,
        screen_y=SerialConfig.SCREEN_CENTER_Y
    )
```

## Error Handling

### Serial Communication Error Handling

```python
class SerialErrorHandler:
    def __init__(self):
        self.connection_status = False
        self.error_count = 0
        self.last_error_time = 0
        
    def handle_uart_error(self, error):
        """Handle UART communication errors gracefully"""
        self.error_count += 1
        self.last_error_time = time.ticks_ms()
        
        if self.error_count > 10:
            # Disable serial communication after too many errors
            self.connection_status = False
            print(f"Serial communication disabled after {self.error_count} errors")
        
        # Continue normal operation - don't crash the main application
        return False
        
    def attempt_reconnection(self):
        """Attempt to reconnect serial communication"""
        if time.ticks_ms() - self.last_error_time > 5000:  # Wait 5 seconds
            try:
                # Attempt to reinitialize UART
                self.connection_status = True
                self.error_count = 0
                return True
            except:
                return False
        return False
```

### Graceful Degradation Strategy

1. **Serial Initialization Failure**: Continue with step4 functionality, disable serial features
2. **Transmission Errors**: Log errors, continue vision processing
3. **Connection Loss**: Attempt periodic reconnection without affecting UI
4. **Invalid Data**: Skip transmission, continue with next frame

## Testing Strategy

### Unit Testing Approach

```python
class SerialCommunicationTests:
    def test_packet_formation(self):
        """Test coordinate to packet conversion"""
        coord = CoordinateData(rect_x=300, rect_y=200, screen_x=240, screen_y=120)
        packet = coord.to_packet_bytes()
        
        # Verify packet structure
        assert len(packet) == 8
        assert packet[0] == 0x01 and packet[1] == 0x2C  # 300 = 0x012C
        assert packet[2] == 0x00 and packet[3] == 0xC8  # 200 = 0x00C8
        
    def test_checksum_calculation(self):
        """Test packet checksum integrity"""
        data = [0x01, 0x2C, 0x00, 0xC8, 0x00, 0xF0, 0x00, 0x78]
        checksum = sum(data) % 256
        assert checksum == expected_checksum
        
    def test_error_handling(self):
        """Test graceful error handling"""
        serial_manager = SerialManager()
        # Simulate connection failure
        result = serial_manager.send_coordinates(None, None)
        assert result == False  # Should not crash
```

### Integration Testing

1. **Step4 Compatibility**: Verify all step4 features work identically
2. **Performance Testing**: Ensure FPS remains stable with serial communication
3. **STM32 Integration**: Test end-to-end coordinate transmission and parsing
4. **Error Recovery**: Test system behavior under various failure conditions

### Hardware Testing Setup

```
K230 Development Board
├── GPIO 11 (TX) ──────┐
├── GPIO 12 (RX) ──────┼─── STM32 Development Board
└── GND ───────────────┘     ├── UART RX (PA9/PA10)
                             ├── Coordinate Processing
                             └── Debug Output (Optional)
```

## STM32 Receiver Implementation

### STM32 Packet Parser

```c
// STM32 receiver code structure
typedef struct {
    uint16_t rect_x;
    uint16_t rect_y; 
    uint16_t screen_x;
    uint16_t screen_y;
} CoordinateData_t;

typedef enum {
    WAIT_HEADER1,
    WAIT_HEADER2, 
    READ_DATA,
    VERIFY_CHECKSUM,
    WAIT_TAIL1,
    WAIT_TAIL2
} PacketState_t;

class PacketParser {
    PacketState_t state;
    uint8_t data_buffer[8];
    uint8_t data_index;
    uint8_t received_checksum;
    
public:
    bool ProcessByte(uint8_t byte);
    CoordinateData_t GetCoordinates();
    bool IsPacketValid();
};
```

### STM32 Integration Example

```c
// Example STM32 usage
void UART_IRQHandler(void) {
    if (UART_GetITStatus(UART2, UART_IT_RXNE)) {
        uint8_t received_byte = UART_ReceiveData(UART2);
        
        if (packet_parser.ProcessByte(received_byte)) {
            // Complete packet received
            CoordinateData_t coords = packet_parser.GetCoordinates();
            
            // Process coordinates for servo control, etc.
            ProcessCoordinates(coords.rect_x, coords.rect_y, 
                             coords.screen_x, coords.screen_y);
        }
    }
}
```

## Performance Considerations

### Non-Blocking Design

- Serial transmission occurs after image processing
- UART operations use hardware buffers to prevent blocking
- Error handling designed to never interrupt main loop
- Coordinate extraction adds minimal computational overhead

### Memory Usage

- Packet buffer: 13 bytes per transmission
- Coordinate data structure: 8 bytes
- Error handling state: ~20 bytes
- Total additional memory: <100 bytes

### Timing Analysis

```
Main Loop Timing (per frame):
├── Image Capture: ~10ms (unchanged)
├── Vision Processing: ~15ms (unchanged) 
├── UI Rendering: ~5ms (unchanged)
├── Coordinate Extraction: ~0.1ms (new)
├── Packet Formation: ~0.1ms (new)
└── Serial Transmission: ~0.2ms (new, non-blocking)

Total Impact: +0.4ms per frame (~1% overhead)
Expected FPS: Maintains step4 performance (30+ FPS)
```

This design ensures that the serial communication feature integrates seamlessly with the existing step4 codebase while providing reliable coordinate transmission to STM32 microcontrollers.