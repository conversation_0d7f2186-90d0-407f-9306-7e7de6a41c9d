# bilibili搜索学不会电磁场看教程
# K230矩形中心激光定位系统 - 完全重新设计版
# 功能：识别黑色方框中的矩形，检测激光点，使用步进电机PID控制将激光点移动到矩形中心
# 参考：05色块追踪与线段识别.py, 12_PID激光点回中.py, 14_脱机调整阈值.py

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import FPIOA, Pin, UART, TOUCH

sensor = None

try:
    print("矩形中心激光定位系统启动")

    # PID控制器类 - 参考12_PID激光点回中.py
    # 参数: kp(比例系数), ki(积分系数), target(目标值)
    class PID_step_motor:
        def __init__(self, kp, ki, target=240):
            self.e = 0
            self.e_last = 0
            self.kp = kp
            self.ki = ki
            self.target = target
        
        # 计算PID输出
        # 参数: value(当前值)
        # 返回: delta(控制量变化)
        def cal(self, value):
            self.e = self.target - value
            delta = self.kp * (self.e-self.e_last) + self.ki * self.e
            self.e_last = self.e
            return delta

    # 传感器初始化 - 参考显示参考.py
    sensor = Sensor(width=1920, height=1080)
    sensor.reset()
    sensor.set_framesize(width=1920, height=1080)
    sensor.set_pixformat(Sensor.RGB565)

    # 显示器初始化 - 使用LCD模式800x480
    Display.init(Display.ST7701, to_ide=True)
    MediaManager.init()
    sensor.run()

    # 步进电机串口通信初始化
    fpioa = FPIOA()
    fpioa.set_function(11, FPIOA.UART2_TXD)
    fpioa.set_function(12, FPIOA.UART2_RXD)
    uart2 = UART(UART.UART2, 115200)
    rx_order = [0xAA, 0xAA, 0x00, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xFF]

    # 发送步进电机控制指令并返回控制数据
    # 参数: number(电机编号), dir_(方向), steps(步数)
    def send_order(number, dir_, steps):
        global rx_order, uart2
        rx_order[2] = number
        rx_order[3] = dir_
        rx_order[4] = (steps // 256) % 256
        rx_order[5] = steps % 256
        rx_order[6] = sum(rx_order[2:6]) % 256
        
        # 发送控制指令
        uart2.write(bytes(rx_order))
        
        # 通过串口二返回控制数据
        control_data = f"Motor{number}: Dir={dir_}, Steps={steps}\n"
        uart2.write(control_data.encode())
        
        print(f"步进电机控制 - 电机{number}: 方向={dir_}, 步数={steps}")
        return rx_order

    # 按键初始化 - 参考14_脱机调整阈值.py
    fpioa.set_function(53, FPIOA.GPIO53)
    key = Pin(53, Pin.IN, Pin.PULL_DOWN)

    # 激光笔控制
    fpioa.set_function(33, FPIOA.GPIO33)
    pin = Pin(33, Pin.OUT)
    pin.value(0)

    # 触摸屏初始化 - 用于脱机调整阈值
    tp = TOUCH(0)

    clock = time.clock()

    # 系统状态标识 - 参考14_脱机调整阈值.py
    # flag = 0: 待机状态，等待矩形标定
    # flag = 1: 激光点识别和PID控制
    # flag = 2: 阈值调整模式
    flag = 0

    # 系统参数初始化
    touch_counter = 0

    # 初始化中心点和PID对象
    c_x = 240  # ROI中心
    c_y = 240  # ROI中心
    pid_x = PID_step_motor(-0.2, -0.1, c_x)
    pid_y = PID_step_motor(-0.2, -0.1, c_y)

    # 图像裁剪ROI设置 - 参考14_脱机调整阈值.py
    # ROI格式: (x, y, width, height) - 使用480x480的大小，性能高且刚好铺满LCD屏幕
    cut_roi = (540, 300, 480, 480)

    # 阈值字典 - 参考14_脱机调整阈值.py
    threshold_dict = {
        'rect': [(59, 246)],  # 矩形检测阈值
        'red_point': [(47, 80, 9, 91, -55, 63), (16, 37, 23, 74, -48, 52)]  # 激光点检测阈值
    }

    # 向屏幕显示图像 - 参考14_脱机调整阈值.py
    # 参数: 无 (使用全局变量img)
    def show_img_2_screen():
        global img
        if(img.height()>480 or img.width() > 800):
            scale = max(img.height() // 480, img.width() // 800) + 1
            img.midpoint_pool(scale, scale)
        img.compress_for_ide()
        Display.show_image(img, x=(800-img.width())//2, y=(480-img.height())//2)

    # 矩形检测和中心点计算 - 参考12_PID激光点回中.py
    # 参数: img(输入图像)
    # 返回: success(是否成功), center_x, center_y(中心点坐标)
    def detect_rectangle_center(img):
        img_rect = img.to_grayscale(copy=True)
        img_rect = img_rect.binary(threshold_dict['rect'])
        rects = img_rect.find_rects(threshold=10000)
        
        if rects and len(rects) == 2:  # 检测到两个矩形框
            total_x, total_y = 0, 0
            for rect in rects:
                corner = rect.corners()
                # 绘制矩形边框
                for i in range(4):
                    next_i = (i + 1) % 4
                    img.draw_line(corner[i][0], corner[i][1], corner[next_i][0], corner[next_i][1], 
                                color=(0, 255, 0), thickness=3)
                
                # 计算矩形中心
                rect_center_x = sum([corner[k][0] for k in range(4)])/4
                rect_center_y = sum([corner[k][1] for k in range(4)])/4
                total_x += rect_center_x
                total_y += rect_center_y
            
            # 计算两个矩形的总中心点
            center_x = total_x / 2
            center_y = total_y / 2
            
            # 绘制中心点
            img.draw_circle(int(center_x), int(center_y), 8, color=(255, 255, 0), thickness=3)
            
            return True, center_x, center_y
        
        return False, 0, 0

    # 激光点检测 - 参考12_PID激光点回中.py
    # 参数: img(输入图像)
    # 返回: success(是否成功), laser_x, laser_y(激光点坐标)
    def detect_laser_point(img):
        blobs = img.find_blobs(threshold_dict['red_point'], False,
                             x_stride=1, y_stride=1,
                             pixels_threshold=20, margin=False)
        
        if blobs:
            blob = blobs[0]  # 取第一个检测到的色块
            img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(), 
                             color=(0, 255, 0), thickness=2, fill=False)
            laser_x = blob.x() + blob.w() / 2
            laser_y = blob.y() + blob.h() / 2
            return True, laser_x, laser_y
        
        return False, 0, 0

    print("系统初始化完成，等待操作...")

    # 主循环
    while True:
        clock.tick()
        os.exitpoint()
        
        # 长按触摸屏进入阈值调整模式 - 参考14_脱机调整阈值.py
        points = tp.read()
        if len(points) > 0:
            touch_counter += 1
            if touch_counter > 30:  # 长按约1.5秒进入阈值调整模式
                flag = 2
                print("长按触摸检测到，进入阈值调整模式")
                touch_counter = 0
        else:
            touch_counter = max(0, touch_counter - 1)

        # 阈值调整模式 - 完全参考14_脱机调整阈值.py
        if flag == 2:
            print("进入阈值调整模式")
            
            # 可以调多个阈值
            threshold_mode_lst = list(threshold_dict.keys())
            threshold_mode = 'rect'
            current_mode_index = 0
            
            # 阈值调整界面主循环
            while flag == 2:
                img = sensor.snapshot(chn=CAM_CHN_ID_0)
                img = img.copy(roi=cut_roi)
                
                # 显示当前调整的阈值类型
                img.draw_string_advanced(10, 10, 25, f"调整: {threshold_mode}", color=(255, 255, 0))
                img.draw_string_advanced(10, 40, 20, "短按屏幕切换模式", color=(0, 255, 255))
                img.draw_string_advanced(10, 70, 20, "长按屏幕退出", color=(0, 255, 255))
                
                # 根据当前模式处理图像
                if threshold_mode == 'rect':
                    img_processed = img.to_grayscale()
                    img_processed = img_processed.binary(threshold_dict['rect'])
                    rects = img_processed.find_rects(threshold=10000)
                    for rect in rects:
                        corner = rect.corners()
                        for i in range(4):
                            next_i = (i + 1) % 4
                            img.draw_line(corner[i][0], corner[i][1], corner[next_i][0], corner[next_i][1], 
                                        color=(255, 0, 0), thickness=2)
                elif threshold_mode == 'red_point':
                    blobs = img.find_blobs(threshold_dict['red_point'], False,
                                         x_stride=1, y_stride=1,
                                         pixels_threshold=20, margin=False)
                    for blob in blobs:
                        img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(), 
                                         color=(255, 0, 0), thickness=2, fill=False)
                
                # 触摸检测
                points = tp.read()
                if len(points) > 0:
                    touch_counter += 1
                    if touch_counter > 30:  # 长按退出
                        flag = 0
                        print("退出阈值调整模式")
                        touch_counter = 0
                        break
                    elif touch_counter > 5:  # 短按切换模式
                        current_mode_index = (current_mode_index + 1) % len(threshold_mode_lst)
                        threshold_mode = threshold_mode_lst[current_mode_index]
                        print(f"切换到调整模式: {threshold_mode}")
                        touch_counter = 0
                else:
                    touch_counter = max(0, touch_counter - 1)
                
                show_img_2_screen()
                time.sleep_ms(50)

        # 按键检测 - 矩形标定
        elif key.value() == 1 and flag == 0:
            print("开始矩形标定...")
            time.sleep_ms(2000)  # 参考14_脱机调整阈值.py的延时

            for i in range(5):
                img = sensor.snapshot(chn=CAM_CHN_ID_0)
                img = img.copy(roi=cut_roi)

                success, center_x, center_y = detect_rectangle_center(img)

                if success:
                    c_x, c_y = center_x, center_y
                    img.draw_string_advanced(30, 30, 30, f"Center: ({int(c_x)}, {int(c_y)})", color=(255, 255, 0))
                    show_img_2_screen()

                    print(f"矩形中心点: ({round(c_x)}, {round(c_y)})")
                    flag = 1
                    time.sleep_ms(500)

                    # 设置PID目标值
                    pid_x.target = c_x
                    pid_y.target = c_y
                    break
                else:
                    img.draw_string_advanced(30, 30, 30, "识别错误", color=(255, 0, 0))
                    show_img_2_screen()
                    time.sleep_ms(500)

            if flag == 0:
                print("矩形识别失败")

        # 激光点跟踪和PID控制
        elif flag == 1:
            pin.value(1)  # 打开激光笔
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            img = img.copy(roi=cut_roi)

            success, laser_x, laser_y = detect_laser_point(img)

            if success:
                # 绘制激光点
                img.draw_circle(int(laser_x), int(laser_y), 8, color=(255, 0, 0), thickness=2)

                # 绘制目标中心点
                img.draw_circle(int(pid_x.target), int(pid_y.target), 12, color=(0, 255, 255), thickness=3)

                # 绘制连接线
                img.draw_line(int(laser_x), int(laser_y), int(pid_x.target), int(pid_y.target),
                            color=(255, 255, 0), thickness=2)

                # PID计算
                delta_x = pid_x.cal(laser_x)
                delta_y = pid_y.cal(laser_y)

                # 步进电机控制
                if abs(delta_x) > 1:  # 死区控制
                    dir_x = 1 if delta_x > 0 else 0
                    send_order(0, dir_x, abs(int(delta_x)))

                if abs(delta_y) > 1:  # 死区控制
                    dir_y = 1 if delta_y < 0 else 0
                    send_order(1, dir_y, abs(int(delta_y)))

                # 显示控制信息
                img.draw_string_advanced(10, 10, 20, f"Error: ({delta_x:.1f}, {delta_y:.1f})", color=(255, 255, 0))
                img.draw_string_advanced(10, 40, 20, f"Laser: ({int(laser_x)}, {int(laser_y)})", color=(255, 0, 0))
                img.draw_string_advanced(10, 70, 20, f"Target: ({int(pid_x.target)}, {int(pid_y.target)})", color=(0, 255, 255))
            else:
                img.draw_string_advanced(30, 30, 25, "未检测到激光点", color=(255, 0, 0))

            show_img_2_screen()

        # 正常模式 - 显示实时图像
        else:
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            img = img.copy(roi=cut_roi)

            # 显示操作提示
            img.draw_string_advanced(10, 10, 25, "按键标定矩形", color=(255, 255, 0))
            img.draw_string_advanced(10, 40, 20, "长按屏幕调阈值", color=(0, 255, 255))
            img.draw_string_advanced(10, 70, 20, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))

            show_img_2_screen()

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    # 清理资源
    if sensor is not None:
        sensor.stop()
    try:
        Display.deinit()
    except:
        pass
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    try:
        MediaManager.deinit()
    except:
        pass
