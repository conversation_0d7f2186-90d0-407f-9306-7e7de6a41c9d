# Step11 传感器配置修复说明

## 🔧 问题分析

### 错误信息
```
AttributeError: type object 'Sensor' has no attribute 'RGB888_PLANAR'
```

### 根本原因
Step11中使用了错误的像素格式常量 `Sensor.RGB888_PLANAR`，但实际上应该使用从 `media.sensor` 导入的常量 `PIXEL_FORMAT_RGB_888_PLANAR`。

## ✅ 正确的配置方式

### 1. 导入语句（已正确）
```python
from media.sensor import *
from media.display import *
from media.media import *
```

### 2. 传感器配置（需要修复）

**错误的配置：**
```python
# 错误：使用了不存在的Sensor类属性
sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)
sensor.set_pixformat(Sensor.RGB888_PLANAR, chn=CAM_CHN_ID_2)
```

**正确的配置（参考det_video.py）：**
```python
# 正确：使用从media.sensor导入的常量
sensor.set_framesize(width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT)
sensor.set_pixformat(PIXEL_FORMAT_YUV_SEMIPLANAR_420)
sensor.set_framesize(width=OUT_RGB888P_WIDTH, height=OUT_RGB888P_HEIGH, chn=CAM_CHN_ID_2)
sensor.set_pixformat(PIXEL_FORMAT_RGB_888_PLANAR, chn=CAM_CHN_ID_2)
```

### 3. 显示配置（需要修复）

**错误的配置：**
```python
# 错误：使用了自定义的DisplayLayer类
Display.bind_layer(**sensor_bind_info, layer=DisplayLayer.LAYER_VIDEO1)
Display.show_image(osd_img, 0, 0, DisplayLayer.LAYER_OSD3)
```

**正确的配置（参考det_video.py）：**
```python
# 正确：使用从media.display导入的常量
Display.bind_layer(**sensor_bind_info, layer=Display.LAYER_VIDEO1)
Display.show_image(osd_img, 0, 0, Display.LAYER_OSD3)
```

## 🎯 修复步骤

### 步骤1：修复传感器像素格式
将Step11中的传感器配置完全改为与det_video.py一致：

```python
# 在detection()函数中，替换传感器配置部分
sensor = Sensor(id=2)
sensor.reset()
sensor.set_hmirror(False)
sensor.set_vflip(False)
sensor.set_framesize(width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT)
sensor.set_pixformat(PIXEL_FORMAT_YUV_SEMIPLANAR_420)
sensor.set_framesize(width=OUT_RGB888P_WIDTH, height=OUT_RGB888P_HEIGH, chn=CAM_CHN_ID_2)
sensor.set_pixformat(PIXEL_FORMAT_RGB_888_PLANAR, chn=CAM_CHN_ID_2)
```

### 步骤2：修复显示层配置
确保使用正确的显示层常量：

```python
# 显示初始化
sensor_bind_info = sensor.bind_info(x=0, y=0, chn=CAM_CHN_ID_0)
Display.bind_layer(**sensor_bind_info, layer=Display.LAYER_VIDEO1)

# 图像显示
Display.show_image(osd_img, 0, 0, Display.LAYER_OSD3)
```

### 步骤3：移除自定义常量定义
删除Step11中自定义的DisplayLayer类和错误的像素格式常量，因为这些应该从media模块自动导入。

## 📊 验证方法

### 1. 启动测试
运行修复后的Step11系统，确保：
- ✅ 串口初始化成功
- ✅ 传感器初始化成功（无RGB888_PLANAR错误）
- ✅ 显示系统正常工作
- ✅ YOLO检测正常运行

### 2. 功能测试
验证所有Step11功能：
- ✅ YOLO目标检测
- ✅ 矩形中心点计算
- ✅ STM32串口通信
- ✅ 可视化显示
- ✅ 性能监控

## 🔍 关键要点

### 1. 常量来源
- `PIXEL_FORMAT_*` 常量来自 `media.sensor`
- `Display.LAYER_*` 常量来自 `media.display`
- 不要自定义这些常量，使用系统提供的

### 2. 配置一致性
- 完全按照det_video.py的配置方式
- 不要混合使用不同的API风格
- 保持与原始YOLO检测代码的兼容性

### 3. 错误处理
- 添加适当的错误检查
- 确保资源正确清理
- 保持系统稳定性

## 🎉 预期结果

修复后的Step11系统应该能够：

1. **正常启动**：无传感器配置错误
2. **稳定运行**：YOLO检测正常工作
3. **功能完整**：所有Step11增强功能可用
4. **性能良好**：达到预期的帧率和检测率

### 成功标志
```
✅ 串口初始化成功: UART2, 波特率: 115200
find sensor gc2093_csi2, type 24, output 1920x1080@60
🎯 Step11系统初始化完成
📱 串口状态: ✅连接
📊 开始检测...
```

如果看到以上输出且没有AttributeError，说明修复成功！
