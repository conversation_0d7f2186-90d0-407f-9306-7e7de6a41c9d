# bilibili搜索学不会电磁场看教程
# K230矩形中心激光定位系统 - 第九步：混合优化系统
# 基于Step8和Step7的最佳功能组合，确保稳定性和正确率
#
# 核心组合策略：
# 1. 检测算法：采用Step8的动态快速矩形识别算法（已验证高正确率）
# 2. 阈值编辑：采用Step7的成熟阈值编辑界面（已验证稳定性）
# 3. 性能监控：采用Step8的性能监控系统（实时性能反馈）
# 4. 串口通信：采用Step8的高频通信机制（30ms间隔）
#
# 设计目标：
# - 矩形检测正确率 ≥85%
# - 系统帧率 ≥20FPS
# - 响应延迟 ≤50ms
# - 界面稳定性和易用性

import time
import os
import sys
import math
from media.sensor import *
from media.display import *
from media.media import *
from machine import TOUCH
from machine import UART
from machine import FPIOA

# 常量定义
CAM_CHN_ID_0 = 0

# ==================== Step8核心：性能目标配置 ====================
TARGET_FPS = 60              # 目标帧率 ≥20FPS
TARGET_SUCCESS_RATE = 85     # 提高目标识别率到85%
TARGET_LATENCY = 50          # 目标延迟 ≤50ms

# Step8快速检测模式配置
FAST_MODE = True             # 启用快速模式
SIMPLIFIED_VALIDATION = True # 简化特征验证
ADAPTIVE_ROI = True          # 启用自适应ROI
MOTION_PREDICTION = True     # 启用运动预测

# ==================== Step7核心：LAB阈值系统 ====================
# 阈值编辑全局变量 - 继承Step7的成熟设计
threshold_edit_mode = "rect"
threshold_current = [13, 35, 20, -5, -44, 16]  # 用户调整的LAB阈值

# 阈值存储机制 - Step7的稳定设计
threshold_dict = {
    'rect': [(13, 35, 20, -5, -44, 16)],  # 黑色矩形的LAB阈值
    'laser': [(47, 80, 9, 91, -55, 63), (16, 37, 23, 74, -48, 52)]  # 激光点LAB阈值
}

# ==================== Step8核心：统计系统 ====================
rect_detection_stats = {
    'total_attempts': 0,
    'successful_detections': 0,
    'success_rate': 0.0,
    'recent_attempts': [],    # 最近50次检测记录
    'recent_success_rate': 0.0,
    'fps_history': [],        # FPS历史记录
    'avg_fps': 0.0,
    'processing_times': [],   # 处理时间历史
    'avg_processing_time': 0.0
}

# ==================== Step8核心：快速多帧稳定性检测系统 ====================
fast_multi_frame_detection = {
    'frame_buffer': [],           # 存储最近3帧的检测结果
    'buffer_size': 3,            # 缓冲区大小
    'stable_threshold': 2,       # 稳定阈值（3帧中至少2帧检测成功）
    'position_threshold': 20.0,  # 位置变化阈值
    'last_stable_center': None,
    'last_send_center': None,
    'send_threshold': 5.0,       # 发送阈值
    'confidence_threshold': 0.6  # 置信度阈值
}

# ==================== Step8核心：自适应ROI系统 ====================
adaptive_roi_config = {
    'enabled': ADAPTIVE_ROI,
    'roi_x': 0,
    'roi_y': 0,
    'roi_width': 400,
    'roi_height': 240,
    'expansion_factor': 1.5,     # ROI扩展因子
    'min_roi_size': 100,         # 最小ROI尺寸
    'max_roi_size': 400,         # 最大ROI尺寸
    'update_threshold': 5,       # ROI更新阈值
    'frames_since_update': 0
}

# ==================== Step8核心：运动预测系统 ====================
motion_prediction_config = {
    'enabled': MOTION_PREDICTION,
    'history_length': 5,         # 运动历史长度
    'position_history': [],      # 位置历史
    'velocity_history': [],      # 速度历史
    'predicted_position': None,  # 预测位置
    'prediction_confidence': 0.0,# 预测置信度
    'max_velocity': 100.0,       # 最大速度
    'velocity_smoothing': 0.7    # 速度平滑因子
}

# ==================== Step8核心：串口通信配置 ====================
uart = None
SERIAL_PORT = UART.UART2
BAUD_RATE = 115200
TX_PIN = 11
RX_PIN = 12

PACKET_HEADER = [0xAA, 0x55]
PACKET_TAIL = [0x0D, 0x0A]
SCREEN_CENTER_X = 200
SCREEN_CENTER_Y = 120

# ==================== 检测参数配置 ====================
RECT_THRESHOLD = [(13, 35, 20, -5, -44, 16)]
MIN_RECT_AREA = 2000
MAX_RECT_AREA = 60000
MIN_RECT_ASPECT_RATIO = 0.2
MAX_RECT_ASPECT_RATIO = 5.0

LASER_THRESHOLD = [
    (47, 80, 9, 91, -55, 63),
    (16, 37, 23, 74, -48, 52)
]
MIN_LASER_PIXELS = 15

# ==================== 图像和显示参数 ====================
picture_width = 400
picture_height = 240
sensor_id = 2
DISPLAY_MODE = "LCD"
DISPLAY_WIDTH = 800
DISPLAY_HEIGHT = 480
SCALE_FACTOR = 2.0
SCALED_WIDTH = int(picture_width * SCALE_FACTOR)
SCALED_HEIGHT = int(picture_height * SCALE_FACTOR)

# ==================== 按键定义（修改为支持4个按钮） ====================
BUTTON_WIDTH = 150
BUTTON_HEIGHT = 60
BUTTON_Y = SCALED_HEIGHT - BUTTON_HEIGHT - 10
BUTTON_SPACING = 160  # 减小间距以容纳4个按钮
BUTTON_START_X = 20   # 调整起始位置
BUTTON1_POS = (BUTTON_START_X, BUTTON_Y)
BUTTON2_POS = (BUTTON_START_X + BUTTON_SPACING, BUTTON_Y)
BUTTON3_POS = (BUTTON_START_X + BUTTON_SPACING * 2, BUTTON_Y)
BUTTON4_POS = (BUTTON_START_X + BUTTON_SPACING * 3, BUTTON_Y)  # 新增第四个按钮
BUTTON_ACTIVE_COLOR = (0, 255, 0)
BUTTON_INACTIVE_COLOR = (80, 80, 80)
BUTTON_TEXT_COLOR = (255, 255, 255)

# ==================== 全局变量 ====================
sensor = None
frame_count = 0
current_mode = 1
last_send_time = 0
SEND_INTERVAL = 30  # Step8的高频发送间隔

# ==================== Step8核心：自适应ROI函数 ====================
def update_adaptive_roi(detection_center):
    """根据检测结果更新自适应ROI - Step8核心算法"""
    global adaptive_roi_config

    if not adaptive_roi_config['enabled'] or detection_center is None:
        return

    center_x, center_y = detection_center
    expansion = adaptive_roi_config['expansion_factor']
    min_size = adaptive_roi_config['min_roi_size']
    max_size = adaptive_roi_config['max_roi_size']

    # 计算新的ROI尺寸
    new_roi_size = min(max_size, max(min_size, int(200 * expansion)))

    # 计算ROI位置
    new_roi_x = max(0, min(picture_width - new_roi_size, int(center_x - new_roi_size // 2)))
    new_roi_y = max(0, min(picture_height - new_roi_size, int(center_y - new_roi_size // 2)))

    # 确保ROI不超出图像边界
    new_roi_width = min(new_roi_size, picture_width - new_roi_x)
    new_roi_height = min(new_roi_size, picture_height - new_roi_y)

    # 更新ROI配置
    adaptive_roi_config['roi_x'] = new_roi_x
    adaptive_roi_config['roi_y'] = new_roi_y
    adaptive_roi_config['roi_width'] = new_roi_width
    adaptive_roi_config['roi_height'] = new_roi_height
    adaptive_roi_config['frames_since_update'] = 0

    # 减少调试输出频率
    if frame_count % 50 == 0:
        print(f"🎯 ROI: ({new_roi_x},{new_roi_y}) {new_roi_width}x{new_roi_height}")

def reset_adaptive_roi():
    """重置ROI到全图 - Step8核心算法"""
    global adaptive_roi_config
    adaptive_roi_config['roi_x'] = 0
    adaptive_roi_config['roi_y'] = 0
    adaptive_roi_config['roi_width'] = picture_width
    adaptive_roi_config['roi_height'] = picture_height
    adaptive_roi_config['frames_since_update'] = 0

def get_current_roi():
    """获取当前ROI区域 - Step8核心算法"""
    global adaptive_roi_config
    return (adaptive_roi_config['roi_x'], adaptive_roi_config['roi_y'],
            adaptive_roi_config['roi_width'], adaptive_roi_config['roi_height'])

# ==================== Step8核心：运动预测函数 ====================
def update_motion_prediction(current_center):
    """更新运动预测 - Step8核心算法"""
    global motion_prediction_config

    if not motion_prediction_config['enabled'] or current_center is None:
        return

    # 添加当前位置到历史
    motion_prediction_config['position_history'].append(current_center)

    # 保持历史长度
    max_length = motion_prediction_config['history_length']
    if len(motion_prediction_config['position_history']) > max_length:
        motion_prediction_config['position_history'].pop(0)

    # 计算速度
    if len(motion_prediction_config['position_history']) >= 2:
        recent_positions = motion_prediction_config['position_history'][-2:]
        velocity = (
            recent_positions[1][0] - recent_positions[0][0],
            recent_positions[1][1] - recent_positions[0][1]
        )

        # 速度平滑
        if motion_prediction_config['velocity_history']:
            smoothing = motion_prediction_config['velocity_smoothing']
            last_velocity = motion_prediction_config['velocity_history'][-1]
            velocity = (
                last_velocity[0] * (1 - smoothing) + velocity[0] * smoothing,
                last_velocity[1] * (1 - smoothing) + velocity[1] * smoothing
            )

        motion_prediction_config['velocity_history'].append(velocity)

        # 保持速度历史长度
        if len(motion_prediction_config['velocity_history']) > max_length:
            motion_prediction_config['velocity_history'].pop(0)

        # 计算预测位置
        if len(motion_prediction_config['velocity_history']) >= 3:
            avg_velocity = motion_prediction_config['velocity_history'][-1]
            predicted_pos = (
                current_center[0] + avg_velocity[0],
                current_center[1] + avg_velocity[1]
            )

            # 速度限制
            speed = math.sqrt(avg_velocity[0]**2 + avg_velocity[1]**2)
            if speed <= motion_prediction_config['max_velocity']:
                motion_prediction_config['predicted_position'] = predicted_pos
                motion_prediction_config['prediction_confidence'] = min(1.0, 1.0 - speed / motion_prediction_config['max_velocity'])
            else:
                motion_prediction_config['prediction_confidence'] = 0.0

def get_predicted_position():
    """获取预测位置 - Step8核心算法"""
    global motion_prediction_config
    return (motion_prediction_config['predicted_position'],
            motion_prediction_config['prediction_confidence'])

def get_motion_state():
    """获取运动状态 - Step8核心算法"""
    global motion_prediction_config

    if not motion_prediction_config['velocity_history']:
        return 0  # 静止

    recent_velocity = motion_prediction_config['velocity_history'][-1]
    speed = math.sqrt(recent_velocity[0]**2 + recent_velocity[1]**2)

    if speed < 5.0:
        return 0  # 静止
    elif speed < 20.0:
        return 1  # 慢速运动
    else:
        return 2  # 快速运动

# ==================== Step8核心：快速矩形检测算法 ====================
def detect_rectangles_fast(img):
    """快速矩形检测算法 - Step8核心算法，已验证高正确率"""
    try:
        start_time = time.ticks_ms()

        # 获取当前ROI区域
        roi_x, roi_y, roi_width, roi_height = get_current_roi()

        # 创建检测图像副本
        if adaptive_roi_config['enabled'] and (roi_width < picture_width or roi_height < picture_height):
            detect_img = img.copy(roi=(roi_x, roi_y, roi_width, roi_height))
            # 减少调试输出频率
            if frame_count % 100 == 0:
                print(f"🎯 ROI: ({roi_x},{roi_y}) {roi_width}x{roi_height}")
        else:
            detect_img = img.copy()

        # LAB二值化
        binary_img = detect_img.binary(RECT_THRESHOLD)

        # 快速形态学操作
        if FAST_MODE:
            binary_img.erode(1, threshold=1)
        else:
            binary_img.erode(1, threshold=1)
            binary_img.dilate(1, threshold=1)

        # 查找矩形
        rects = binary_img.find_rects(threshold=MIN_RECT_AREA)

        if not rects:
            processing_time = time.ticks_ms() - start_time
            return False, 0, [], None, processing_time

        # 快速特征验证
        valid_rects = []
        for rect in rects:
            area = rect.w() * rect.h()
            if MIN_RECT_AREA <= area <= MAX_RECT_AREA:
                if SIMPLIFIED_VALIDATION:
                    # 简化验证：只检查面积和基本宽高比
                    aspect_ratio = max(rect.w(), rect.h()) / min(rect.w(), rect.h())
                    if MIN_RECT_ASPECT_RATIO <= aspect_ratio <= MAX_RECT_ASPECT_RATIO:
                        valid_rects.append(rect)
                else:
                    # 完整验证
                    aspect_ratio = max(rect.w(), rect.h()) / min(rect.w(), rect.h())
                    if MIN_RECT_ASPECT_RATIO <= aspect_ratio <= MAX_RECT_ASPECT_RATIO:
                        valid_rects.append(rect)

        if len(valid_rects) < 2:
            processing_time = time.ticks_ms() - start_time
            return False, 0, [], None, processing_time

        # 快速配对算法
        valid_rects.sort(key=lambda r: r.w() * r.h(), reverse=True)

        # 限制检查的矩形对数量
        max_pairs_to_check = 3 if FAST_MODE else 5
        best_pair = None
        best_score = 0

        for i in range(min(len(valid_rects), max_pairs_to_check)):
            for j in range(i + 1, min(len(valid_rects), max_pairs_to_check)):
                rect1, rect2 = valid_rects[i], valid_rects[j]

                # 计算中心点
                center1 = (rect1.x() + rect1.w() // 2, rect1.y() + rect1.h() // 2)
                center2 = (rect2.x() + rect2.w() // 2, rect2.y() + rect2.h() // 2)

                # 快速距离检查
                distance = math.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)

                # 根据运动状态调整阈值
                motion_state = get_motion_state()
                if motion_state == 0:  # 静止
                    CENTER_MATCH_THRESHOLD = 8.0
                elif motion_state == 1:  # 慢速运动
                    CENTER_MATCH_THRESHOLD = 12.0
                else:  # 快速运动
                    CENTER_MATCH_THRESHOLD = 18.0

                if distance <= CENTER_MATCH_THRESHOLD:
                    # 计算配对得分
                    area_score = min(rect1.w() * rect1.h(), rect2.w() * rect2.h())
                    distance_score = 1.0 / (1.0 + distance)
                    total_score = area_score * distance_score

                    if total_score > best_score:
                        best_score = total_score
                        best_pair = (rect1, rect2, center1, center2)

        if best_pair is None:
            processing_time = time.ticks_ms() - start_time
            return False, 0, [], None, processing_time

        rect1, rect2, center1, center2 = best_pair

        # 调整坐标到原图坐标系
        if adaptive_roi_config['enabled'] and (roi_width < picture_width or roi_height < picture_height):
            center1 = (center1[0] + roi_x, center1[1] + roi_y)
            center2 = (center2[0] + roi_x, center2[1] + roi_y)

        # 计算平均中心点
        avg_center = ((center1[0] + center2[0]) / 2, (center1[1] + center2[1]) / 2)

        # 绘制检测结果到原图
        # 调整矩形坐标
        if adaptive_roi_config['enabled'] and (roi_width < picture_width or roi_height < picture_height):
            rect1_adj = (rect1.x() + roi_x, rect1.y() + roi_y, rect1.w(), rect1.h())
            rect2_adj = (rect2.x() + roi_x, rect2.y() + roi_y, rect2.w(), rect2.h())
        else:
            rect1_adj = (rect1.x(), rect1.y(), rect1.w(), rect1.h())
            rect2_adj = (rect2.x(), rect2.y(), rect2.w(), rect2.h())

        # 绘制矩形框
        img.draw_rectangle(rect1_adj[0], rect1_adj[1], rect1_adj[2], rect1_adj[3],
                          color=(255, 0, 255), thickness=2, fill=False)
        img.draw_rectangle(rect2_adj[0], rect2_adj[1], rect2_adj[2], rect2_adj[3],
                          color=(0, 255, 0), thickness=2, fill=False)

        # 绘制中心点
        img.draw_circle(int(avg_center[0]), int(avg_center[1]), 8,
                       color=(255, 0, 0), thickness=3)
        img.draw_string_advanced(int(avg_center[0]) - 20, int(avg_center[1]) - 30,
                               14, "FAST", color=(255, 0, 0))

        # 更新自适应ROI
        update_adaptive_roi(avg_center)

        # 更新运动预测
        update_motion_prediction(avg_center)

        processing_time = time.ticks_ms() - start_time

        return True, 2, [center1, center2], avg_center, processing_time

    except Exception as e:
        print(f"快速矩形检测错误: {e}")
        processing_time = time.ticks_ms() - start_time
        return False, 0, [], None, processing_time

# ==================== Step8核心：多帧稳定性检测 ====================
def update_fast_multi_frame_buffer(detection_result, rect_centers, rect_total_center):
    """更新快速多帧缓冲区 - Step8核心算法"""
    global fast_multi_frame_detection

    frame_data = {
        'success': detection_result,
        'centers': rect_centers,
        'total_center': rect_total_center,
        'timestamp': time.ticks_ms()
    }

    fast_multi_frame_detection['frame_buffer'].append(frame_data)

    # 保持缓冲区大小
    if len(fast_multi_frame_detection['frame_buffer']) > fast_multi_frame_detection['buffer_size']:
        fast_multi_frame_detection['frame_buffer'].pop(0)

def get_fast_stable_result():
    """获取快速稳定检测结果 - Step8核心算法"""
    global fast_multi_frame_detection

    buffer = fast_multi_frame_detection['frame_buffer']
    if len(buffer) < fast_multi_frame_detection['stable_threshold']:
        return None, 0.0

    # 统计成功的检测
    successful_detections = [frame for frame in buffer if frame['success']]

    if len(successful_detections) < fast_multi_frame_detection['stable_threshold']:
        return None, 0.0

    # 计算平均中心点
    if successful_detections:
        recent_centers = [det['total_center'] for det in successful_detections[-2:] if det['total_center']]
        if recent_centers:
            avg_x = sum(center[0] for center in recent_centers) / len(recent_centers)
            avg_y = sum(center[1] for center in recent_centers) / len(recent_centers)

            # 计算置信度
            confidence = len(successful_detections) / len(buffer)

            return (avg_x, avg_y), confidence

    return None, 0.0

def should_send_fast_data(center, confidence):
    """判断是否应该发送数据 - Step8核心算法"""
    global fast_multi_frame_detection

    if center is None or confidence < fast_multi_frame_detection['confidence_threshold']:
        return False

    last_center = fast_multi_frame_detection['last_send_center']
    if last_center is None:
        return True

    # 检查位置变化
    distance = math.sqrt((center[0] - last_center[0])**2 + (center[1] - last_center[1])**2)
    return distance >= fast_multi_frame_detection['send_threshold']

# ==================== Step7核心：LAB阈值编辑界面（完整移植） ====================
def show_threshold_edit_interface(img):
    """显示LAB阈值编辑界面 - 完整移植Step7的成熟设计"""
    global threshold_edit_mode, threshold_current, sensor

    # 清空背景
    img.draw_rectangle(0, 0, picture_width, picture_height,
                     color=(40, 40, 80), thickness=1, fill=True)

    if threshold_edit_mode == "rect":
        try:
            # ==================== Step7核心：全屏阈值预览 + 画中画彩色原图 ====================
            preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)

            # 1. 全屏LAB二值化预览作为背景界面
            preview_binary = preview_img.binary([tuple(threshold_current)])
            preview_fullscreen = preview_binary.to_rgb565()

            # 缩放二值化预览图到全屏400x240
            if preview_fullscreen.width() != picture_width or preview_fullscreen.height() != picture_height:
                # 计算缩放比例，保持宽高比
                scale_x = preview_fullscreen.width() / picture_width
                scale_y = preview_fullscreen.height() / picture_height
                scale = max(scale_x, scale_y)

                if scale > 1:
                    # 需要缩小
                    scale_factor = int(scale) + 1
                    preview_fullscreen.midpoint_pool(scale_factor, scale_factor)

                # 如果还是不匹配，进行裁剪或填充
                if preview_fullscreen.width() > picture_width or preview_fullscreen.height() > picture_height:
                    # 居中裁剪
                    crop_x = max(0, (preview_fullscreen.width() - picture_width) // 2)
                    crop_y = max(0, (preview_fullscreen.height() - picture_height) // 2)
                    crop_w = min(picture_width, preview_fullscreen.width())
                    crop_h = min(picture_height, preview_fullscreen.height())
                    preview_fullscreen = preview_fullscreen.copy(roi=(crop_x, crop_y, crop_w, crop_h))

            # 将全屏二值化预览作为背景，直接替换img的内容
            img.draw_image(preview_fullscreen, 0, 0)

            # 2. 叠加小尺寸彩色原图窗口（画中画效果）
            color_original = preview_img.copy()  # 保留彩色原图

            # 设置小窗口尺寸为100x75像素
            target_width = 100
            target_height = 75

            # 计算缩放比例
            if color_original.width() > target_width or color_original.height() > target_height:
                scale_x = color_original.width() // target_width + 1
                scale_y = color_original.height() // target_height + 1
                scale = max(scale_x, scale_y)
                color_original.midpoint_pool(scale, scale)

            # 如果缩放后仍然过大，进行裁剪
            if color_original.width() > target_width or color_original.height() > target_height:
                crop_x = max(0, (color_original.width() - target_width) // 2)
                crop_y = max(0, (color_original.height() - target_height) // 2)
                crop_w = min(target_width, color_original.width())
                crop_h = min(target_height, color_original.height())
                color_original = color_original.copy(roi=(crop_x, crop_y, crop_w, crop_h))

            # 放置在右上角，留出边距
            pip_x = picture_width - color_original.width() - 5
            pip_y = 5

            # 绘制小窗口边框（画中画效果）
            border_thickness = 2
            img.draw_rectangle(pip_x - border_thickness, pip_y - border_thickness,
                             color_original.width() + 2 * border_thickness,
                             color_original.height() + 2 * border_thickness,
                             color=(255, 255, 255), thickness=border_thickness, fill=True)

            # 叠加彩色原图
            img.draw_image(color_original, pip_x, pip_y)

            # 3. 叠加界面信息（半透明背景）
            # 标题区域背景
            img.draw_rectangle(0, 0, picture_width, 25, color=(0, 0, 0), thickness=1, fill=True)
            img.draw_string_advanced(5, 5, 16, "矩形LAB阈值编辑 - 全屏预览", color=(255, 255, 0))

            # 参数信息区域背景
            info_text = f"LAB: ({threshold_current[0]},{threshold_current[1]},{threshold_current[2]},{threshold_current[3]},{threshold_current[4]},{threshold_current[5]})"
            img.draw_rectangle(0, picture_height - 20, picture_width, 20, color=(0, 0, 0), thickness=1, fill=True)
            img.draw_string_advanced(5, picture_height - 18, 12, info_text, color=(255, 255, 255))

            # 画中画标签
            img.draw_string_advanced(pip_x, pip_y + color_original.height() + 3, 8, "彩色原图", color=(255, 255, 255))

        except Exception as e:
            print(f"矩形阈值预览错误: {e}")
            # 错误时显示简单界面
            img.draw_rectangle(0, 0, picture_width, picture_height, color=(40, 40, 80), thickness=1, fill=True)
            img.draw_string_advanced(60, 10, 18, "矩形LAB阈值编辑", color=(255, 255, 0))
            img.draw_string_advanced(10, 35, 12, f"LAB: ({threshold_current[0]},{threshold_current[1]},{threshold_current[2]},{threshold_current[3]},{threshold_current[4]},{threshold_current[5]})", color=(255, 255, 255))

    elif threshold_edit_mode == "laser":
        try:
            # 激光点LAB阈值预览 - 保持原有布局（激光点检测效果更适合彩色显示）
            preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)
            current_lab_threshold = [tuple(threshold_current)]
            blobs = preview_img.find_blobs(current_lab_threshold, False,
                                         x_stride=1, y_stride=1,
                                         pixels_threshold=MIN_LASER_PIXELS, margin=False)
            for blob in blobs:
                preview_img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                                         color=(0, 255, 255), thickness=2, fill=False)
                preview_img.draw_circle(blob.x() + blob.w()//2, blob.y() + blob.h()//2, 3,
                                       color=(255, 0, 255), thickness=2)
            if preview_img.width() > 150:
                scale = preview_img.width() // 150 + 1
                preview_img.midpoint_pool(scale, scale)
            img.draw_image(preview_img, picture_width - preview_img.width() - 10, 10)
            img.draw_string_advanced(60, 10, 18, "激光点LAB阈值编辑", color=(255, 255, 0))
            img.draw_string_advanced(10, 35, 12, f"LAB: ({threshold_current[0]},{threshold_current[1]},{threshold_current[2]},{threshold_current[3]},{threshold_current[4]},{threshold_current[5]})", color=(255, 255, 255))
        except Exception as e:
            print(f"激光点阈值预览错误: {e}")
            img.draw_string_advanced(60, 10, 18, "激光点LAB阈值编辑", color=(255, 255, 0))

    # 显示编辑模式和统计信息
    img.draw_string_advanced(120, 200, 14, "编辑模式: " + threshold_edit_mode.upper() + " (LAB)", color=(0, 255, 255))

    # 显示字典状态
    rect_count = len(threshold_dict['rect']) if 'rect' in threshold_dict else 0
    laser_count = len(threshold_dict['laser']) if 'laser' in threshold_dict else 0
    img.draw_string_advanced(100, 180, 12, f"LAB字典: 矩形{rect_count}个 激光点{laser_count}个", color=(0, 255, 0))

    # 绘制边框
    img.draw_rectangle(2, 2, picture_width-4, picture_height-4,
                     color=(255, 255, 0), thickness=2, fill=False)

def draw_threshold_edit_buttons(img):
    """绘制LAB阈值编辑按钮 - 完整移植Step7的成熟设计"""
    global threshold_edit_mode, threshold_current

    button_color = (150, 150, 150)
    text_color = (0, 0, 0)
    active_color = (0, 255, 0)

    # ==================== Step7核心：顶部控制按钮 ====================
    # 返回按钮
    img.draw_rectangle(20, 20, 120, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(50, 30, 20, "返回", color=text_color)

    # 切换按钮
    switch_color = active_color if threshold_edit_mode == "laser" else button_color
    img.draw_rectangle(660, 20, 120, 40, color=switch_color, thickness=2, fill=True)
    img.draw_string_advanced(690, 30, 20, "切换", color=text_color)

    # ==================== Step7核心：底部控制按钮 ====================
    # 重置按钮
    img.draw_rectangle(20, 420, 100, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(45, 430, 18, "重置", color=text_color)

    # 保存按钮
    img.draw_rectangle(140, 420, 100, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(165, 430, 18, "保存", color=text_color)

    # 保存到字典按钮
    img.draw_rectangle(540, 420, 120, 40, color=(100, 200, 100), thickness=2, fill=True)
    img.draw_string_advanced(555, 430, 16, "保存到字典", color=text_color)

    # 从字典加载按钮
    img.draw_rectangle(680, 420, 120, 40, color=(100, 100, 200), thickness=2, fill=True)
    img.draw_string_advanced(695, 430, 16, "从字典加载", color=text_color)

    # 高性能识别按钮已移动到主界面

    # ==================== Step7核心：统一的6参数LAB编辑界面 ====================
    # 矩形和激光点都使用相同的6参数LAB编辑界面
    param_names = ["L_min", "L_max", "A_min", "A_max", "B_min", "B_max"]
    for i in range(6):
        y_pos = 80 + i * 55

        # 减少按钮 (左侧)
        img.draw_rectangle(20, y_pos, 60, 35, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(40, y_pos + 8, 16, "-", color=text_color)

        # 参数显示
        param_value = threshold_current[i] if i < len(threshold_current) else 0
        img.draw_string_advanced(100, y_pos + 8, 14, f"{param_names[i]}: {param_value}", color=(255, 255, 255))

        # 增加按钮 (右侧)
        img.draw_rectangle(720, y_pos, 60, 35, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(740, y_pos + 8, 16, "+", color=text_color)

def check_threshold_edit_click(x, y):
    """检测LAB阈值编辑界面的按钮点击 - 完整移植Step7的成熟设计"""
    global threshold_edit_mode, threshold_current, current_mode, RECT_THRESHOLD, LASER_THRESHOLD

    # ==================== Step7核心：顶部控制按钮 ====================
    # 返回按钮
    if 20 <= x <= 140 and 20 <= y <= 60:
        print("点击返回按钮")
        current_mode = 1
        return True

    # 切换按钮
    if 660 <= x <= 780 and 20 <= y <= 60:
        print("点击切换按钮")
        if threshold_edit_mode == "rect":
            threshold_edit_mode = "laser"
            threshold_current = list(LASER_THRESHOLD[0])  # 加载激光点LAB阈值
        else:
            threshold_edit_mode = "rect"
            threshold_current = list(RECT_THRESHOLD[0])   # 加载矩形LAB阈值
        print(f"切换到 {threshold_edit_mode} 模式，LAB阈值: {threshold_current}")
        return True

    # ==================== Step7核心：底部控制按钮 ====================
    # 重置按钮
    if 20 <= x <= 120 and 420 <= y <= 460:
        print("✅ 点击重置按钮")
        if threshold_edit_mode == "rect":
            threshold_current = [0, 100, -20, 20, -20, 20]  # 黑色矩形默认LAB阈值
            print("🔄 重置矩形LAB阈值为默认值")
        else:
            threshold_current = [47, 80, 9, 91, -55, 63]    # 激光点默认LAB阈值
            print("🔄 重置激光点LAB阈值为默认值")
        return True

    # 保存按钮
    if 140 <= x <= 240 and 420 <= y <= 460:
        print("✅ 点击保存按钮")
        if threshold_edit_mode == "rect":
            RECT_THRESHOLD[0] = tuple(threshold_current)
            print(f"💾 保存矩形LAB阈值到内存: {RECT_THRESHOLD[0]}")
        else:
            LASER_THRESHOLD[0] = tuple(threshold_current)
            print(f"💾 保存激光点LAB阈值到内存: {LASER_THRESHOLD[0]}")
        return True

    # 高性能识别按钮已移动到主界面

    # 保存到字典按钮
    if 540 <= x <= 660 and 420 <= y <= 460:
        print("✅ 点击保存到字典按钮")
        if save_threshold_to_dict():
            print("📁 LAB阈值已成功保存到字典")
        else:
            print("❌ 保存到字典失败")
        return True

    # 从字典加载按钮
    if 680 <= x <= 800 and 420 <= y <= 460:
        print("✅ 点击从字典加载按钮")
        if load_threshold_from_dict():
            print("📁 LAB阈值已从字典加载")
        else:
            print("❌ 从字典加载失败")
        return True

    # ==================== Step7核心：统一的6参数调整逻辑 ====================
    # 矩形和激光点都使用相同的6参数调整逻辑
    for i in range(6):
        y_pos = 80 + i * 55
        # 减少按钮
        if 20 <= x <= 80 and y_pos <= y <= y_pos + 35:
            if i < 2:  # L参数 0-100
                threshold_current[i] = max(0, threshold_current[i] - 2)
            else:  # A,B参数 -128到127
                threshold_current[i] = max(-128, threshold_current[i] - 2)
            print(f"减少参数{i} ({['L_min','L_max','A_min','A_max','B_min','B_max'][i]}): {threshold_current[i]}")
            return True
        # 增加按钮
        if 720 <= x <= 780 and y_pos <= y <= y_pos + 35:
            if i < 2:  # L参数 0-100
                threshold_current[i] = min(100, threshold_current[i] + 2)
            else:  # A,B参数 -128到127
                threshold_current[i] = min(127, threshold_current[i] + 2)
            print(f"增加参数{i} ({['L_min','L_max','A_min','A_max','B_min','B_max'][i]}): {threshold_current[i]}")
            return True

    return False

# ==================== Step7核心：字典管理功能 ====================
def save_threshold_to_dict():
    """保存LAB阈值到字典 - Step7成熟功能"""
    global threshold_edit_mode, threshold_current, threshold_dict
    try:
        if threshold_edit_mode == "rect":
            if tuple(threshold_current) not in threshold_dict['rect']:
                threshold_dict['rect'].append(tuple(threshold_current))
                print(f"📁 矩形LAB阈值已保存到字典: {threshold_current}")
                return True
            else:
                print("⚠️ 该矩形LAB阈值已存在于字典中")
                return False
        else:
            if tuple(threshold_current) not in threshold_dict['laser']:
                threshold_dict['laser'].append(tuple(threshold_current))
                print(f"📁 激光点LAB阈值已保存到字典: {threshold_current}")
                return True
            else:
                print("⚠️ 该激光点LAB阈值已存在于字典中")
                return False
    except Exception as e:
        print(f"❌ 保存到字典失败: {e}")
        return False

def load_threshold_from_dict():
    """从字典加载LAB阈值 - Step7成熟功能"""
    global threshold_edit_mode, threshold_current, threshold_dict
    try:
        if threshold_edit_mode == "rect":
            if threshold_dict['rect']:
                threshold_current = list(threshold_dict['rect'][-1])  # 加载最后一个
                print(f"📁 从字典加载矩形LAB阈值: {threshold_current}")
                return True
            else:
                print("⚠️ 矩形LAB阈值字典为空")
                return False
        else:
            if threshold_dict['laser']:
                threshold_current = list(threshold_dict['laser'][-1])  # 加载最后一个
                print(f"📁 从字典加载激光点LAB阈值: {threshold_current}")
                return True
            else:
                print("⚠️ 激光点LAB阈值字典为空")
                return False
    except Exception as e:
        print(f"❌ 从字典加载失败: {e}")
        return False

# ==================== Step8核心：串口通信功能 ====================
def init_uart():
    """初始化串口通信 - Step8核心功能"""
    global uart
    try:
        fpioa = FPIOA()
        fpioa.set_function(TX_PIN, FPIOA.UART2_TXD)
        fpioa.set_function(RX_PIN, FPIOA.UART2_RXD)
        uart = UART(SERIAL_PORT, BAUD_RATE)
        print(f"✅ 串口初始化成功: {BAUD_RATE}bps")
        return True
    except Exception as e:
        print(f"❌ 串口初始化失败: {e}")
        return False

def send_fast_coordinates(rect_center, screen_center):
    """发送快速坐标数据 - 修改为与STM32端完全一致的数据包格式"""
    global uart

    if uart is None:
        return False

    try:
        packet = []
        packet.extend(PACKET_HEADER)  # 0xAA, 0x55

        if rect_center is not None:
            rect_x, rect_y = int(rect_center[0]), int(rect_center[1])
        else:
            rect_x, rect_y = 0xFFFF, 0xFFFF

        screen_x, screen_y = int(screen_center[0]), int(screen_center[1])

        # 按照STM32端期望的数据包结构组装数据
        # 数据包格式：header[2] + rect_x[2] + rect_y[2] + screen_x[2] + screen_y[2] + checksum[1] + tail[2]
        packet.extend([(rect_x >> 8) & 0xFF, rect_x & 0xFF])        # 矩形中心X坐标（大端序）
        packet.extend([(rect_y >> 8) & 0xFF, rect_y & 0xFF])        # 矩形中心Y坐标（大端序）
        packet.extend([(screen_x >> 8) & 0xFF, screen_x & 0xFF])    # 屏幕中心X坐标（大端序）
        packet.extend([(screen_y >> 8) & 0xFF, screen_y & 0xFF])    # 屏幕中心Y坐标（大端序）

        # 计算校验和（从rect_x开始到screen_y结束的8个字节）
        checksum = sum(packet[2:]) % 256
        packet.append(checksum)
        packet.extend(PACKET_TAIL)  # 0x0D, 0x0A

        uart.write(bytes(packet))
        return True

    except Exception as e:
        print(f"❌ 串口发送失败: {e}")
        return False

# ==================== Step8核心：统计系统 ====================
def update_detection_stats(success):
    """更新检测统计 - Step8核心功能"""
    global rect_detection_stats

    rect_detection_stats['total_attempts'] += 1
    if success:
        rect_detection_stats['successful_detections'] += 1

    if rect_detection_stats['total_attempts'] > 0:
        rect_detection_stats['success_rate'] = (
            rect_detection_stats['successful_detections'] /
            rect_detection_stats['total_attempts'] * 100
        )

    rect_detection_stats['recent_attempts'].append(success)
    if len(rect_detection_stats['recent_attempts']) > 50:
        rect_detection_stats['recent_attempts'].pop(0)

    if len(rect_detection_stats['recent_attempts']) > 0:
        recent_successes = sum(rect_detection_stats['recent_attempts'])
        rect_detection_stats['recent_success_rate'] = (
            recent_successes / len(rect_detection_stats['recent_attempts']) * 100
        )

def update_performance_stats(fps, processing_time):
    """更新性能统计 - Step8核心功能"""
    global rect_detection_stats

    rect_detection_stats['fps_history'].append(fps)
    if len(rect_detection_stats['fps_history']) > 20:
        rect_detection_stats['fps_history'].pop(0)

    rect_detection_stats['processing_times'].append(processing_time)
    if len(rect_detection_stats['processing_times']) > 20:
        rect_detection_stats['processing_times'].pop(0)

    if rect_detection_stats['fps_history']:
        rect_detection_stats['avg_fps'] = sum(rect_detection_stats['fps_history']) / len(rect_detection_stats['fps_history'])

    if rect_detection_stats['processing_times']:
        rect_detection_stats['avg_processing_time'] = sum(rect_detection_stats['processing_times']) / len(rect_detection_stats['processing_times'])

def get_performance_status():
    """获取性能状态 - Step8核心功能"""
    global rect_detection_stats

    return {
        'fps': rect_detection_stats['avg_fps'],
        'fps_status': "优秀" if rect_detection_stats['avg_fps'] >= TARGET_FPS else "需优化",
        'processing_time': rect_detection_stats['avg_processing_time'],
        'time_status': "优秀" if rect_detection_stats['avg_processing_time'] <= TARGET_LATENCY else "需优化",
        'success_rate': rect_detection_stats['success_rate'],
        'rate_status': "优秀" if rect_detection_stats['success_rate'] >= TARGET_SUCCESS_RATE else "需优化"
    }

def get_detection_stats_text():
    """获取检测统计文本 - Step8核心功能"""
    global rect_detection_stats

    success_rate = rect_detection_stats['success_rate']
    recent_rate = rect_detection_stats['recent_success_rate']
    total_attempts = rect_detection_stats['total_attempts']

    return {
        'total_text': f"总计: {total_attempts}次",
        'success_rate_text': f"成功率: {success_rate:.1f}%",
        'recent_text': f"近50次: {recent_rate:.1f}%"
    }

# ==================== 主界面按钮功能 ====================
def draw_main_buttons(img):
    """绘制主界面按钮 - 支持四个按钮布局"""
    global current_mode

    button_colors = [
        BUTTON_ACTIVE_COLOR if current_mode == 1 else BUTTON_INACTIVE_COLOR,
        BUTTON_ACTIVE_COLOR if current_mode == 2 else BUTTON_INACTIVE_COLOR,
        BUTTON_ACTIVE_COLOR if current_mode == 3 else BUTTON_INACTIVE_COLOR,
        BUTTON_ACTIVE_COLOR if current_mode == 4 else BUTTON_INACTIVE_COLOR  # 新增第四个按钮
    ]

    button_texts = ["混合检测", "性能监控", "阈值编辑", "高性能识别"]  # 新增第四个按钮文本

    for i, (pos, color, text) in enumerate(zip([BUTTON1_POS, BUTTON2_POS, BUTTON3_POS, BUTTON4_POS],
                                              button_colors, button_texts)):
        # 为高性能识别按钮使用特殊颜色
        if i == 3:  # 第四个按钮（高性能识别）
            special_color = (200, 100, 200) if current_mode == 4 else (120, 60, 120)
            img.draw_rectangle(pos[0], pos[1], BUTTON_WIDTH, BUTTON_HEIGHT,
                              color=special_color, thickness=2, fill=True)
        else:
            img.draw_rectangle(pos[0], pos[1], BUTTON_WIDTH, BUTTON_HEIGHT,
                              color=color, thickness=2, fill=True)

        # 调整文字位置以适应不同长度的按钮文本
        text_offset_x = 15 if len(text) > 4 else 30
        img.draw_string_advanced(pos[0] + text_offset_x, pos[1] + 20, 18, text, color=BUTTON_TEXT_COLOR)

def check_main_button_click(x, y):
    """检测主界面按钮点击 - 支持四个按钮"""
    if (BUTTON1_POS[0] <= x <= BUTTON1_POS[0] + BUTTON_WIDTH and
        BUTTON1_POS[1] <= y <= BUTTON1_POS[1] + BUTTON_HEIGHT):
        return 1
    if (BUTTON2_POS[0] <= x <= BUTTON2_POS[0] + BUTTON_WIDTH and
        BUTTON2_POS[1] <= y <= BUTTON2_POS[1] + BUTTON_HEIGHT):
        return 2
    if (BUTTON3_POS[0] <= x <= BUTTON3_POS[0] + BUTTON_WIDTH and
        BUTTON3_POS[1] <= y <= BUTTON3_POS[1] + BUTTON_HEIGHT):
        return 3
    if (BUTTON4_POS[0] <= x <= BUTTON4_POS[0] + BUTTON_WIDTH and
        BUTTON4_POS[1] <= y <= BUTTON4_POS[1] + BUTTON_HEIGHT):
        return 4  # 新增第四个按钮（高性能识别）
    return None

# ==================== 主程序 ====================
try:
    print("第九步：混合优化系统测试开始")
    print("🎯 Step9核心组合：")
    print("   检测算法: Step8动态快速矩形识别（已验证高正确率）")
    print("   阈值编辑: Step7成熟LAB阈值界面（已验证稳定性）")
    print("   性能监控: Step8实时性能监控系统")
    print("   串口通信: Step8高频通信机制（30ms间隔）")
    print("🚀 性能目标：")
    print(f"   帧率: ≥{TARGET_FPS}FPS")
    print(f"   识别率: ≥{TARGET_SUCCESS_RATE}%")
    print(f"   延迟: ≤{TARGET_LATENCY}ms")

    # 初始化串口
    if not init_uart():
        print("⚠️  串口初始化失败，程序继续运行但无通信功能")

    # 传感器初始化
    sensor = Sensor(id=sensor_id)
    sensor.reset()
    sensor.set_framesize(width=picture_width, height=picture_height, chn=CAM_CHN_ID_0)
    sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)

    # 显示器初始化
    Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
    MediaManager.init()
    sensor.run()

    # 触摸屏初始化
    tp = TOUCH(0)
    clock = time.clock()

    print("🎉 系统初始化完成，开始主循环...")

    while True:
        clock.tick()
        os.exitpoint()

        try:
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            frame_count += 1
            frame_start_time = time.ticks_ms()

            # 处理触摸输入
            points = tp.read()
            if len(points) > 0:
                touch_x, touch_y = points[0].x, points[0].y

                if current_mode == 3:
                    # 阈值编辑模式 - 使用Step7的成熟界面
                    threshold_clicked = check_threshold_edit_click(touch_x, touch_y)
                    if threshold_clicked:
                        time.sleep_ms(200)
                else:
                    # 主界面模式
                    clicked_button = check_main_button_click(touch_x, touch_y)
                    if clicked_button:
                        current_mode = clicked_button
                        print(f"🔄 界面切换到模式: {current_mode}")
                        time.sleep_ms(200)

            # ==================== 核心功能处理 ====================
            if current_mode == 1:
                # 混合检测界面 - Step8核心算法
                img.draw_string_advanced(10, 10, 20, "K230混合优化系统", color=(255, 255, 0))
                img.draw_string_advanced(10, 30, 16, "Step9: 最佳功能组合", color=(0, 255, 255))

                # 显示系统状态
                img.draw_string_advanced(10, 50, 12, f"帧数: {frame_count}", color=(255, 255, 255))
                img.draw_string_advanced(10, 65, 12, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))

                # 串口状态
                uart_status = "✅连接" if uart else "❌断开"
                img.draw_string_advanced(300, 10, 12, f"串口: {uart_status}", color=(0, 255, 0))

                # ==================== Step8核心：快速矩形检测 ====================
                rect_success, rect_count, rect_centers, rect_total_center, processing_time = detect_rectangles_fast(img)

                # 更新多帧缓冲
                update_fast_multi_frame_buffer(rect_success, rect_centers, rect_total_center)

                # 获取稳定结果
                stable_center, stable_confidence = get_fast_stable_result()

                # 更新统计
                update_detection_stats(rect_success)
                update_performance_stats(clock.fps(), processing_time)

                # 显示检测结果
                if rect_success:
                    img.draw_string_advanced(10, 80, 12, f"快速检测成功! 用时: {processing_time}ms", color=(0, 255, 0))
                else:
                    img.draw_string_advanced(10, 80, 12, f"检测失败 用时: {processing_time}ms", color=(255, 0, 0))

                # 显示统计信息
                stats = get_detection_stats_text()
                img.draw_string_advanced(10, 95, 12, stats['total_text'], color=(255, 255, 255))
                img.draw_string_advanced(10, 110, 12, stats['success_rate_text'], color=(0, 255, 0))
                img.draw_string_advanced(10, 125, 12, stats['recent_text'], color=(0, 255, 0))

                # ==================== Step8核心：高频串口通信（修改为STM32兼容格式） ====================
                current_time = time.ticks_ms()
                if current_time - last_send_time >= SEND_INTERVAL:
                    if stable_center is not None and should_send_fast_data(stable_center, stable_confidence):
                        # 移除motion_state参数，使用STM32兼容的数据包格式
                        send_success = send_fast_coordinates(stable_center, (SCREEN_CENTER_X, SCREEN_CENTER_Y))
                        if send_success:
                            fast_multi_frame_detection['last_send_center'] = stable_center
                            # 减少输出频率
                            if frame_count % 50 == 0:
                                print(f"📡 发送坐标 - 置信度: {stable_confidence:.3f}")
                        last_send_time = current_time
                    else:
                        # 检测失败时可选择发送预测位置
                        predicted_pos, pred_confidence = get_predicted_position()
                        if predicted_pos and pred_confidence > 0.7:
                            # 移除motion_state参数，使用STM32兼容的数据包格式
                            send_fast_coordinates(predicted_pos, (SCREEN_CENTER_X, SCREEN_CENTER_Y))
                            if frame_count % 50 == 0:
                                print(f"📡 预测位置 - 置信度: {pred_confidence:.3f}")
                        last_send_time = current_time

            elif current_mode == 2:
                # 性能监控界面 - Step8核心功能
                img.draw_string_advanced(10, 10, 20, "性能监控面板", color=(255, 255, 0))

                perf = get_performance_status()

                # FPS监控
                fps_color = (0, 255, 0) if perf['fps_status'] == "优秀" else (255, 0, 0)
                img.draw_string_advanced(10, 40, 16, f"当前FPS: {perf['fps']:.1f} ({perf['fps_status']})", color=fps_color)
                img.draw_string_advanced(10, 60, 14, f"目标FPS: {TARGET_FPS}", color=(255, 255, 255))

                # 处理时间监控
                time_color = (0, 255, 0) if perf['time_status'] == "优秀" else (255, 0, 0)
                img.draw_string_advanced(10, 80, 16, f"处理时间: {perf['processing_time']:.1f}ms ({perf['time_status']})", color=time_color)
                img.draw_string_advanced(10, 100, 14, f"目标延迟: {TARGET_LATENCY}ms", color=(255, 255, 255))

                # 识别率监控
                rate_color = (0, 255, 0) if perf['rate_status'] == "优秀" else (255, 0, 0)
                img.draw_string_advanced(10, 120, 16, f"识别率: {perf['success_rate']:.1f}% ({perf['rate_status']})", color=rate_color)
                img.draw_string_advanced(10, 140, 14, f"目标识别率: {TARGET_SUCCESS_RATE}%", color=(255, 255, 255))

                # 系统状态
                img.draw_string_advanced(10, 160, 14, f"总帧数: {frame_count}", color=(255, 255, 255))
                img.draw_string_advanced(10, 175, 14, f"成功检测: {rect_detection_stats['successful_detections']}", color=(0, 255, 0))

                # ROI状态
                roi_x, roi_y, roi_width, roi_height = get_current_roi()
                roi_efficiency = (roi_width * roi_height) / (picture_width * picture_height) * 100
                img.draw_string_advanced(10, 190, 12, f"ROI效率: {roi_efficiency:.1f}% ({roi_width}x{roi_height})", color=(0, 255, 255))

                # 运动状态
                motion_state = get_motion_state()
                motion_text = ["静止", "慢速", "快速"][motion_state]
                img.draw_string_advanced(10, 205, 12, f"运动状态: {motion_text}", color=(255, 255, 0))

                # 预测状态
                predicted_pos, pred_confidence = get_predicted_position()
                if predicted_pos:
                    img.draw_string_advanced(10, 220, 12, f"预测置信度: {pred_confidence:.2f}", color=(255, 255, 0))

            elif current_mode == 3:
                # 阈值编辑界面 - Step7成熟设计
                show_threshold_edit_interface(img)

            elif current_mode == 4:
                # 高性能识别界面 - 预留功能
                img.draw_string_advanced(10, 10, 20, "高性能识别系统", color=(255, 255, 0))
                img.draw_string_advanced(10, 30, 16, "Step9: 高性能识别模式", color=(200, 100, 200))

                # 显示系统状态
                img.draw_string_advanced(10, 50, 12, f"帧数: {frame_count}", color=(255, 255, 255))
                img.draw_string_advanced(10, 65, 12, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))

                # 功能说明
                img.draw_string_advanced(10, 90, 14, "🚀 高性能识别功能开发中...", color=(255, 255, 0))
                img.draw_string_advanced(10, 110, 12, "此模式为后续高性能算法预留", color=(255, 255, 255))
                img.draw_string_advanced(10, 125, 12, "可在此基础上开发:", color=(255, 255, 255))
                img.draw_string_advanced(10, 140, 12, "• 深度学习检测算法", color=(0, 255, 255))
                img.draw_string_advanced(10, 155, 12, "• 多目标跟踪算法", color=(0, 255, 255))
                img.draw_string_advanced(10, 170, 12, "• 高精度定位算法", color=(0, 255, 255))
                img.draw_string_advanced(10, 185, 12, "• 实时性能优化", color=(0, 255, 255))

                # 当前使用基础检测算法
                img.draw_string_advanced(10, 210, 12, "当前使用Step8基础检测算法", color=(255, 255, 0))

                # 执行基础检测以保持功能性
                rect_success, rect_count, rect_centers, rect_total_center, processing_time = detect_rectangles_fast(img)
                if rect_success:
                    img.draw_string_advanced(10, 225, 12, f"检测成功 - 处理时间: {processing_time}ms", color=(0, 255, 0))
                else:
                    img.draw_string_advanced(10, 225, 12, f"检测失败 - 处理时间: {processing_time}ms", color=(255, 0, 0))

            # 图像放大和显示
            img = img.scale(SCALE_FACTOR, SCALE_FACTOR)
            if current_mode == 3:
                draw_threshold_edit_buttons(img)
            else:
                draw_main_buttons(img)
            Display.show_image(img, x=0, y=0)

            # 性能监控输出
            if frame_count % 200 == 0:
                mode_names = {1: "混合检测", 2: "性能监控", 3: "阈值编辑", 4: "高性能识别"}
                mode_name = mode_names.get(current_mode, "未知")
                perf = get_performance_status()
                stats = get_detection_stats_text()

                print(f"📊 Step9混合优化状态 - 帧数: {frame_count}")
                print(f"📱 当前模式: {current_mode} ({mode_name})")
                print(f"🚀 性能指标: FPS={perf['fps']:.1f}({perf['fps_status']}) 延迟={perf['processing_time']:.1f}ms({perf['time_status']})")
                print(f"📈 检测统计: {stats['success_rate_text']}({perf['rate_status']}), {stats['recent_text']}")

                # ROI效率
                roi_x, roi_y, roi_width, roi_height = get_current_roi()
                roi_efficiency = (roi_width * roi_height) / (picture_width * picture_height) * 100
                print(f"🎯 ROI效率: {roi_efficiency:.1f}%, 运动状态: {['静止', '慢速', '快速'][get_motion_state()]}")

                # 性能评估
                fps_ok = perf['fps_status'] == "优秀"
                time_ok = perf['time_status'] == "优秀"
                rate_ok = perf['rate_status'] == "优秀"

                if fps_ok and time_ok and rate_ok:
                    print("🎉 Step9混合优化目标全面达成！")
                else:
                    print("⚠️ 部分性能指标需要优化")

                print("=" * 60)

        except Exception as e:
            print(f"主循环错误: {e}")
            time.sleep_ms(50)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    print("清理资源...")

    # 输出最终性能报告
    if frame_count > 0:
        final_perf = get_performance_status()
        final_stats = get_detection_stats_text()

        print("📊 Step9混合优化最终报告:")
        print(f"   总帧数: {frame_count}")
        print(f"   平均FPS: {final_perf['fps']:.1f} ({final_perf['fps_status']}) (目标: ≥{TARGET_FPS})")
        print(f"   平均延迟: {final_perf['processing_time']:.1f}ms ({final_perf['time_status']}) (目标: ≤{TARGET_LATENCY}ms)")
        print(f"   {final_stats['success_rate_text']} ({final_perf['rate_status']}) (目标: ≥{TARGET_SUCCESS_RATE}%)")
        print(f"   {final_stats['recent_text']}")

        # ROI最终效率
        roi_x, roi_y, roi_width, roi_height = get_current_roi()
        roi_efficiency = (roi_width * roi_height) / (picture_width * picture_height) * 100
        print(f"   ROI最终效率: {roi_efficiency:.1f}%")

        # Step9混合优化评级
        fps_ok = final_perf['fps_status'] == "优秀"
        time_ok = final_perf['time_status'] == "优秀"
        rate_ok = final_perf['rate_status'] == "优秀"

        score = sum([fps_ok, time_ok, rate_ok])
        if score == 3:
            print("🏆 Step9混合优化评级: 优秀 - 所有目标达成")
        elif score >= 2:
            print("🥈 Step9混合优化评级: 良好 - 大部分目标达成")
        elif score >= 1:
            print("🥉 Step9混合优化评级: 合格 - 部分目标达成")
        else:
            print("📈 Step9混合优化评级: 需改进 - 多项指标未达标")

    if isinstance(sensor, Sensor):
        sensor.stop()
    if uart:
        uart.deinit()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()

    print("第九步混合优化系统测试完成")
    print("🎉 Step9核心组合总结:")
    print("   ✅ Step8动态快速矩形识别算法（高正确率保证）")
    print("   ✅ Step7成熟LAB阈值编辑界面（稳定性保证）")
    print("   ✅ Step8实时性能监控系统（性能反馈）")
    print("   ✅ Step8高频串口通信机制（实时性保证）")
    print("   ✅ 混合优化策略验证完成")
