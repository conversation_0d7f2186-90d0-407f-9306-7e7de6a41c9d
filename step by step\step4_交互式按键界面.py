# bilibili搜索学不会电磁场看教程
# K230矩形中心激光定位系统 - 第四步：交互式按键界面设计
# 基于step3的双重检测功能，添加三个可点击按键界面
# 核心特性：完全移植14_脱机调整阈值.py的成功阈值管理机制
# 解决方案：使用内存字典替代文件操作，彻底解决EINVAL错误

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from machine import TOUCH

# 常量定义
CAM_CHN_ID_0 = 0

# 阈值编辑全局变量
threshold_edit_mode = "rect"  # "rect" 或 "laser"
threshold_current = [0, 80, 0, 255, 0, 255]  # 当前编辑的阈值

# 基于14_脱机调整阈值.py的阈值存储机制
threshold_dict = {
    'rect': [(0, 80)],
    'laser': [(47, 80, 9, 91, -55, 63), (16, 37, 23, 74, -48, 52)]
}

sensor = None

try:
    print("第四步：交互式按键界面设计测试开始")
    print("基于step1的成功显示配置")
    print("🔧 增强功能：完整阈值编辑 + 基于14_脱机调整阈值.py的字典保存机制")

    # ==================== 检测参数设置 ====================
    # 矩形检测阈值
    RECT_THRESHOLD = [(0, 80)]
    MIN_RECT_AREA = 500

    # 激光点检测阈值 - LAB颜色空间
    LASER_THRESHOLD = [
        (47, 80, 9, 91, -55, 63),
        (16, 37, 23, 74, -48, 52)
    ]
    MIN_LASER_PIXELS = 20

    # ROI区域设置
    USE_ROI = False
    ROI_X = 50
    ROI_Y = 30
    ROI_WIDTH = 300
    ROI_HEIGHT = 180
    # ====================================================

    # 图像参数设置 - 完全继承step1的成功配置
    picture_width = 400
    picture_height = 240
    sensor_id = 2

    # 显示模式设置 - 完全继承step1的成功配置
    DISPLAY_MODE = "LCD"
    DISPLAY_WIDTH = 800
    DISPLAY_HEIGHT = 480

    # 图像放大设置 - 将400x240放大到800x480
    SCALE_FACTOR = 2.0
    SCALED_WIDTH = int(picture_width * SCALE_FACTOR)  # 800
    SCALED_HEIGHT = int(picture_height * SCALE_FACTOR)  # 480

    # 按键定义 - 水平排列在底部
    BUTTON_WIDTH = 150
    BUTTON_HEIGHT = 60
    BUTTON_Y = SCALED_HEIGHT - BUTTON_HEIGHT - 10  # 按键Y坐标（底部）
    BUTTON_SPACING = 200
    BUTTON_START_X = 50

    # 三个按键的位置
    BUTTON1_POS = (BUTTON_START_X, BUTTON_Y)
    BUTTON2_POS = (BUTTON_START_X + BUTTON_SPACING, BUTTON_Y)
    BUTTON3_POS = (BUTTON_START_X + BUTTON_SPACING * 2, BUTTON_Y)

    # 按键颜色定义
    BUTTON_ACTIVE_COLOR = (0, 255, 0)
    BUTTON_INACTIVE_COLOR = (80, 80, 80)
    BUTTON_TEXT_COLOR = (255, 255, 255)

    print(f"显示模式: {DISPLAY_MODE}")
    print(f"图像分辨率: {picture_width}x{picture_height}")
    print(f"显示分辨率: {DISPLAY_WIDTH}x{DISPLAY_HEIGHT}")
    print(f"放大后尺寸: {SCALED_WIDTH}x{SCALED_HEIGHT}")
    print(f"按键位置: Y={BUTTON_Y}")

    # 传感器初始化 - 完全继承step1的成功配置
    sensor = Sensor(id=sensor_id)
    sensor.reset()
    sensor.set_framesize(width=picture_width, height=picture_height, chn=CAM_CHN_ID_0)
    sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)

    # 显示器初始化 - 完全继承step1的成功配置
    Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)

    # 初始化媒体管理器 - 完全继承step1的成功配置
    MediaManager.init()
    sensor.run()

    # 触摸屏初始化
    tp = TOUCH(0)

    # 时钟对象用于FPS计算
    clock = time.clock()

    # 界面状态管理
    current_mode = 1

    # 矩形检测函数 - 简化版本
    def detect_rectangles(img):
        """检测图像中的矩形并计算中心点"""
        try:
            if USE_ROI:
                detect_img = img.copy(roi=(ROI_X, ROI_Y, ROI_WIDTH, ROI_HEIGHT))
            else:
                detect_img = img

            gray_img = detect_img.to_grayscale()
            binary_img = gray_img.binary(RECT_THRESHOLD)
            rects = binary_img.find_rects(threshold=MIN_RECT_AREA)

            centers = []
            if rects:
                for i, rect in enumerate(rects):
                    corners = rect.corners()
                    center_x = sum([corner[0] for corner in corners]) / 4
                    center_y = sum([corner[1] for corner in corners]) / 4

                    if USE_ROI:
                        global_center_x = ROI_X + center_x
                        global_center_y = ROI_Y + center_y
                    else:
                        global_center_x = center_x
                        global_center_y = center_y

                    centers.append((global_center_x, global_center_y))

                    # 绘制矩形边框
                    for j in range(4):
                        next_j = (j + 1) % 4
                        if USE_ROI:
                            x1, y1 = ROI_X + corners[j][0], ROI_Y + corners[j][1]
                            x2, y2 = ROI_X + corners[next_j][0], ROI_Y + corners[next_j][1]
                        else:
                            x1, y1 = corners[j][0], corners[j][1]
                            x2, y2 = corners[next_j][0], corners[next_j][1]
                        img.draw_line(x1, y1, x2, y2, color=(0, 255, 0), thickness=2)

                    # 绘制矩形中心点
                    img.draw_circle(int(global_center_x), int(global_center_y), 4,
                                   color=(255, 0, 0), thickness=2)
                    img.draw_string_advanced(int(global_center_x) - 15, int(global_center_y) - 25,
                                           10, f"R{i+1}", color=(255, 255, 0))

                # 计算总中心点
                if len(centers) > 1:
                    total_center_x = sum([cx for cx, cy in centers]) / len(centers)
                    total_center_y = sum([cy for cx, cy in centers]) / len(centers)
                    total_center = (total_center_x, total_center_y)
                    img.draw_circle(int(total_center_x), int(total_center_y), 8,
                                   color=(0, 255, 255), thickness=3)
                else:
                    total_center = centers[0] if centers else None

                return True, len(rects), centers, total_center

            return False, 0, [], None

        except Exception as e:
            print(f"矩形检测错误: {e}")
            return False, 0, [], None

    # 激光点检测函数 - 简化版本
    def detect_laser_point(img):
        """检测图像中的蓝紫激光点"""
        try:
            if USE_ROI:
                detect_img = img.copy(roi=(ROI_X, ROI_Y, ROI_WIDTH, ROI_HEIGHT))
            else:
                detect_img = img

            blobs = detect_img.find_blobs(LASER_THRESHOLD, False,
                                        x_stride=1, y_stride=1,
                                        pixels_threshold=MIN_LASER_PIXELS, margin=False)

            laser_centers = []
            if blobs:
                for i, blob in enumerate(blobs):
                    laser_x = blob.x() + blob.w() / 2
                    laser_y = blob.y() + blob.h() / 2

                    if USE_ROI:
                        global_laser_x = ROI_X + laser_x
                        global_laser_y = ROI_Y + laser_y
                        img.draw_rectangle(ROI_X + blob.x(), ROI_Y + blob.y(),
                                         blob.w(), blob.h(),
                                         color=(0, 0, 255), thickness=2, fill=False)
                    else:
                        global_laser_x = laser_x
                        global_laser_y = laser_y
                        img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                                         color=(0, 0, 255), thickness=2, fill=False)

                    laser_centers.append((global_laser_x, global_laser_y))
                    img.draw_circle(int(global_laser_x), int(global_laser_y), 3,
                                   color=(0, 0, 255), thickness=2)
                    img.draw_string_advanced(int(global_laser_x) - 15, int(global_laser_y) - 25,
                                           10, f"L{i+1}", color=(0, 0, 255))

                return True, len(blobs), laser_centers

            return False, 0, []

        except Exception as e:
            print(f"激光点检测错误: {e}")
            return False, 0, []

    # ==================== 基于14_脱机调整阈值.py的阈值字典保存和加载功能 ====================

    def save_threshold_to_dict():
        """基于14_脱机调整阈值.py的保存机制 - 保存到内存字典"""
        global threshold_dict, threshold_edit_mode, threshold_current

        try:
            print("开始保存阈值到字典...")

            if threshold_edit_mode == 'rect':
                # 保存矩形阈值到字典
                threshold_dict[threshold_edit_mode].append(threshold_current[:2])
                print("Success: 矩形阈值已保存到字典 " + str(threshold_current[:2]))
            elif threshold_edit_mode == 'laser':
                # 保存激光点阈值到字典 (转换为LAB格式)
                laser_threshold = [i - 127 if i > 127 else i for i in threshold_current]
                threshold_dict[threshold_edit_mode].append(laser_threshold)
                print("Success: 激光点阈值已保存到字典 " + str(laser_threshold))

            # 同时更新全局阈值变量
            update_global_thresholds_from_dict()

            print("Success: 阈值保存完成")
            return True

        except Exception as e:
            print("Error: 保存阈值失败 " + str(e))
            return False

    def update_global_thresholds_from_dict():
        """从字典更新全局阈值变量"""
        global RECT_THRESHOLD, LASER_THRESHOLD, threshold_dict

        try:
            # 更新矩形阈值
            if 'rect' in threshold_dict and len(threshold_dict['rect']) > 0:
                RECT_THRESHOLD[0] = tuple(threshold_dict['rect'][-1])  # 使用最新保存的阈值
                print("Update: 矩形阈值更新为 " + str(RECT_THRESHOLD[0]))

            # 更新激光点阈值
            if 'laser' in threshold_dict and len(threshold_dict['laser']) > 0:
                latest_laser = threshold_dict['laser'][-1]  # 使用最新保存的阈值
                # 转换回正常格式
                converted_laser = tuple([i + 127 if i < 0 else i for i in latest_laser])
                LASER_THRESHOLD[0] = converted_laser
                print("Update: 激光点阈值更新为 " + str(LASER_THRESHOLD[0]))

        except Exception as e:
            print("Error: 更新全局阈值失败 " + str(e))

    def load_threshold_from_dict():
        """基于14_脱机调整阈值.py的加载机制 - 从字典加载最新阈值"""
        global threshold_dict, threshold_current, threshold_edit_mode

        try:
            print("开始从字典加载阈值...")

            if threshold_edit_mode == 'rect':
                # 加载最新的矩形阈值
                if 'rect' in threshold_dict and len(threshold_dict['rect']) > 0:
                    latest_rect = threshold_dict['rect'][-1]  # 获取最新保存的阈值
                    threshold_current = list(latest_rect) + [0, 255, 0, 255]
                    print("Success: 矩形阈值已从字典加载 " + str(latest_rect))
                else:
                    print("Warning: 字典中无矩形阈值，使用默认值")

            elif threshold_edit_mode == 'laser':
                # 加载最新的激光点阈值
                if 'laser' in threshold_dict and len(threshold_dict['laser']) > 0:
                    latest_laser = threshold_dict['laser'][-1]  # 获取最新保存的阈值
                    # 转换回编辑格式
                    threshold_current = [i + 127 if i < 0 else i for i in latest_laser]
                    print("Success: 激光点阈值已从字典加载 " + str(latest_laser))
                else:
                    print("Warning: 字典中无激光点阈值，使用默认值")

            # 同时更新全局阈值变量
            update_global_thresholds_from_dict()

            print("Success: 阈值加载完成")
            return True

        except Exception as e:
            print("Error: 加载阈值失败 " + str(e))
            return False

    def get_default_thresholds():
        """获取默认阈值参数"""
        global RECT_THRESHOLD, LASER_THRESHOLD, MIN_RECT_AREA, MIN_LASER_PIXELS

        # 恢复默认值
        RECT_THRESHOLD = [(0, 80)]
        LASER_THRESHOLD = [
            (47, 80, 9, 91, -55, 63),
            (16, 37, 23, 74, -48, 52)
        ]
        MIN_RECT_AREA = 500
        MIN_LASER_PIXELS = 20

        print("🔄 已恢复默认阈值参数")

    # ==================== 阈值字典初始化 ====================
    print("📁 正在初始化阈值字典...")
    try:
        # 从字典更新全局阈值变量
        update_global_thresholds_from_dict()
        print("✅ 阈值字典初始化成功")

        print("当前阈值配置:")
        print("   矩形阈值字典: " + str(threshold_dict['rect']))
        print("   激光点阈值字典: " + str(threshold_dict['laser']))
        print("   当前矩形阈值: " + str(RECT_THRESHOLD[0]))
        print("   当前激光点阈值: " + str(LASER_THRESHOLD[0]))
    except Exception as e:
        print("Error: 阈值字典初始化失败 " + str(e))
        print("Warning: 使用默认阈值配置")

    # ==================== 阈值编辑功能实现 ====================

    def show_threshold_edit_interface(img):
        """显示完整的阈值编辑界面"""
        global threshold_edit_mode, threshold_current, RECT_THRESHOLD, LASER_THRESHOLD

        # 绘制背景
        img.draw_rectangle(0, 0, picture_width, picture_height,
                         color=(40, 40, 80), thickness=1, fill=True)

        # 根据当前编辑模式显示预览
        if threshold_edit_mode == "rect":
            # 矩形阈值预览
            try:
                # 获取预览图像
                preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)
                preview_gray = preview_img.to_grayscale()
                preview_binary = preview_gray.binary([tuple(threshold_current[:2])])

                # 缩小预览图像显示在右上角
                preview_small = preview_binary.to_rgb565()
                if preview_small.width() > 150:
                    scale = preview_small.width() // 150 + 1
                    preview_small.midpoint_pool(scale, scale)

                img.draw_image(preview_small, picture_width - preview_small.width() - 10, 10)

                # 显示当前矩形阈值
                img.draw_string_advanced(80, 10, 18, " 矩形阈值编辑", color=(255, 255, 0))
                img.draw_string_advanced(10, 35, 14, f"当前阈值: ({threshold_current[0]}, {threshold_current[1]})", color=(255, 255, 255))
                img.draw_string_advanced(10, 55, 12, f"范围: 0-255", color=(200, 200, 200))

            except Exception as e:
                print(f"矩形预览错误: {e}")
                img.draw_string_advanced(80, 10, 18, " 矩形阈值编辑", color=(255, 255, 0))
                img.draw_string_advanced(10, 35, 14, f"当前阈值: ({threshold_current[0]}, {threshold_current[1]})", color=(255, 255, 255))

        elif threshold_edit_mode == "laser":
            # 激光点阈值预览
            try:
                # 获取预览图像并检测激光点
                preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)
                current_lab_threshold = [tuple(threshold_current)]
                blobs = preview_img.find_blobs(current_lab_threshold, False,
                                             x_stride=1, y_stride=1,
                                             pixels_threshold=MIN_LASER_PIXELS, margin=False)

                # 在预览图像上绘制检测结果
                for blob in blobs:
                    preview_img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                                             color=(0, 255, 255), thickness=2, fill=False)
                    preview_img.draw_circle(blob.x() + blob.w()//2, blob.y() + blob.h()//2, 3,
                                           color=(255, 0, 255), thickness=2)

                # 缩小预览图像显示在右上角
                if preview_img.width() > 150:
                    scale = preview_img.width() // 150 + 1
                    preview_img.midpoint_pool(scale, scale)

                img.draw_image(preview_img, picture_width - preview_img.width() - 10, 10)

                # 显示当前激光点阈值
                img.draw_string_advanced(80, 10, 18, " 激光点阈值编辑", color=(255, 255, 0))
                img.draw_string_advanced(10, 35, 12, f"LAB: ({threshold_current[0]},{threshold_current[1]},{threshold_current[2]},{threshold_current[3]},{threshold_current[4]},{threshold_current[5]})", color=(255, 255, 255))
                img.draw_string_advanced(10, 55, 12, f"L:0-100, A/B:-128~127", color=(200, 200, 200))

            except Exception as e:
                print(f"激光点预览错误: {e}")
                img.draw_string_advanced(80, 10, 18, " 激光点阈值编辑", color=(255, 255, 0))
                lab_text = "LAB: (" + str(threshold_current[0]) + "," + str(threshold_current[1]) + "," + str(threshold_current[2]) + "," + str(threshold_current[3]) + "," + str(threshold_current[4]) + "," + str(threshold_current[5]) + ")"
                img.draw_string_advanced(10, 35, 12, lab_text, color=(255, 255, 255))

        # 显示编辑说明
        img.draw_string_advanced(140, 200, 14, "编辑模式: " + threshold_edit_mode.upper(), color=(0, 255, 255))


        # 显示字典状态 - 基于14_脱机调整阈值.py的机制
        rect_count = len(threshold_dict['rect']) if 'rect' in threshold_dict else 0
        laser_count = len(threshold_dict['laser']) if 'laser' in threshold_dict else 0
        img.draw_string_advanced(120, 180, 12, "字典状态: 矩形" + str(rect_count) + "个 激光点" + str(laser_count) + "个", color=(0, 255, 0))


        # 绘制边框
        img.draw_rectangle(2, 2, picture_width-4, picture_height-4,
                         color=(255, 255, 0), thickness=2, fill=False)

    def draw_threshold_edit_buttons(img):
        """在放大后的800x480图像上绘制阈值编辑按钮"""
        global threshold_edit_mode, threshold_current

        button_color = (150, 150, 150)
        text_color = (0, 0, 0)
        active_color = (0, 255, 0)

        # 顶部控制按钮
        # 返回按钮 (左上角)
        img.draw_rectangle(20, 20, 120, 40, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(50, 30, 20, "返回", color=text_color)

        # 切换按钮 (右上角)
        switch_color = active_color if threshold_edit_mode == "laser" else button_color
        img.draw_rectangle(660, 20, 120, 40, color=switch_color, thickness=2, fill=True)
        img.draw_string_advanced(690, 30, 20, "切换", color=text_color)

        # 底部控制按钮 - 重新布局为4个按钮
        # 重置按钮 (左下角)
        img.draw_rectangle(20, 420, 100, 40, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(45, 430, 18, "重置", color=text_color)

        # 保存按钮 (左下角第二个)
        img.draw_rectangle(140, 420, 100, 40, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(165, 430, 18, "保存", color=text_color)

        # 保存到字典按钮 (右下角第二个)
        img.draw_rectangle(540, 420, 120, 40, color=(100, 200, 100), thickness=2, fill=True)
        img.draw_string_advanced(555, 430, 16, "保存到字典", color=text_color)

        # 从字典加载按钮 (右下角)
        img.draw_rectangle(680, 420, 120, 40, color=(100, 100, 200), thickness=2, fill=True)
        img.draw_string_advanced(695, 430, 16, "从字典加载", color=text_color)

        # 参数调整按钮
        if threshold_edit_mode == "rect":
            # 矩形阈值只需要2个参数
            param_names = ["最小值", "最大值"]
            for i in range(2):
                y_pos = 100 + i * 80

                # 减少按钮 (左侧)
                img.draw_rectangle(20, y_pos, 80, 40, color=button_color, thickness=2, fill=True)
                img.draw_string_advanced(45, y_pos + 10, 20, "-", color=text_color)

                # 参数显示
                img.draw_string_advanced(120, y_pos + 10, 16, f"{param_names[i]}: {threshold_current[i]}", color=(255, 255, 255))

                # 增加按钮 (右侧)
                img.draw_rectangle(700, y_pos, 80, 40, color=button_color, thickness=2, fill=True)
                img.draw_string_advanced(725, y_pos + 10, 20, "+", color=text_color)

        elif threshold_edit_mode == "laser":
            # 激光点阈值需要6个参数
            param_names = ["L_min", "L_max", "A_min", "A_max", "B_min", "B_max"]
            for i in range(6):
                y_pos = 80 + i * 55

                # 减少按钮 (左侧)
                img.draw_rectangle(20, y_pos, 60, 35, color=button_color, thickness=2, fill=True)
                img.draw_string_advanced(40, y_pos + 8, 16, "-", color=text_color)

                # 参数显示
                img.draw_string_advanced(100, y_pos + 8, 14, f"{param_names[i]}: {threshold_current[i]}", color=(255, 255, 255))

                # 增加按钮 (右侧)
                img.draw_rectangle(720, y_pos, 60, 35, color=button_color, thickness=2, fill=True)
                img.draw_string_advanced(740, y_pos + 8, 16, "+", color=text_color)

    def check_threshold_edit_click(x, y):
        """检测阈值编辑界面的按钮点击"""
        global threshold_edit_mode, threshold_current, current_mode, RECT_THRESHOLD, LASER_THRESHOLD

        print(f"阈值编辑点击检测: ({x}, {y})")

        # 返回按钮
        if 20 <= x <= 140 and 20 <= y <= 60:
            print("点击返回按钮")
            current_mode = 1  # 返回基础界面
            return True

        # 切换按钮
        if 660 <= x <= 780 and 20 <= y <= 60:
            print("点击切换按钮")
            if threshold_edit_mode == "rect":
                threshold_edit_mode = "laser"
                threshold_current = list(LASER_THRESHOLD[0])  # 使用第一个激光点阈值
            else:
                threshold_edit_mode = "rect"
                threshold_current = list(RECT_THRESHOLD[0]) + [0, 255, 0, 255]  # 补齐6个参数
            return True

        # 重置按钮 (20, 420, 100x40)
        if 20 <= x <= 120 and 420 <= y <= 460:
            print("✅ 点击重置按钮")
            if threshold_edit_mode == "rect":
                threshold_current = [0, 80, 0, 255, 0, 255]
                print("🔄 重置矩形阈值为默认值")
            else:
                threshold_current = [47, 80, 9, 91, -55, 63]
                print("🔄 重置激光点阈值为默认值")
            return True

        # 保存按钮 (140, 420, 100x40)
        if 140 <= x <= 240 and 420 <= y <= 460:
            print("✅ 点击保存按钮")
            if threshold_edit_mode == "rect":
                RECT_THRESHOLD[0] = tuple(threshold_current[:2])
                print(f"💾 保存矩形阈值到内存: {RECT_THRESHOLD[0]}")
            else:
                LASER_THRESHOLD[0] = tuple(threshold_current)
                print(f"💾 保存激光点阈值到内存: {LASER_THRESHOLD[0]}")
            return True

        # 保存到字典按钮 (540, 420, 120x40)
        if 540 <= x <= 660 and 420 <= y <= 460:
            print("✅ 点击保存到字典按钮")
            # 保存到字典（基于14_脱机调整阈值.py的机制）
            if save_threshold_to_dict():
                print("📁 阈值已成功保存到字典")
            else:
                print("❌ 保存到字典失败")
            return True

        # 从字典加载按钮 (680, 420, 120x40)
        if 680 <= x <= 800 and 420 <= y <= 460:
            print("✅ 点击从字典加载按钮")
            if load_threshold_from_dict():
                print("📁 阈值已从字典加载")
            else:
                print("❌ 从字典加载失败")
            return True

        # 参数调整按钮
        if threshold_edit_mode == "rect":
            # 矩形阈值参数调整
            for i in range(2):
                y_pos = 100 + i * 80
                # 减少按钮
                if 20 <= x <= 100 and y_pos <= y <= y_pos + 40:
                    threshold_current[i] = max(0, threshold_current[i] - 5)
                    print(f"减少参数{i}: {threshold_current[i]}")
                    return True
                # 增加按钮
                if 700 <= x <= 780 and y_pos <= y <= y_pos + 40:
                    threshold_current[i] = min(255, threshold_current[i] + 5)
                    print(f"增加参数{i}: {threshold_current[i]}")
                    return True

        elif threshold_edit_mode == "laser":
            # 激光点阈值参数调整
            for i in range(6):
                y_pos = 80 + i * 55
                # 减少按钮
                if 20 <= x <= 80 and y_pos <= y <= y_pos + 35:
                    if i < 2:  # L参数 0-100
                        threshold_current[i] = max(0, threshold_current[i] - 2)
                    else:  # A,B参数 -128到127
                        threshold_current[i] = max(-128, threshold_current[i] - 2)
                    print(f"减少参数{i}: {threshold_current[i]}")
                    return True
                # 增加按钮
                if 720 <= x <= 780 and y_pos <= y <= y_pos + 35:
                    if i < 2:  # L参数 0-100
                        threshold_current[i] = min(100, threshold_current[i] + 2)
                    else:  # A,B参数 -128到127
                        threshold_current[i] = min(127, threshold_current[i] + 2)
                    print(f"增加参数{i}: {threshold_current[i]}")
                    return True

        return False
    # ====================================================

    # 绘制按键界面
    def draw_buttons(img):
        """绘制三个按键（在放大后的800x480图像上）"""
        # 按键1：基础部分
        button1_color = BUTTON_ACTIVE_COLOR if current_mode == 1 else BUTTON_INACTIVE_COLOR
        img.draw_rectangle(BUTTON1_POS[0], BUTTON1_POS[1], BUTTON_WIDTH, BUTTON_HEIGHT,
                          color=button1_color, thickness=3, fill=True)
        img.draw_string_advanced(BUTTON1_POS[0] + 30, BUTTON1_POS[1] + 20, 20,
                               "基础部分", color=BUTTON_TEXT_COLOR)

        # 按键2：进阶部分
        button2_color = BUTTON_ACTIVE_COLOR if current_mode == 2 else BUTTON_INACTIVE_COLOR
        img.draw_rectangle(BUTTON2_POS[0], BUTTON2_POS[1], BUTTON_WIDTH, BUTTON_HEIGHT,
                          color=button2_color, thickness=3, fill=True)
        img.draw_string_advanced(BUTTON2_POS[0] + 30, BUTTON2_POS[1] + 20, 20,
                               "进阶部分", color=BUTTON_TEXT_COLOR)

        # 按键3：阈值编辑
        button3_color = BUTTON_ACTIVE_COLOR if current_mode == 3 else BUTTON_INACTIVE_COLOR
        img.draw_rectangle(BUTTON3_POS[0], BUTTON3_POS[1], BUTTON_WIDTH, BUTTON_HEIGHT,
                          color=button3_color, thickness=3, fill=True)
        img.draw_string_advanced(BUTTON3_POS[0] + 30, BUTTON3_POS[1] + 20, 20,
                               "阈值编辑", color=BUTTON_TEXT_COLOR)

        # 绘制按键边框以增强可见性
        for pos in [BUTTON1_POS, BUTTON2_POS, BUTTON3_POS]:
            img.draw_rectangle(pos[0]-2, pos[1]-2, BUTTON_WIDTH+4, BUTTON_HEIGHT+4,
                             color=(255, 255, 255), thickness=2, fill=False)

    # 检测按键点击 - 增强调试版本
    def check_button_click(x, y):
        """检测点击了哪个按键"""
        print(f"触摸检测 - 坐标: ({x}, {y})")
        print(f"按键1区域: ({BUTTON1_POS[0]}, {BUTTON1_POS[1]}) 到 ({BUTTON1_POS[0] + BUTTON_WIDTH}, {BUTTON1_POS[1] + BUTTON_HEIGHT})")
        print(f"按键2区域: ({BUTTON2_POS[0]}, {BUTTON2_POS[1]}) 到 ({BUTTON2_POS[0] + BUTTON_WIDTH}, {BUTTON2_POS[1] + BUTTON_HEIGHT})")
        print(f"按键3区域: ({BUTTON3_POS[0]}, {BUTTON3_POS[1]}) 到 ({BUTTON3_POS[0] + BUTTON_WIDTH}, {BUTTON3_POS[1] + BUTTON_HEIGHT})")

        # 检查按键1
        if (BUTTON1_POS[0] <= x <= BUTTON1_POS[0] + BUTTON_WIDTH and
            BUTTON1_POS[1] <= y <= BUTTON1_POS[1] + BUTTON_HEIGHT):
            print("✅ 检测到按键1点击")
            return 1

        # 检查按键2
        if (BUTTON2_POS[0] <= x <= BUTTON2_POS[0] + BUTTON_WIDTH and
            BUTTON2_POS[1] <= y <= BUTTON2_POS[1] + BUTTON_HEIGHT):
            print("✅ 检测到按键2点击")
            return 2

        # 检查按键3
        if (BUTTON3_POS[0] <= x <= BUTTON3_POS[0] + BUTTON_WIDTH and
            BUTTON3_POS[1] <= y <= BUTTON3_POS[1] + BUTTON_HEIGHT):
            print("✅ 检测到按键3点击")
            return 3

        print("❌ 未检测到任何按键点击")
        return None

    print("系统初始化完成，开始交互式界面...")

    # 统计变量
    frame_count = 0

    # 主循环 - 基于step1的成功显示方式
    while True:
        clock.tick()
        os.exitpoint()

        try:
            # 捕获图像 - 完全继承step1的成功方式
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            frame_count += 1

            # 检测触摸输入 - 增强调试版本
            points = tp.read()
            if len(points) > 0:
                touch_x, touch_y = points[0].x, points[0].y
                print(f"🔍 触摸事件 - 原始坐标: ({touch_x}, {touch_y})")

                # 如果在阈值编辑界面，优先检测阈值编辑按钮
                if current_mode == 3:
                    threshold_clicked = check_threshold_edit_click(touch_x, touch_y)
                    if threshold_clicked:
                        time.sleep_ms(300)  # 防止重复点击
                    else:
                        print(f"⚠️ 触摸位置不在阈值编辑按钮区域内")
                else:
                    # 其他界面检测主按键
                    clicked_button = check_button_click(touch_x, touch_y)

                    if clicked_button:
                        old_mode = current_mode
                        current_mode = clicked_button
                        print(f"🔄 界面切换: {old_mode} → {current_mode}")

                        # 根据模式显示切换信息
                        if current_mode == 1:
                            print("📱 切换到基础部分界面")
                        elif current_mode == 2:
                            print("📱 切换到进阶部分界面")
                        elif current_mode == 3:
                            print("📱 切换到阈值编辑界面")
                            # 初始化阈值编辑参数
                            threshold_edit_mode = "rect"
                            threshold_current = list(RECT_THRESHOLD[0]) + [0, 255, 0, 255]

                        time.sleep_ms(300)  # 防止重复点击
                    else:
                        print(f"⚠️ 触摸位置不在任何按键区域内")

            # 根据当前模式显示不同界面（在原始400x240图像上）
            if current_mode == 1:
                # 基础界面 - 双重检测功能
                img.draw_string_advanced(10, 10, 22, "K230激光定位系统", color=(255, 255, 0))
                img.draw_string_advanced(10, 32, 18, "基础部分：矩形+激光点检测", color=(0, 255, 255))
                img.draw_string_advanced(10, 52, 14, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))

                # 执行检测
                rect_success, rect_count, rect_centers, rect_total_center = detect_rectangles(img)
                laser_success, laser_count, laser_centers = detect_laser_point(img)

                # 显示检测结果
                info_y = 75
                if rect_success:
                    img.draw_string_advanced(10, info_y, 14, f"矩形检测成功! 数量: {rect_count}", color=(0, 255, 0))
                    info_y += 16
                if laser_success:
                    img.draw_string_advanced(10, info_y, 14, f"激光点检测成功! 数量: {laser_count}", color=(0, 0, 255))

            elif current_mode == 2:
                # 进阶界面
                img.draw_string_advanced(10, 10, 22, "K230激光定位系统", color=(255, 255, 0))
                img.draw_string_advanced(10, 32, 18, "进阶部分：功能开发中", color=(255, 165, 0))
                img.draw_string_advanced(10, 52, 14, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
                img.draw_string_advanced(50, 100, 20, "此功能正在开发中", color=(255, 255, 0))
                img.draw_string_advanced(50, 130, 16, "敬请期待后续更新", color=(255, 255, 255))

            elif current_mode == 3:
                # 阈值编辑界面 - 完整功能实现
                show_threshold_edit_interface(img)

            # 图像放大到800x480 - 充分利用LCD屏幕空间
            img = img.scale(SCALE_FACTOR, SCALE_FACTOR)

            # 根据当前模式绘制不同的按键界面
            if current_mode == 3:
                # 阈值编辑界面绘制专用按钮
                draw_threshold_edit_buttons(img)
            else:
                # 其他界面绘制主按键
                draw_buttons(img)

            # 显示放大后的图像 - 填满整个LCD屏幕
            Display.show_image(img, x=0, y=0)

            # 每100帧输出一次状态信息
            if frame_count % 100 == 0:
                mode_names = {1: "基础部分", 2: "进阶部分", 3: "阈值编辑"}
                mode_name = mode_names.get(current_mode, "未知")
                print(f"📊 界面状态 - 帧数: {frame_count}, FPS: {clock.fps():.1f}")
                print(f"📱 当前模式: {current_mode} ({mode_name})")
                print(f"🎯 按键状态: 1={'✅激活' if current_mode==1 else '⭕未激活'} | 2={'✅激活' if current_mode==2 else '⭕未激活'} | 3={'✅激活' if current_mode==3 else '⭕未激活'}")
                print("=" * 50)

        except Exception as e:
            print(f"主循环错误: {e}")
            time.sleep_ms(100)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
    # traceback模块在MicroPython中不可用，使用简单错误输出
finally:
    print("清理资源...")
    # 完全继承step1的成功清理方式
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
    print("第四步测试完成")
