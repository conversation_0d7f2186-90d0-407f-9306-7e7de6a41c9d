# K230 矩形中心激光定位系统 - 分步开发

## 开发计划

### 第一步：基础显示功能

- 结合 05 色块追踪与线段识别.py 和显示参考.py
- 实现 LCD 图像显示功能
- 验证摄像头和显示器工作正常

### 第二步：矩形检测功能

- 实现黑色矩形框检测
- 计算矩形中心点
- 在 LCD 上显示检测结果

### 第三步：激光点检测功能

- 实现红色激光点检测
- 基于 LAB 颜色空间
- 显示激光点位置

### 第四步：PID 控制系统

- 实现 PID 控制算法
- 计算激光点到目标的误差
- 输出控制量

### 第五步：步进电机控制

- 实现串口通信
- 发送步进电机控制指令
- 返回控制状态数据

### 第六步：脱机阈值调整

- 实现触摸屏交互
- 阈值实时调整功能
- 用户界面设计

### 第七步：系统集成

- 整合所有功能模块
- 状态管理和切换
- 最终测试和优化

## 当前进度

- [x] 创建开发文件夹
- [x] 第一步：基础显示功能 ✅ (重新构建)
- [x] 第二步：矩形检测功能 ✅
- [x] 第三步：矩形和激光点检测双重功能 ✅
- [x] 第四步：交互式按键界面设计 ✅
- [ ] 第五步：PID 控制系统
- [ ] 第五步：步进电机控制
- [ ] 第六步：脱机阈值调整
- [ ] 第七步：系统集成

## 已完成功能详情

### 第一步：基础显示功能 ✅ (重新构建)

- **文件**: `step1_基础显示功能.py`
- **功能**: 基于 `电赛识别基础部分.py` 的成功显示配置，确保 LCD 屏幕正常显示
- **技术配置**:
  - 图像分辨率：400x240 (经过验证的稳定配置)
  - 显示分辨率：800x480 (适配 3.1 寸 LCD 屏幕)
  - 显示驱动：ST7701 (经过验证的 LCD 驱动)
  - 像素格式：RGB565 (标准颜色格式)
  - 居中显示：图像在 LCD 屏幕中央显示
- **显示内容**:
  - 系统标题和功能信息
  - 实时 FPS 和帧计数显示
  - 测试图形：十字线、圆圈、旋转线条
  - 四角标记验证边界绘制
  - 状态信息和下一步提示
- **验证功能**:
  - LCD 屏幕正常显示实时摄像头图像
  - 文字信息清晰可读
  - 图形绘制功能正常
  - 动画效果流畅
  - 详细的错误处理和状态输出
- **配套文档**: `step1_使用说明.md` - 详细使用说明和故障排除指南

### 第二步：矩形检测功能 ✅

- **文件**: `step2_矩形检测功能.py`
- **功能**: 基于 step1 的成功显示配置，添加矩形检测和中心点计算功能
- **技术配置**:
  - 完全继承 step1 的稳定显示配置（400x240 图像，800x480 显示，ST7701 驱动）
  - 矩形检测阈值：[(0, 80)] (可调节)
  - 最小矩形面积：500 像素 (可调节)
  - 可选 ROI 区域限制功能
- **检测功能**:
  - 检测黑色矩形框
  - 支持多矩形同时检测
  - 计算每个矩形的中心点坐标
  - 计算所有矩形的总中心点
  - 返回相对于原始图像的绝对坐标
- **可视化显示**:
  - 绿色边框标记检测到的矩形
  - 红色圆点标记矩形中心点
  - 青色大圆标记总中心点
  - 黄色文字显示坐标和编号
  - 实时显示检测状态和参数信息
- **参数调整**:
  - 可调节的检测阈值 (RECT_THRESHOLD)
  - 可调节的最小面积 (MIN_RECT_AREA)
  - 可选择性启用 ROI 功能
  - 代码中提供清晰的参数调整说明
- **配套文档**: `step2_使用说明.md` - 完整使用说明和参数调整指南

### 第三步：矩形和激光点检测双重功能 ✅

- **文件**: `step3_矩形和激光点检测.py`
- **功能**: 基于 step2 的矩形检测功能，添加蓝紫激光点检测功能，实现双重检测
- **技术配置**:
  - 完全继承 step1 的稳定显示配置（400x240 图像，800x480 显示，ST7701 驱动）
  - 矩形检测：灰度二值化，阈值 [(0, 80)]，最小面积 500 像素
  - 激光点检测：LAB 颜色空间，支持蓝紫激光点检测
  - 可选 ROI 区域限制功能
- **任务 1：优化矩形中心点坐标返回**:
  - 清晰返回每个矩形的中心点坐标
  - 控制台明确显示检测数量和对应坐标
  - 分别显示每个矩形中心点和总中心点坐标
  - 坐标相对于原始 400x240 图像的绝对像素坐标
- **任务 2：添加激光点检测功能**:
  - 使用 LAB 颜色空间进行蓝紫激光点检测
  - 可调节的 LAB 颜色阈值参数
  - 检测并返回激光点的中心坐标
  - 不同颜色标记区分矩形和激光点检测结果
- **可视化显示**:
  - 绿色边框 + 红色圆点：矩形检测结果
  - 蓝色边框 + 蓝色圆点：激光点检测结果
  - 青色大圆：矩形总中心点
  - 实时显示坐标数值和检测统计
- **参数调整**:
  - 矩形检测阈值：RECT_THRESHOLD
  - 激光点 LAB 阈值：LASER_THRESHOLD
  - 最小面积/像素数过滤
  - 详细的 LAB 颜色空间调整指南
- **配套文档**: `step3_使用说明.md` - 双重检测功能使用说明和 LAB 参数调整指南

### 第四步：交互式按键界面设计 ✅

- **文件**: `step4_交互式按键界面.py`
- **功能**: 基于 step3 的双重检测功能，添加三个可点击按键界面，实现交互式操作
- **技术配置**:
  - 完全继承 step1 的稳定显示配置（400x240 图像，800x480 显示，ST7701 驱动）
  - 触摸屏支持：TOUCH(0) 初始化
  - 按键布局：位于图像右侧空余区域，120x50 像素按键
  - 状态管理：激活状态（绿色）、未激活状态（灰色）
- **三个按键功能**:
  - **按键 1：基础部分** - 显示 step3 的双重检测界面（矩形+激光点检测）
  - **按键 2：进阶部分** - 预留功能界面，显示开发中提示和计划功能
  - **按键 3：阈值编辑界面** - 实时调整矩形和激光点检测阈值，参考 14\_脱机调整阈值.py
- **界面布局**:
  - 图像显示区域：居中显示 400x240 图像
  - 按键区域：位于图像右侧，起始位置 (600, 100)
  - 按键间距：70 像素，确保易于操作
- **交互功能**:
  - 精确的触摸检测和按键响应
  - 即时界面切换，300ms 防重复点击
  - 清晰的视觉反馈和状态指示
  - 完全兼容现有的矩形和激光点检测功能
- **阈值编辑功能**:
  - 显示当前矩形和激光点检测阈值
  - 实时预览阈值调整效果
  - 支持矩形阈值和 LAB 激光点阈值的独立调整
  - 提供保存/恢复阈值设置功能
- **配套文档**: `step4_使用说明.md` - 交互式界面使用说明和操作指南
