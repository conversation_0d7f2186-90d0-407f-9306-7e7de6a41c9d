# Requirements Document

## Introduction

This feature extends the existing step4 interactive interface program with background serial communication capabilities. The system will maintain all existing functionality (threshold editing, dictionary saving, interface switching) while adding real-time coordinate transmission to STM32 microcontroller via UART. The serial communication runs in the background without affecting the user interface experience.

## Requirements

### Requirement 1

**User Story:** As a developer using the K230 laser positioning system, I want the system to automatically transmit rectangle center coordinates and screen center coordinates to STM32 via serial port, so that I can integrate the vision system with external control hardware.

#### Acceptance Criteria

1. WHEN the system detects rectangles THEN it SHALL transmit the actual rectangle center coordinates via serial port
2. WHEN no rectangles are detected THEN the system SHALL transmit default coordinate values (240, 120) via serial port
3. WHEN transmitting coordinates THEN the system SHALL send both rectangle center and screen center coordinates in each packet
4. WHEN transmitting data THEN the system SHALL use 115200 baud rate with 8N1 configuration
5. WHEN sending coordinates THEN the system SHALL format each coordinate as two uint8_t values to support 0-480 pixel range

### Requirement 2

**User Story:** As a developer integrating the vision system, I want a standardized data packet format with error checking, so that I can reliably receive and parse coordinate data on the STM32 side.

#### Acceptance Criteria

1. W<PERSON><PERSON> transmitting data THEN the system SHALL use the packet format: Header(2 bytes) + Coordinates(8 bytes) + Checksum(1 byte) + Tail(2 bytes)
2. WHEN encoding coordinates THEN the system SHALL split each 16-bit coordinate into two 8-bit values (high byte, low byte)
3. WHEN creating packets THEN the system SHALL include a checksum for data integrity verification
4. WHEN transmitting THEN the system SHALL use fixed packet headers and tails for synchronization
5. WHEN sending coordinate data THEN the system SHALL transmit 4 coordinates: rect_x, rect_y, screen_center_x, screen_center_y

### Requirement 3

**User Story:** As a user of the step4 interface, I want the serial communication to run transparently in the background, so that I can continue using all existing features without any interface changes or performance impact.

#### Acceptance Criteria

1. WHEN serial communication is active THEN the system SHALL maintain all step4 functionality without modification
2. WHEN using threshold editing THEN the interface SHALL respond identically to step4 behavior
3. WHEN switching between interface modes THEN the system SHALL preserve all existing button behaviors
4. WHEN serial communication encounters errors THEN the system SHALL continue normal operation without crashing
5. WHEN displaying the interface THEN the system SHALL maintain the same FPS performance as step4

### Requirement 4

**User Story:** As a developer working with STM32, I want corresponding receiver code that can parse the coordinate data packets, so that I can process the vision system output in my embedded application.

#### Acceptance Criteria

1. WHEN receiving serial data THEN the STM32 code SHALL parse the packet format correctly
2. WHEN packet headers are detected THEN the STM32 SHALL extract the 8-byte coordinate data
3. WHEN checksum verification is performed THEN the STM32 SHALL validate data integrity
4. WHEN coordinates are extracted THEN the STM32 SHALL reconstruct 16-bit values from 8-bit pairs
5. WHEN invalid packets are received THEN the STM32 SHALL discard corrupted data and continue listening

### Requirement 5

**User Story:** As a system integrator, I want proper error handling and status indication for the serial communication, so that I can diagnose connection issues and ensure reliable operation.

#### Acceptance Criteria

1. WHEN serial port initialization fails THEN the system SHALL continue operation with serial communication disabled
2. WHEN transmission errors occur THEN the system SHALL log errors without affecting the main interface
3. WHEN serial port is disconnected THEN the system SHALL handle the disconnection gracefully
4. WHEN debugging is needed THEN the system SHALL provide optional status messages for serial communication
5. WHEN serial communication is active THEN the system SHALL indicate the communication status appropriately