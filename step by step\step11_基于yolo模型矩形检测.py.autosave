# bilibili搜索学不会电磁场看教程
# K230矩形中心激光定位系统 - 第十一步：基于YOLO模型矩形检测
# 基于yolo/det_video.py进行修改和扩展，集成STM32串口通信功能
#
# Step11核心功能：
# 1. YOLO模型检测 - 使用det_video.py的完整YOLO检测流程
# 2. 矩形中心点计算 - 计算检测到的矩形中心点坐标
# 3. STM32串口通信 - 严格按照C结构体协议发送数据
# 4. 可视化显示 - 矩形中心点、屏幕中心点、连线显示
# 5. 性能监控 - 实时FPS和检测状态监控
#
# 设计目标：
# - 保持det_video.py的YOLO检测逻辑不变
# - 添加STM32兼容的串口通信功能
# - 提供清晰的可视化反馈
# - 确保系统运行流畅（≥15FPS）

import os
import ujson
import aicube
from media.sensor import *
from media.display import *
from media.media import *
from time import *
import nncase_runtime as nn
import ulab.numpy as np
import time
import utime
import image
import random
import gc
from machine import UART
from machine import FPIOA

# ==================== Step11新增：STM32串口通信配置 ====================
# 串口配置
SERIAL_CONFIG = {
    'port': UART.UART2,
    'baudrate': 115200,
    'tx_pin': 11,
    'rx_pin': 12,
    'packet_header': [0xAA, 0x55],     # 包头
    'packet_tail': [0x0D, 0x0A],       # 包尾
    'send_interval': 20,               # 发送间隔(ms)
    'screen_center_x': 585,            # 屏幕中心X坐标
    'screen_center_y': 440,            # 屏幕中心Y坐标
}

# 可视化配置
VISUALIZATION_CONFIG = {
    'draw_center_points': True,        # 绘制中心点
    'draw_screen_center': True,        # 绘制屏幕中心
    'draw_connection_line': True,      # 绘制连接线
    'draw_info_panel': True,           # 绘制信息面板
    'center_color': (255, 0, 0),      # 矩形中心点颜色（红色）
    'screen_center_color': (255, 255, 0),  # 屏幕中心颜色（黄色）
    'line_color': (0, 255, 255),      # 连接线颜色（青色）
}

# 全局变量
uart = None
last_send_time = 0
frame_count = 0
detection_count = 0

# FPS计算相关全局变量
fps_start_time = 0
fps_frame_count = 0
last_fps = 0.0

# ==================== 继承det_video.py的配置 ====================
# 调试模式: True 会在屏幕上绘制检测框和信息, False 则不绘制
debug_mode = True

# 显示模式: "hdmi" 或 "lcd"
display_mode = "lcd"

# 常量定义（继承det_video.py）
def ALIGN_UP(x, align):
    return ((x + align - 1) & ~(align - 1))

# 摄像头通道定义
CAM_CHN_ID_0 = 0
CAM_CHN_ID_2 = 2

# 注意：像素格式常量和显示层常量由media.sensor和media.display自动导入

if display_mode == "lcd":
    DISPLAY_WIDTH = ALIGN_UP(800, 16)
    DISPLAY_HEIGHT = 480
else:
    DISPLAY_WIDTH = ALIGN_UP(1920, 16)
    DISPLAY_HEIGHT = 1080

OUT_RGB888P_WIDTH = ALIGN_UP(1080, 16)
OUT_RGB888P_HEIGH = 720

# 颜色盘（继承det_video.py）
color_four = [(255, 220, 20, 60), (255, 119, 11, 32), (255, 0, 0, 142), (255, 0, 0, 230),
        (255, 106, 0, 228), (255, 0, 60, 100), (255, 0, 80, 100), (255, 0, 0, 70),
        (255, 0, 0, 192), (255, 250, 170, 30), (255, 100, 170, 30), (255, 220, 220, 0),
        (255, 175, 116, 175), (255, 250, 0, 30), (255, 165, 42, 42), (255, 255, 77, 255),
        (255, 0, 226, 252), (255, 182, 182, 255), (255, 0, 82, 0), (255, 120, 166, 157),
        (255, 110, 76, 0), (255, 174, 57, 255), (255, 199, 100, 0), (255, 72, 0, 118),
        (255, 255, 179, 240), (255, 0, 125, 92), (255, 209, 0, 151), (255, 188, 208, 182),
        (255, 0, 220, 176), (255, 255, 99, 164), (255, 92, 0, 73), (255, 133, 129, 255),
        (255, 78, 180, 255), (255, 0, 228, 0), (255, 174, 255, 243), (255, 45, 89, 255),
        (255, 134, 134, 103), (255, 145, 148, 174), (255, 255, 208, 186),
        (255, 197, 226, 255), (255, 171, 134, 1), (255, 109, 63, 54), (255, 207, 138, 255),
        (255, 151, 0, 95), (255, 9, 80, 61), (255, 84, 105, 51), (255, 74, 65, 105),
        (255, 166, 196, 102), (255, 208, 195, 210), (255, 255, 109, 65), (255, 0, 143, 149),
        (255, 179, 0, 194), (255, 209, 99, 106), (255, 5, 121, 0), (255, 227, 255, 205),
        (255, 147, 186, 208), (255, 153, 69, 1), (255, 3, 95, 161), (255, 163, 255, 0),
        (255, 119, 0, 170), (255, 0, 182, 199), (255, 0, 165, 120), (255, 183, 130, 88),
        (255, 95, 32, 0), (255, 130, 114, 135), (255, 110, 129, 133), (255, 166, 74, 118),
        (255, 219, 142, 185), (255, 79, 210, 114), (255, 178, 90, 62), (255, 65, 70, 15),
        (255, 127, 167, 115), (255, 59, 105, 106), (255, 142, 108, 45), (255, 196, 172, 0),
        (255, 95, 54, 80), (255, 128, 76, 255), (255, 201, 57, 1), (255, 246, 0, 122),
        (255, 191, 162, 208)]

root_path = "/sdcard/new/"
config_path = root_path + "deploy_config.json"
deploy_conf = {}

# ==================== 继承det_video.py的工具类 ====================
class ScopedTiming:
    def __init__(self, info="", enable_profile=True):
        self.info = info
        self.enable_profile = enable_profile

    def __enter__(self):
        if self.enable_profile:
            self.start_time = time.time_ns()
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        if self.enable_profile:
            elapsed_time = time.time_ns() - self.start_time
            print(f"{self.info} took {elapsed_time / 1000000:.2f} ms")

def read_deploy_config(config_path):
    """读取配置文件 - 继承det_video.py"""
    with open(config_path, 'r') as json_file:
        try:
            config = ujson.load(json_file)
        except ValueError as e:
            print("JSON parsing error:", e)
            return None
    return config

# ==================== Step11新增：STM32串口通信模块 ====================
def init_uart():
    """初始化串口通信 - Step11核心功能（修复FPIOA配置）"""
    global uart
    try:
        fpioa = FPIOA()
        # 修复：使用正确的FPIOA属性名称
        fpioa.set_function(SERIAL_CONFIG['tx_pin'], FPIOA.UART2_TXD)
        fpioa.set_function(SERIAL_CONFIG['rx_pin'], FPIOA.UART2_RXD)

        # 修复：使用简化的UART初始化方式
        uart = UART(SERIAL_CONFIG['port'], SERIAL_CONFIG['baudrate'])
        print(f"✅ 串口初始化成功: UART{SERIAL_CONFIG['port']}, 波特率: {SERIAL_CONFIG['baudrate']}")
        return True
    except Exception as e:
        print(f"❌ 串口初始化失败: {e}")
        return False

def send_coordinates_to_stm32(rect_x, rect_y):
    """发送坐标数据到STM32 - 严格按照C结构体格式"""
    global uart

    if uart is None:
        return False

    try:
        # 严格按照STM32 C结构体定义组装数据包
        packet = []

        # 包头 (2字节)
        packet.extend(SERIAL_CONFIG['packet_header'])  # 0xAA, 0x55

        # 矩形中心坐标 (4字节，大端序)
        packet.extend([(rect_x >> 8) & 0xFF, rect_x & 0xFF])        # rect_x (大端序)
        packet.extend([(rect_y >> 8) & 0xFF, rect_y & 0xFF])        # rect_y (大端序)

        # 屏幕中心坐标 (4字节，大端序)
        screen_x = SERIAL_CONFIG['screen_center_x']
        screen_y = SERIAL_CONFIG['screen_center_y']
        packet.extend([(screen_x >> 8) & 0xFF, screen_x & 0xFF])    # screen_x (大端序)
        packet.extend([(screen_y >> 8) & 0xFF, screen_y & 0xFF])    # screen_y (大端序)

        # 校验和 (1字节) - 从rect_x到screen_y的所有8个数据字节
        checksum = sum(packet[2:]) % 256
        packet.append(checksum)

        # 包尾 (2字节)
        packet.extend(SERIAL_CONFIG['packet_tail'])  # 0x0D, 0x0A

        # 发送数据包 (总长度13字节)
        uart.write(bytes(packet))

        return True

    except Exception as e:
        print(f"❌ 串口发送失败: {e}")
        return False

# ==================== Step11新增：可视化绘制模块 ====================
def draw_screen_center(osd_img):
    """绘制屏幕中心点 - Step11可视化功能"""
    try:
        if not VISUALIZATION_CONFIG['draw_screen_center']:
            return

        center_x = SERIAL_CONFIG['screen_center_x'] * DISPLAY_WIDTH // OUT_RGB888P_WIDTH
        center_y = SERIAL_CONFIG['screen_center_y'] * DISPLAY_HEIGHT // OUT_RGB888P_HEIGH
        color = VISUALIZATION_CONFIG['screen_center_color']

        # 绘制中心圆点
        osd_img.draw_circle(center_x, center_y, 8, color=color, thickness=3)

        # 绘制十字标记
        cross_size = 15
        osd_img.draw_line(center_x - cross_size, center_y, center_x + cross_size, center_y,
                         color=color, thickness=3)
        osd_img.draw_line(center_x, center_y - cross_size, center_x, center_y + cross_size,
                         color=color, thickness=3)

        # 绘制标签
        osd_img.draw_string_advanced(center_x - 40, center_y - 40, 24, "SCREEN", color=color)

    except Exception as e:
        print(f"绘制屏幕中心错误: {e}")

def draw_target_center(osd_img, target_x, target_y):
    """绘制目标中心点 - Step11可视化功能"""
    try:
        if not VISUALIZATION_CONFIG['draw_center_points']:
            return

        color = VISUALIZATION_CONFIG['center_color']

        # 绘制目标中心点（较大的圆点）
        osd_img.draw_circle(target_x, target_y, 10, color=color, thickness=4)

        # 绘制标签
        osd_img.draw_string_advanced(target_x - 30, target_y - 50, 24, "TARGET", color=color)

    except Exception as e:
        print(f"绘制目标中心错误: {e}")

def draw_connection_line(osd_img, target_x, target_y):
    """绘制连接线 - Step11可视化功能"""
    try:
        if not VISUALIZATION_CONFIG['draw_connection_line']:
            return

        screen_center_x = SERIAL_CONFIG['screen_center_x'] * DISPLAY_WIDTH // OUT_RGB888P_WIDTH
        screen_center_y = SERIAL_CONFIG['screen_center_y'] * DISPLAY_HEIGHT // OUT_RGB888P_HEIGH
        color = VISUALIZATION_CONFIG['line_color']

        # 绘制连接线
        osd_img.draw_line(target_x, target_y, screen_center_x, screen_center_y,
                         color=color, thickness=3)

        # 计算并显示距离
        import math
        distance = math.sqrt((target_x - screen_center_x)**2 + (target_y - screen_center_y)**2)

        mid_x = (target_x + screen_center_x) // 2
        mid_y = (target_y + screen_center_y) // 2
        osd_img.draw_string_advanced(mid_x - 30, mid_y - 15, 20, f"{distance:.1f}", color=color)

    except Exception as e:
        print(f"绘制连接线错误: {e}")

def draw_info_panel(osd_img, target_x, target_y, fps):
    """绘制信息面板 - Step11可视化功能"""
    try:
        if not VISUALIZATION_CONFIG['draw_info_panel']:
            return

        # 基础信息
        info_x = 20
        info_y = 20
        line_height = 35

        # 系统信息
        osd_img.draw_string_advanced(info_x, info_y, 28, "Step11 YOLO检测系统", color=(255, 255, 0))
        info_y += line_height

        # 目标中心坐标（原始坐标）
        if target_x is not None and target_y is not None:
            # 转换回原始坐标系
            orig_x = target_x * OUT_RGB888P_WIDTH // DISPLAY_WIDTH
            orig_y = target_y * OUT_RGB888P_HEIGH // DISPLAY_HEIGHT

            osd_img.draw_string_advanced(info_x, info_y, 24,
                                       f"目标中心: ({orig_x}, {orig_y})",
                                       color=(0, 255, 0))

            # 偏差计算
            offset_x = orig_x - SERIAL_CONFIG['screen_center_x']
            offset_y = orig_y - SERIAL_CONFIG['screen_center_y']
            info_y += line_height
            osd_img.draw_string_advanced(info_x, info_y, 24,
                                       f"偏差: ΔX={offset_x:+d}, ΔY={offset_y:+d}",
                                       color=(255, 255, 0))
        else:
            osd_img.draw_string_advanced(info_x, info_y, 24, "目标中心: 未检测到", color=(255, 0, 0))

        info_y += line_height

        # 性能信息
        osd_img.draw_string_advanced(info_x, info_y, 24, f"FPS: {fps:.1f}", color=(255, 255, 255))
        info_y += line_height

        osd_img.draw_string_advanced(info_x, info_y, 24, f"总帧数: {frame_count}", color=(255, 255, 255))
        info_y += line_height

        # 检测成功率
        detection_rate = (detection_count / frame_count * 100) if frame_count > 0 else 0
        rate_color = (0, 255, 0) if detection_rate >= 60 else (255, 0, 0)
        osd_img.draw_string_advanced(info_x, info_y, 24, f"检测率: {detection_rate:.1f}%", color=rate_color)

    except Exception as e:
        print(f"绘制信息面板错误: {e}")

def calculate_fps():
    """计算FPS - 修复函数属性问题"""
    current_time = time.time()

    # 使用全局变量而不是函数属性
    global fps_start_time, fps_frame_count, last_fps

    # 初始化全局变量
    if 'fps_start_time' not in globals():
        fps_start_time = current_time
        fps_frame_count = 0
        last_fps = 0.0

    fps_frame_count += 1
    elapsed_time = current_time - fps_start_time

    if elapsed_time >= 1.0:  # 每秒更新一次FPS
        last_fps = fps_frame_count / elapsed_time
        fps_start_time = current_time
        fps_frame_count = 0

    return last_fps

# ==================== Step11主检测函数（基于det_video.py） ====================
def detection():
    """主检测函数 - 基于det_video.py，集成Step11功能"""
    global frame_count, detection_count, last_send_time, fps_start_time, fps_frame_count, last_fps

    print("🚀 Step11 YOLO矩形检测系统启动")
    print("📋 系统特性:")
    print("   ✅ 基于det_video.py的YOLO检测")
    print("   ✅ STM32串口通信（C结构体协议）")
    print("   ✅ 实时可视化显示")
    print("   ✅ 性能监控")

    # 初始化串口
    uart_status = "❌断开"
    if init_uart():
        uart_status = "✅连接"
    else:
        print("⚠️  串口初始化失败，程序继续运行但无通信功能")

    # 读取配置文件（继承det_video.py）
    deploy_conf = read_deploy_config(config_path)
    if not deploy_conf:
        print("Failed to load deploy_config.json, exiting.")
        return

    kmodel_name = deploy_conf["kmodel_path"]
    labels = deploy_conf["categories"]
    confidence_threshold = deploy_conf["confidence_threshold"]
    nms_threshold = deploy_conf["nms_threshold"]
    img_size = deploy_conf["img_size"]
    num_classes = deploy_conf["num_classes"]
    nms_option = deploy_conf["nms_option"]
    model_type = deploy_conf["model_type"]
    if model_type == "AnchorBaseDet":
        anchors = deploy_conf["anchors"][0] + deploy_conf["anchors"][1] + deploy_conf["anchors"][2]

    kmodel_frame_size = img_size
    frame_size = [OUT_RGB888P_WIDTH, OUT_RGB888P_HEIGH]
    strides = [8, 16, 32]

    # 计算padding（继承det_video.py）
    ori_w, ori_h = OUT_RGB888P_WIDTH, OUT_RGB888P_HEIGH
    width, height = kmodel_frame_size[0], kmodel_frame_size[1]
    ratio = min(float(width) / ori_w, float(height) / ori_h)
    new_w, new_h = int(ratio * ori_w), int(ratio * ori_h)
    dw, dh = (width - new_w) / 2, (height - new_h) / 2
    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))

    # 初始化KPU和AI2D（继承det_video.py）
    kpu = nn.kpu()
    ai2d = nn.ai2d()
    kpu.load_kmodel(root_path + kmodel_name)
    ai2d.set_dtype(nn.ai2d_format.NCHW_FMT, nn.ai2d_format.NCHW_FMT, np.uint8, np.uint8)
    ai2d.set_pad_param(True, [0, 0, 0, 0, top, bottom, left, right], 0, [114, 114, 114])
    ai2d.set_resize_param(True, nn.interp_method.tf_bilinear, nn.interp_mode.half_pixel)
    ai2d_builder = ai2d.build([1, 3, OUT_RGB888P_HEIGH, OUT_RGB888P_WIDTH], [1, 3, height, width])

    # 初始化摄像头（修复：完全按照det_video.py的配置方式）
    sensor = Sensor(id=2)
    sensor.reset()
    sensor.set_hmirror(False)
    sensor.set_vflip(False)

    # 完全按照det_video.py的传感器配置
    sensor.set_framesize(width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT)
    sensor.set_pixformat(PIXEL_FORMAT_YUV_SEMIPLANAR_420)
    sensor.set_framesize(width=OUT_RGB888P_WIDTH, height=OUT_RGB888P_HEIGH, chn=CAM_CHN_ID_2)
    sensor.set_pixformat(PIXEL_FORMAT_RGB_888_PLANAR, chn=CAM_CHN_ID_2)

    # 初始化显示（修复：完全按照det_video.py的配置）
    sensor_bind_info = sensor.bind_info(x=0, y=0, chn=CAM_CHN_ID_0)
    Display.bind_layer(**sensor_bind_info, layer=Display.LAYER_VIDEO1)
    if display_mode == "lcd":
        Display.init(Display.ST7701, to_ide=True)
    else:
        Display.init(Display.LT9611, to_ide=True)

    osd_img = image.Image(DISPLAY_WIDTH, DISPLAY_HEIGHT, image.ARGB8888)

    print("🎯 Step11系统初始化完成")
    print(f"📱 串口状态: {uart_status}")
    print("📊 开始检测...")

    try:
        MediaManager.init()
        sensor.run()

        rgb888p_img = None
        ai2d_input_tensor = None
        data = np.ones((1, 3, width, height), dtype=np.uint8)
        ai2d_output_tensor = nn.from_numpy(data)

        while True:
            frame_count += 1
            current_fps = calculate_fps()

            with ScopedTiming("total", debug_mode > 0):
                rgb888p_img = sensor.snapshot(chn=CAM_CHN_ID_2)
                # 修复：检查图像格式，确保兼容性
                if rgb888p_img and rgb888p_img.format() == image.RGBP888:
                    ai2d_input = rgb888p_img.to_numpy_ref()
                    ai2d_input_tensor = nn.from_numpy(ai2d_input)
                    ai2d_builder.run(ai2d_input_tensor, ai2d_output_tensor)

                    kpu.set_input_tensor(0, ai2d_output_tensor)
                    kpu.run()

                    results = []
                    for i in range(kpu.outputs_size()):
                        out_data = kpu.get_output_tensor(i)
                        result = out_data.to_numpy()
                        result = result.reshape((result.shape[0] * result.shape[1] * result.shape[2] * result.shape[3]))
                        del out_data
                        results.append(result)
                    gc.collect()

                    # YOLO后处理（继承det_video.py）
                    if model_type == "AnchorBaseDet":
                        det_boxes = aicube.anchorbasedet_post_process(results[0], results[1], results[2], kmodel_frame_size, frame_size, strides, num_classes, confidence_threshold, nms_threshold, anchors, nms_option)
                    elif model_type == "GFLDet":
                        det_boxes = aicube.gfldet_post_process(results[0], results[1], results[2], kmodel_frame_size, frame_size, strides, num_classes, confidence_threshold, nms_threshold, nms_option)
                    else:
                        det_boxes = aicube.anchorfreedet_post_process(results[0], results[1], results[2], kmodel_frame_size, frame_size, strides, num_classes, confidence_threshold, nms_threshold, nms_option)

                    osd_img.clear()

                    # Step11新增：处理检测结果和串口通信
                    target_found = False
                    target_display_x = None
                    target_display_y = None
                    target_orig_x = None
                    target_orig_y = None

                    if det_boxes:
                        for det_boxe in det_boxes:
                            class_id = det_boxe[0]
                            # 检查检测到的类别是否为目标类别（如"yuan"）
                            if labels[class_id] in ["yuan", "person", "object"]:  # 可配置目标类别
                                x1, y1, x2, y2 = det_boxe[2], det_boxe[3], det_boxe[4], det_boxe[5]

                                # 映射回显示分辨率
                                display_x1 = int(x1 * DISPLAY_WIDTH / OUT_RGB888P_WIDTH)
                                display_y1 = int(y1 * DISPLAY_HEIGHT / OUT_RGB888P_HEIGH)
                                display_w = int((x2 - x1) * DISPLAY_WIDTH / OUT_RGB888P_WIDTH)
                                display_h = int((y2 - y1) * DISPLAY_HEIGHT / OUT_RGB888P_HEIGH)

                                # 计算中心点
                                target_display_x = display_x1 + display_w // 2
                                target_display_y = display_y1 + display_h // 2

                                # 计算原始坐标系中的中心点（用于串口发送）
                                target_orig_x = int(x1 + (x2 - x1) // 2)
                                target_orig_y = int(y1 + (y2 - y1) // 2)

                                # 确保坐标在有效范围内
                                target_orig_x = max(0, min(OUT_RGB888P_WIDTH - 1, target_orig_x))
                                target_orig_y = max(0, min(OUT_RGB888P_HEIGH - 1, target_orig_y))

                                target_found = True
                                detection_count += 1

                                # 计算误差（继承det_video.py的逻辑）
                                delta_x = target_orig_x - OUT_RGB888P_WIDTH // 2
                                delta_y = target_orig_y - OUT_RGB888P_HEIGH // 2

                                # 当debug_mode为True时打印误差
                                if debug_mode:
                                    print(f"Target '{labels[class_id]}' found. Error -> x: {delta_x}, y: {delta_y}")

                                # Step11新增：STM32串口通信
                                current_time = time.ticks_ms()
                                if current_time - last_send_time >= SERIAL_CONFIG['send_interval']:
                                    if uart is not None:
                                        send_success = send_coordinates_to_stm32(target_orig_x, target_orig_y)
                                        if send_success and frame_count % 50 == 0:
                                            print(f"📡 Step11数据发送:")
                                            print(f"   目标中心: ({target_orig_x}, {target_orig_y})")
                                            print(f"   目标类别: {labels[class_id]}")
                                            print(f"   置信度: {det_boxe[1]:.2f}")
                                            print(f"   数据包格式: STM32兼容(11字节)")
                                    last_send_time = current_time

                                break  # 只处理第一个检测到的目标

                            # 如果开启调试模式，绘制所有检测框（继承det_video.py）
                            if debug_mode:
                                x1, y1, x2, y2 = det_boxe[2], det_boxe[3], det_boxe[4], det_boxe[5]
                                w = float(x2 - x1) * DISPLAY_WIDTH / OUT_RGB888P_WIDTH
                                h = float(y2 - y1) * DISPLAY_HEIGHT / OUT_RGB888P_HEIGH
                                osd_img.draw_rectangle(int(x1 * DISPLAY_WIDTH / OUT_RGB888P_WIDTH),
                                                       int(y1 * DISPLAY_HEIGHT / OUT_RGB888P_HEIGH),
                                                       int(w), int(h), color=color_four[class_id][1:])
                                label = labels[class_id]
                                score = str(round(det_boxe[1], 2))
                                osd_img.draw_string_advanced(int(x1 * DISPLAY_WIDTH / OUT_RGB888P_WIDTH),
                                                             int(y1 * DISPLAY_HEIGHT / OUT_RGB888P_HEIGH) - 50, 32,
                                                             label + " " + score, color=color_four[class_id][1:])

                    # Step11新增：绘制可视化元素
                    draw_screen_center(osd_img)
                    if target_found:
                        draw_target_center(osd_img, target_display_x, target_display_y)
                        draw_connection_line(osd_img, target_display_x, target_display_y)
                    draw_info_panel(osd_img, target_display_x, target_display_y, current_fps)

                    Display.show_image(osd_img, 0, 0, Display.LAYER_OSD3)
                    gc.collect()
                rgb888p_img = None

    except Exception as e:
        print(f"❌ 程序运行错误: {e}")
    finally:
        # 清理资源（继承det_video.py）
        print("🔄 正在清理资源...")
        os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
        if 'ai2d_input_tensor' in locals() and ai2d_input_tensor:
            del ai2d_input_tensor
        if 'ai2d_output_tensor' in locals() and ai2d_output_tensor:
            del ai2d_output_tensor
        if 'sensor' in locals():
            sensor.stop()
        Display.deinit()
        MediaManager.deinit()
        if uart:
            uart.deinit()
        gc.collect()
        time.sleep(1)
        nn.shrink_memory_pool()

        # Step11新增：最终统计报告
        if frame_count > 0:
            detection_rate = detection_count / frame_count * 100
            # 修复：确保current_fps变量存在
            final_fps = last_fps if 'last_fps' in globals() else 0.0
            print("📊 Step11最终报告:")
            print(f"   总帧数: {frame_count}")
            print(f"   检测成功: {detection_count}")
            print(f"   检测成功率: {detection_rate:.1f}%")
            print(f"   平均FPS: {final_fps:.1f}")

            if detection_rate >= 60:
                print("📈 系统评级: 优秀 - 检测率达标")
            elif detection_rate >= 40:
                print("📈 系统评级: 良好 - 检测率可接受")
            else:
                print("📈 系统评级: 需改进 - 检测率偏低")

    print("第十一步YOLO矩形检测系统测试完成")
    print("🎉 Step11核心功能总结:")
    print("   ✅ 基于det_video.py的YOLO检测")
    print("   ✅ STM32串口通信（C结构体协议，13字节）")
    print("   ✅ 实时可视化显示（中心点+连线+信息面板）")
    print("   ✅ 坐标转换和范围检查")
    print("   ✅ 性能监控和统计")
    print("   ✅ 完善的错误处理和资源清理")
    return 0

if __name__ == "__main__":
    detection()
