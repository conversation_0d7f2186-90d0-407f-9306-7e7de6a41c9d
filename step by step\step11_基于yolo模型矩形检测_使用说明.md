# Step11 基于YOLO模型矩形检测系统 - 使用说明

## 📋 系统概述

Step11系统是基于`yolo/det_video.py`进行修改和扩展的YOLO矩形检测系统，集成了STM32串口通信功能，提供实时的目标检测和定位服务。

## 🎯 核心功能

### 1. YOLO模型检测
- **完全继承** `det_video.py` 的YOLO检测流程
- **支持多种模型类型**：AnchorBaseDet、GFLDet、AnchorFreeDet
- **自动配置加载**：从 `deploy_config.json` 读取模型参数
- **高性能推理**：使用K230的KPU和AI2D加速

### 2. STM32串口通信
- **严格协议兼容**：按照C结构体定义的数据包格式
- **数据包结构**：
  ```c
  typedef struct {
      uint8_t header[2];      // 包头 0xAA 0x55
      uint16_t rect_x;        // 矩形中心X坐标 (大端序)
      uint16_t rect_y;        // 矩形中心Y坐标 (大端序)
      uint16_t screen_x;      // 屏幕中心X坐标 (大端序)
      uint16_t screen_y;      // 屏幕中心Y坐标 (大端序)
      uint8_t checksum;       // 校验和
      uint8_t tail[2];        // 包尾 0x0D 0x0A
  } CoordinatePacket;
  ```
- **数据包长度**：11字节
- **发送条件**：只有成功检测到目标时才发送
- **发送频率**：40ms间隔，确保不会过载

### 3. 实时可视化
- **矩形中心点**：红色圆点标记检测到的目标中心
- **屏幕中心点**：黄色十字标记固定的屏幕中心 (160, 120)
- **连接线**：青色线条连接目标中心和屏幕中心，显示距离
- **信息面板**：实时显示坐标、偏差、FPS、检测率等信息
- **检测框**：继承det_video.py的检测框绘制功能

### 4. 性能监控
- **实时FPS**：每秒更新的帧率显示
- **检测成功率**：统计检测到目标的帧数比例
- **坐标偏差**：实时计算目标中心与屏幕中心的偏差
- **系统评级**：根据检测率自动评估系统性能

## 🔧 配置说明

### 串口配置
```python
SERIAL_CONFIG = {
    'port': UART.UART2,        # 串口端口
    'baudrate': 115200,        # 波特率
    'tx_pin': 11,              # 发送引脚
    'rx_pin': 12,              # 接收引脚
    'send_interval': 40,       # 发送间隔(ms)
    'screen_center_x': 160,    # 屏幕中心X坐标
    'screen_center_y': 120,    # 屏幕中心Y坐标
}
```

### 可视化配置
```python
VISUALIZATION_CONFIG = {
    'draw_center_points': True,    # 绘制中心点
    'draw_screen_center': True,    # 绘制屏幕中心
    'draw_connection_line': True,  # 绘制连接线
    'draw_info_panel': True,       # 绘制信息面板
    'center_color': (255, 0, 0),  # 矩形中心点颜色（红色）
    'screen_center_color': (255, 255, 0),  # 屏幕中心颜色（黄色）
    'line_color': (0, 255, 255),  # 连接线颜色（青色）
}
```

## 📁 文件结构

### 必需文件
```
/sdcard/new/
├── deploy_config.json          # YOLO模型配置文件
├── best_AnchorBaseDet_*.kmodel # YOLO模型文件
└── step11_基于yolo模型矩形检测.py
```

### 配置文件示例 (deploy_config.json)
```json
{
    "inference_width": 320,
    "inference_height": 320,
    "confidence_threshold": 0.5,
    "nms_threshold": 0.5,
    "categories": ["yuan"],
    "kmodel_path": "best_AnchorBaseDet_can2_10_s_20250730105226.kmodel",
    "num_classes": 1,
    "model_type": "AnchorBaseDet"
}
```

## 🚀 使用步骤

### 1. 环境准备
1. 确保K230开发板正常工作
2. 将YOLO模型文件和配置文件放置在 `/sdcard/new/` 目录
3. 连接STM32控制器到指定的串口引脚

### 2. 运行系统
```bash
# 在K230终端中运行
python step11_基于yolo模型矩形检测.py
```

### 3. 系统启动信息
```
🚀 Step11 YOLO矩形检测系统启动
📋 系统特性:
   ✅ 基于det_video.py的YOLO检测
   ✅ STM32串口通信（C结构体协议）
   ✅ 实时可视化显示
   ✅ 性能监控
📱 串口状态: ✅连接
📊 开始检测...
```

### 4. 实时监控
- 观察屏幕上的可视化元素
- 监控终端输出的检测信息
- 检查STM32是否正确接收数据

## 📊 性能指标

### 目标性能
- **帧率**：≥15 FPS
- **检测成功率**：≥60%
- **延迟**：≤50ms
- **串口通信**：稳定可靠

### 性能评级标准
- **优秀**：检测率 ≥60%
- **良好**：检测率 40%-60%
- **需改进**：检测率 <40%

## 🔍 调试功能

### 调试模式
设置 `debug_mode = True` 启用详细调试信息：
- 检测框绘制
- 置信度显示
- 误差输出
- 处理时间统计

### 调试输出示例
```
Target 'yuan' found. Error -> x: -25, y: 15
📡 Step11数据发送:
   目标中心: (135, 135)
   目标类别: yuan
   置信度: 0.87
   数据包格式: STM32兼容(11字节)
```

## ⚠️ 注意事项

### 1. 硬件要求
- K230开发板
- 摄像头模块
- STM32控制器
- 串口连接线

### 2. 软件依赖
- 所有det_video.py的依赖库
- UART通信库
- 正确的YOLO模型文件

### 3. 常见问题
1. **模型加载失败**：检查模型文件路径和格式
2. **串口通信失败**：检查引脚连接和波特率设置
3. **检测率低**：调整置信度阈值或更换模型
4. **帧率低**：减少可视化元素或降低分辨率

## 🎉 系统优势

### 相比原始det_video.py的改进
1. **STM32集成**：无缝的串口通信功能
2. **可视化增强**：丰富的实时显示元素
3. **性能监控**：详细的统计和评级系统
4. **坐标转换**：准确的坐标系转换和范围检查
5. **错误处理**：完善的异常处理和资源清理

### 适用场景
- 激光定位控制系统
- 目标跟踪应用
- 机器人视觉导航
- 自动化控制系统

## 📞 技术支持

如遇到问题，请检查：
1. 硬件连接是否正确
2. 配置文件是否完整
3. 模型文件是否兼容
4. 系统资源是否充足

系统设计确保了与det_video.py的完全兼容性，同时提供了强大的扩展功能。
