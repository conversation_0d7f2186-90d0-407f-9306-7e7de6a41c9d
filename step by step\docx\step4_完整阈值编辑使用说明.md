# 第四步：完整阈值编辑功能使用说明

## 程序概述

**文件名**: `step4_交互式按键界面.py`

**功能**: 基于step3的双重检测功能，实现完整的阈值编辑界面，支持实时调整矩形和激光点检测阈值

**参考**: 基于`14_脱机调整阈值.py`的实现方式

## 新增功能

### 1. 完整的阈值编辑界面

#### **界面切换**：
- 保持原有的三个主按键：基础部分、进阶部分、阈值编辑
- 点击"阈值编辑"按钮进入完整的阈值编辑界面
- 阈值编辑界面占用整个800x480屏幕空间

#### **两种编辑模式**：
- **矩形阈值编辑**：调整RECT_THRESHOLD参数（灰度阈值）
- **激光点阈值编辑**：调整LASER_THRESHOLD参数（LAB颜色空间）

### 2. 阈值编辑界面布局

#### **800x480屏幕布局**：
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ [返回]                                              [切换]                  │
│                                                                             │
│ 🔧 矩形阈值编辑                           [实时预览图像]                    │
│ 当前阈值: (0, 80)                                                          │
│ 📝 编辑模式: RECT                                                          │
│                                                                             │
│ [-] 最小值: 0                                                          [+] │
│                                                                             │
│ [-] 最大值: 80                                                         [+] │
│                                                                             │
│                                                                             │
│                                                                             │
│ [重置]                                              [保存]                  │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### **按钮功能说明**：

**顶部控制按钮**：
- **返回按钮** (20, 20, 120x40)：退出阈值编辑，返回基础检测界面
- **切换按钮** (660, 20, 120x40)：在矩形阈值和激光点阈值之间切换

**底部控制按钮**：
- **重置按钮** (20, 420, 120x40)：恢复当前模式的默认阈值
- **保存按钮** (660, 420, 120x40)：保存当前阈值到程序变量

**参数调整按钮**：
- **减少按钮** (左侧)：减少对应参数值
- **增加按钮** (右侧)：增加对应参数值

### 3. 矩形阈值编辑模式

#### **编辑参数**：
- **最小值** (0-255)：灰度阈值下限
- **最大值** (0-255)：灰度阈值上限

#### **调整步长**：
- 每次点击调整5个单位
- 范围限制：0-255

#### **实时预览**：
- 右上角显示二值化预览图像
- 实时反映当前阈值的检测效果

#### **默认值**：
```python
矩形阈值: (0, 80)
```

### 4. 激光点阈值编辑模式

#### **编辑参数**：
- **L_min** (0-100)：LAB颜色空间L通道最小值
- **L_max** (0-100)：LAB颜色空间L通道最大值
- **A_min** (-128~127)：LAB颜色空间A通道最小值
- **A_max** (-128~127)：LAB颜色空间A通道最大值
- **B_min** (-128~127)：LAB颜色空间B通道最小值
- **B_max** (-128~127)：LAB颜色空间B通道最大值

#### **调整步长**：
- L通道：每次调整2个单位
- A/B通道：每次调整2个单位

#### **实时预览**：
- 右上角显示激光点检测结果
- 检测到的激光点用青色边框和紫色圆点标记

#### **默认值**：
```python
激光点阈值: (47, 80, 9, 91, -55, 63)
```

## 操作指南

### 1. 进入阈值编辑界面

1. 运行程序：`python "step by step/step4_交互式按键界面.py"`
2. 点击底部的"阈值编辑"按钮
3. 界面自动切换到阈值编辑模式

### 2. 矩形阈值编辑

#### **步骤**：
1. 确保当前为矩形编辑模式（标题显示"🔧 矩形阈值编辑"）
2. 观察右上角的二值化预览图像
3. 点击参数旁的"-"或"+"按钮调整阈值
4. 实时观察预览效果
5. 满意后点击"保存"按钮

#### **调整技巧**：
- **最小值**：控制检测的暗度下限，值越小检测越多暗色区域
- **最大值**：控制检测的暗度上限，值越大检测越多亮色区域
- **常用范围**：(0, 50)检测深黑色，(0, 120)检测灰色

### 3. 激光点阈值编辑

#### **步骤**：
1. 点击"切换"按钮切换到激光点编辑模式
2. 标题显示"🔧 激光点阈值编辑"
3. 观察右上角的激光点检测预览
4. 逐个调整6个LAB参数
5. 实时观察检测效果
6. 满意后点击"保存"按钮

#### **LAB参数调整技巧**：
- **L通道 (亮度)**：
  - L_min, L_max：控制激光点的亮度范围
  - 激光点通常较亮，建议L_min > 30
- **A通道 (绿-红)**：
  - 负值偏绿色，正值偏红色
  - 蓝紫激光点A值通常为正数
- **B通道 (蓝-黄)**：
  - 负值偏蓝色，正值偏黄色
  - 蓝紫激光点B值通常为负数

### 4. 保存和应用阈值

#### **保存操作**：
1. 调整满意后点击"保存"按钮
2. 控制台显示保存确认信息
3. 阈值立即生效

#### **验证效果**：
1. 点击"返回"按钮回到基础检测界面
2. 观察检测效果是否改善
3. 如需继续调整，重新进入阈值编辑界面

### 5. 重置功能

#### **重置操作**：
1. 在阈值编辑界面点击"重置"按钮
2. 当前模式的阈值恢复到默认值
3. 预览图像立即更新

#### **默认阈值**：
```python
# 矩形阈值默认值
RECT_THRESHOLD = [(0, 80)]

# 激光点阈值默认值
LASER_THRESHOLD = [(47, 80, 9, 91, -55, 63)]
```

## 技术实现

### 1. 全局变量更新

```python
# 阈值编辑状态变量
threshold_edit_mode = "rect"  # "rect" 或 "laser"
threshold_current = [0, 80, 0, 255, 0, 255]  # 当前编辑的阈值

# 保存时更新全局变量
if threshold_edit_mode == "rect":
    RECT_THRESHOLD[0] = tuple(threshold_current[:2])
else:
    LASER_THRESHOLD[0] = tuple(threshold_current)
```

### 2. 实时预览实现

```python
# 矩形阈值预览
preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)
preview_gray = preview_img.to_grayscale()
preview_binary = preview_gray.binary([tuple(threshold_current[:2])])

# 激光点阈值预览
current_lab_threshold = [tuple(threshold_current)]
blobs = preview_img.find_blobs(current_lab_threshold, False,
                             pixels_threshold=MIN_LASER_PIXELS)
```

### 3. 触摸检测优化

```python
# 阈值编辑界面优先检测
if current_mode == 3:
    threshold_clicked = check_threshold_edit_click(touch_x, touch_y)
else:
    clicked_button = check_button_click(touch_x, touch_y)
```

## 故障排除

### 问题1：预览图像不更新
**解决方案**：
1. 检查sensor.snapshot()是否正常
2. 确认阈值参数格式正确
3. 检查预览图像缩放是否成功

### 问题2：参数调整无效果
**解决方案**：
1. 确认threshold_current变量更新
2. 检查参数范围限制
3. 验证预览函数调用

### 问题3：保存后检测无变化
**解决方案**：
1. 确认全局变量RECT_THRESHOLD/LASER_THRESHOLD更新
2. 检查基础检测界面是否使用新阈值
3. 验证保存操作是否成功执行

### 问题4：触摸检测不准确
**解决方案**：
1. 检查按钮坐标定义
2. 确认触摸坐标映射正确
3. 调整按钮检测区域

## 使用建议

### 1. 矩形阈值调整建议
- 先观察目标矩形的灰度范围
- 从默认值(0, 80)开始微调
- 避免阈值范围过大导致误检

### 2. 激光点阈值调整建议
- 先确定激光点的主要颜色特征
- 逐个调整LAB参数，观察效果
- 建议先调L通道，再调A/B通道

### 3. 调试技巧
- 利用实时预览快速验证效果
- 保存前先在基础界面验证
- 记录有效的阈值参数组合

## 总结

完整的阈值编辑功能提供了：
- ✅ 直观的触摸操作界面
- ✅ 实时预览调整效果
- ✅ 矩形和激光点阈值双重编辑
- ✅ 参数保存和重置功能
- ✅ 与基础检测功能完全兼容
- ✅ 基于14_脱机调整阈值.py的成熟实现

这为K230激光定位系统提供了完整的参数调试能力！
