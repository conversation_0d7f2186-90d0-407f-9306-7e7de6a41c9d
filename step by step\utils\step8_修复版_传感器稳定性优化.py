# bilibili搜索学不会电磁场看教程
# K230矩形中心激光定位系统 - 第八步修复版：传感器稳定性优化
# 主要修复：
# 1. 传感器初始化重试机制和错误处理
# 2. 图像获取失败的自动恢复
# 3. 优化调试输出，减少冗余信息
# 4. 增强系统稳定性和鲁棒性

import time
import os
import sys
import math
from media.sensor import *
from media.display import *
from media.media import *
from machine import TOUCH
from machine import UART
from machine import FPIOA

# 常量定义
CAM_CHN_ID_0 = 0

# ==================== 传感器稳定性配置 ====================
SENSOR_RETRY_COUNT = 3       # 传感器初始化重试次数
SNAPSHOT_RETRY_COUNT = 3     # 图像获取重试次数
MAX_CONSECUTIVE_ERRORS = 10  # 最大连续错误次数
SENSOR_RESET_THRESHOLD = 5   # 传感器重置阈值

# 性能目标配置
TARGET_FPS = 20
TARGET_SUCCESS_RATE = 80
TARGET_LATENCY = 50

# 快速检测模式配置
FAST_MODE = True
SIMPLIFIED_VALIDATION = True
ADAPTIVE_ROI = True
MOTION_PREDICTION = True

# LAB阈值配置
threshold_edit_mode = "rect"
threshold_current = [13, 35, 20, -5, -44, 16]

threshold_dict = {
    'rect': [(13, 35, 20, -5, -44, 16)],
    'laser': [(47, 80, 9, 91, -55, 63), (16, 37, 23, 74, -48, 52)]
}

# 统计变量
rect_detection_stats = {
    'total_attempts': 0,
    'successful_detections': 0,
    'success_rate': 0.0,
    'recent_attempts': [],
    'recent_success_rate': 0.0,
    'fps_history': [],
    'avg_fps': 0.0,
    'processing_times': [],
    'avg_processing_time': 0.0
}

# 快速多帧检测配置
fast_multi_frame_detection = {
    'frame_buffer': [],
    'buffer_size': 3,
    'stable_threshold': 2,
    'position_threshold': 20.0,
    'last_stable_center': None,
    'last_send_center': None,
    'send_threshold': 5.0,
    'confidence_threshold': 0.6
}

# 自适应ROI配置
adaptive_roi_config = {
    'enabled': ADAPTIVE_ROI,
    'roi_x': 0,
    'roi_y': 0,
    'roi_width': 400,
    'roi_height': 240,
    'expansion_factor': 1.5,
    'min_roi_size': 100,
    'max_roi_size': 400,
    'update_threshold': 5,
    'frames_since_update': 0
}

# 运动预测配置
motion_prediction_config = {
    'enabled': MOTION_PREDICTION,
    'history_length': 5,
    'position_history': [],
    'velocity_history': [],
    'predicted_position': None,
    'prediction_confidence': 0.0,
    'max_velocity': 100.0,
    'velocity_smoothing': 0.7
}

# 串口通信配置
uart = None
SERIAL_PORT = UART.UART2
BAUD_RATE = 115200
TX_PIN = 11
RX_PIN = 12

PACKET_HEADER = [0xAA, 0x55]
PACKET_TAIL = [0x0D, 0x0A]
SCREEN_CENTER_X = 200
SCREEN_CENTER_Y = 120

# 检测参数
RECT_THRESHOLD = [(13, 35, 20, -5, -44, 16)]
MIN_RECT_AREA = 2000
MAX_RECT_AREA = 60000
MIN_RECT_ASPECT_RATIO = 0.2
MAX_RECT_ASPECT_RATIO = 5.0

LASER_THRESHOLD = [
    (47, 80, 9, 91, -55, 63),
    (16, 37, 23, 74, -48, 52)
]
MIN_LASER_PIXELS = 15

# 图像参数
picture_width = 400
picture_height = 240
sensor_id = 2
DISPLAY_MODE = "LCD"
DISPLAY_WIDTH = 800
DISPLAY_HEIGHT = 480
SCALE_FACTOR = 2.0
SCALED_WIDTH = int(picture_width * SCALE_FACTOR)
SCALED_HEIGHT = int(picture_height * SCALE_FACTOR)

# 按键定义
BUTTON_WIDTH = 150
BUTTON_HEIGHT = 60
BUTTON_Y = SCALED_HEIGHT - BUTTON_HEIGHT - 10
BUTTON_SPACING = 200
BUTTON_START_X = 50
BUTTON1_POS = (BUTTON_START_X, BUTTON_Y)
BUTTON2_POS = (BUTTON_START_X + BUTTON_SPACING, BUTTON_Y)
BUTTON3_POS = (BUTTON_START_X + BUTTON_SPACING * 2, BUTTON_Y)
BUTTON_ACTIVE_COLOR = (0, 255, 0)
BUTTON_INACTIVE_COLOR = (80, 80, 80)
BUTTON_TEXT_COLOR = (255, 255, 255)

sensor = None
frame_count = 0

# ==================== 传感器稳定性函数 ====================
def init_sensor_with_retry():
    """带重试机制的传感器初始化"""
    global sensor
    
    print("🔧 开始传感器初始化...")
    
    for retry in range(SENSOR_RETRY_COUNT):
        try:
            print(f"📷 传感器初始化尝试 {retry + 1}/{SENSOR_RETRY_COUNT}")
            
            # 创建传感器实例
            sensor = Sensor(id=sensor_id)
            
            # 重置传感器
            sensor.reset()
            time.sleep_ms(100)
            
            # 设置帧大小
            sensor.set_framesize(width=picture_width, height=picture_height, chn=CAM_CHN_ID_0)
            time.sleep_ms(50)
            
            # 设置像素格式
            sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)
            time.sleep_ms(50)
            
            print(f"✅ 传感器配置完成: {picture_width}x{picture_height}, RGB565")
            return True
            
        except Exception as e:
            print(f"❌ 传感器初始化失败 (尝试 {retry + 1}): {e}")
            if retry < SENSOR_RETRY_COUNT - 1:
                time.sleep_ms(500)
            else:
                return False
    
    return False

def start_sensor_with_check():
    """启动传感器并进行稳定性检查"""
    global sensor
    
    try:
        print("🚀 启动传感器...")
        sensor.run()
        time.sleep_ms(200)
        
        # 稳定性测试
        print("🔍 进行传感器稳定性测试...")
        for test_attempt in range(3):
            try:
                test_img = sensor.snapshot(chn=CAM_CHN_ID_0)
                if test_img is not None:
                    print(f"✅ 传感器测试成功 (尝试 {test_attempt + 1})")
                    return True
            except Exception as e:
                print(f"⚠️ 传感器测试失败 (尝试 {test_attempt + 1}): {e}")
                time.sleep_ms(200)
        
        return False
        
    except Exception as e:
        print(f"❌ 传感器启动失败: {e}")
        return False

def get_image_with_retry():
    """带重试机制的图像获取"""
    global sensor, frame_count
    
    for retry in range(SNAPSHOT_RETRY_COUNT):
        try:
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            if img is not None:
                return img, True
            else:
                if retry == 0:  # 只在第一次失败时输出
                    print(f"⚠️ 图像为空 (帧 {frame_count})")
                time.sleep_ms(10)
                
        except Exception as e:
            if retry == 0:  # 只在第一次失败时输出详细错误
                print(f"❌ 图像获取失败 (帧 {frame_count}): {e}")
            time.sleep_ms(20)
    
    return None, False

def reinitialize_sensor():
    """重新初始化传感器"""
    global sensor
    
    print("🔄 重新初始化传感器...")
    try:
        if sensor:
            sensor.stop()
            time.sleep_ms(100)
        
        if init_sensor_with_retry():
            if start_sensor_with_check():
                print("✅ 传感器重新初始化成功")
                return True
        
        print("❌ 传感器重新初始化失败")
        return False
        
    except Exception as e:
        print(f"💥 传感器重新初始化异常: {e}")
        return False

# ==================== 核心检测函数（简化版） ====================
def update_detection_stats(success):
    """更新检测统计"""
    global rect_detection_stats
    
    rect_detection_stats['total_attempts'] += 1
    if success:
        rect_detection_stats['successful_detections'] += 1
    
    if rect_detection_stats['total_attempts'] > 0:
        rect_detection_stats['success_rate'] = (
            rect_detection_stats['successful_detections'] / 
            rect_detection_stats['total_attempts'] * 100
        )
    
    rect_detection_stats['recent_attempts'].append(success)
    if len(rect_detection_stats['recent_attempts']) > 50:
        rect_detection_stats['recent_attempts'].pop(0)
    
    if len(rect_detection_stats['recent_attempts']) > 0:
        recent_successes = sum(rect_detection_stats['recent_attempts'])
        rect_detection_stats['recent_success_rate'] = (
            recent_successes / len(rect_detection_stats['recent_attempts']) * 100
        )

def detect_rectangles_simple(img):
    """简化的快速矩形检测"""
    try:
        start_time = time.ticks_ms()
        
        # 使用全图检测（简化版本）
        detect_img = img.copy()
        
        # LAB二值化
        binary_img = detect_img.binary(RECT_THRESHOLD)
        
        # 简化的形态学操作
        if FAST_MODE:
            binary_img.erode(1, threshold=1)
        
        # 查找矩形
        rects = binary_img.find_rects(threshold=MIN_RECT_AREA)
        
        if not rects:
            update_detection_stats(False)
            processing_time = time.ticks_ms() - start_time
            return False, 0, [], None, processing_time
        
        # 简化的矩形验证
        valid_rects = []
        for rect in rects:
            area = rect.w() * rect.h()
            if MIN_RECT_AREA <= area <= MAX_RECT_AREA:
                valid_rects.append(rect)
        
        if len(valid_rects) < 2:
            update_detection_stats(False)
            processing_time = time.ticks_ms() - start_time
            return False, 0, [], None, processing_time
        
        # 简化的配对算法
        valid_rects.sort(key=lambda r: r.w() * r.h(), reverse=True)
        
        # 取前两个矩形
        rect1, rect2 = valid_rects[0], valid_rects[1]
        
        # 计算中心点
        corners1 = rect1.corners()
        center1 = (
            sum(corner[0] for corner in corners1) / 4,
            sum(corner[1] for corner in corners1) / 4
        )
        
        corners2 = rect2.corners()
        center2 = (
            sum(corner[0] for corner in corners2) / 4,
            sum(corner[1] for corner in corners2) / 4
        )
        
        # 计算平均中心点
        avg_center = (
            (center1[0] + center2[0]) / 2,
            (center1[1] + center2[1]) / 2
        )
        
        # 绘制检测结果
        for rect, color in zip([rect1, rect2], [(255, 0, 255), (0, 255, 0)]):
            corners = rect.corners()
            for j in range(4):
                next_j = (j + 1) % 4
                x1, y1 = corners[j][0], corners[j][1]
                x2, y2 = corners[next_j][0], corners[next_j][1]
                img.draw_line(x1, y1, x2, y2, color=color, thickness=2)
        
        # 绘制中心点
        img.draw_circle(int(avg_center[0]), int(avg_center[1]), 8,
                       color=(255, 0, 0), thickness=3)
        img.draw_string_advanced(int(avg_center[0]) - 20, int(avg_center[1]) - 30,
                               14, "CENTER", color=(255, 0, 0))
        
        update_detection_stats(True)
        processing_time = time.ticks_ms() - start_time
        
        return True, 2, [center1, center2], avg_center, processing_time
        
    except Exception as e:
        print(f"矩形检测错误: {e}")
        update_detection_stats(False)
        processing_time = time.ticks_ms() - start_time
        return False, 0, [], None, processing_time

# 注：此文件已移动到utils文件夹，完整代码请参考原始文件
# 这里只保留核心函数示例，完整版本请查看原始step8文件
