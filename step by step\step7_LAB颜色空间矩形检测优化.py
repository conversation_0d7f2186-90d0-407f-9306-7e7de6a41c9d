# bilibili搜索学不会电磁场看教程
# K230矩形中心激光定位系统 - 第七步：LAB颜色空间矩形检测优化
# 基于step6的串口通信功能，实现以下重要改进：
# 1. 将矩形检测从灰度方案迁移到LAB颜色空间方案，提高光照适应性
# 2. 优化矩形识别算法，显著提高识别成功率
# 3. 添加识别率统计功能，实时显示矩形检测成功率
# 4. 统一阈值管理机制，矩形和激光点都使用LAB颜色空间

import time
import os
import sys
from media.sensor import *
from media.display import *
from media.media import *
from machine import TOUCH
from machine import UART
from machine import FPIOA

# 常量定义
CAM_CHN_ID_0 = 0

# ==================== 重要改进：LAB颜色空间阈值系统 ====================
# 阈值编辑全局变量 - 统一使用LAB格式的6个参数
threshold_edit_mode = "rect"  # "rect" 或 "laser"
threshold_current = [0, 100, -20, 20, -20, 20]  # LAB格式：L_min, L_max, A_min, A_max, B_min, B_max

# 阈值存储机制 - 统一使用LAB颜色空间
threshold_dict = {
    'rect': [(13, 35, 20, -5, -44, 16)],  # 黑色矩形的LAB阈值
    'laser': [(47, 80, 9, 91, -55, 63), (16, 37, 23, 74, -48, 52)]  # 激光点LAB阈值
}

# ==================== 识别率统计系统 ====================
# 识别率统计变量
rect_detection_stats = {
    'total_attempts': 0,      # 总检测次数
    'successful_detections': 0,  # 成功检测次数
    'success_rate': 0.0,      # 成功率百分比
    'recent_attempts': [],    # 最近100次检测结果（True/False）
    'recent_success_rate': 0.0  # 最近100次的成功率
}

# ==================== 多帧稳定性检测系统 ====================
# 多帧检测稳定性变量
multi_frame_detection = {
    'frame_buffer': [],           # 存储最近N帧的检测结果
    'buffer_size': 5,            # 缓冲区大小（连续5帧确认）
    'stable_threshold': 3,       # 稳定阈值（5帧中至少3帧检测成功）
    'position_threshold': 15.0,  # 位置变化阈值（像素）
    'last_stable_center': None,  # 上次稳定的中心点
    'last_send_center': None,    # 上次发送的中心点
    'send_threshold': 10.0,      # 发送阈值（位置变化超过此值才发送）
    'confidence_threshold': 0.7  # 置信度阈值
}

def update_detection_stats(success):
    """更新矩形检测统计数据"""
    global rect_detection_stats

    # 更新总体统计
    rect_detection_stats['total_attempts'] += 1
    if success:
        rect_detection_stats['successful_detections'] += 1

    # 计算总体成功率
    if rect_detection_stats['total_attempts'] > 0:
        rect_detection_stats['success_rate'] = (
            rect_detection_stats['successful_detections'] /
            rect_detection_stats['total_attempts'] * 100
        )

    # 更新最近100次统计
    rect_detection_stats['recent_attempts'].append(success)
    if len(rect_detection_stats['recent_attempts']) > 100:
        rect_detection_stats['recent_attempts'].pop(0)  # 移除最旧的记录

    # 计算最近100次成功率
    if len(rect_detection_stats['recent_attempts']) > 0:
        recent_successes = sum(rect_detection_stats['recent_attempts'])
        rect_detection_stats['recent_success_rate'] = (
            recent_successes / len(rect_detection_stats['recent_attempts']) * 100
        )

def get_detection_stats_text():
    """获取检测统计信息的显示文本"""
    total = rect_detection_stats['total_attempts']
    success_rate = rect_detection_stats['success_rate']
    recent_rate = rect_detection_stats['recent_success_rate']
    recent_count = len(rect_detection_stats['recent_attempts'])

    return {
        'total_text': f"总计: {total}次",
        'success_rate_text': f"成功率: {success_rate:.1f}%",
        'recent_text': f"近{recent_count}次: {recent_rate:.1f}%"
    }

# ==================== 多帧稳定性检测辅助函数 ====================
def calculate_distance(point1, point2):
    """计算两点间的欧几里得距离"""
    if point1 is None or point2 is None:
        return float('inf')
    return ((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)**0.5

def add_detection_to_buffer(detection_result):
    """将检测结果添加到多帧缓冲区"""
    global multi_frame_detection

    buffer = multi_frame_detection['frame_buffer']
    buffer_size = multi_frame_detection['buffer_size']

    # 添加新的检测结果
    buffer.append(detection_result)

    # 保持缓冲区大小
    if len(buffer) > buffer_size:
        buffer.pop(0)  # 移除最旧的结果

def get_stable_detection():
    """从多帧缓冲区获取稳定的检测结果"""
    global multi_frame_detection

    buffer = multi_frame_detection['frame_buffer']
    stable_threshold = multi_frame_detection['stable_threshold']
    position_threshold = multi_frame_detection['position_threshold']

    if len(buffer) < stable_threshold:
        return None, 0.0  # 缓冲区数据不足

    # 统计成功检测的帧数
    successful_frames = [frame for frame in buffer if frame['success']]

    if len(successful_frames) < stable_threshold:
        return None, 0.0  # 成功帧数不足

    # 计算位置的一致性
    centers = [frame['center'] for frame in successful_frames if frame['center'] is not None]

    if len(centers) < stable_threshold:
        return None, 0.0  # 有效中心点不足

    # 计算平均中心点
    avg_x = sum(center[0] for center in centers) / len(centers)
    avg_y = sum(center[1] for center in centers) / len(centers)
    avg_center = (avg_x, avg_y)

    # 计算位置稳定性（所有中心点与平均中心点的距离）
    distances = [calculate_distance(center, avg_center) for center in centers]
    max_distance = max(distances)

    if max_distance > position_threshold:
        return None, 0.0  # 位置不够稳定

    # 计算置信度（基于成功率和位置稳定性）
    success_rate = len(successful_frames) / len(buffer)
    position_stability = 1.0 - (max_distance / position_threshold)
    confidence = (success_rate + position_stability) / 2.0

    return avg_center, confidence

def should_send_data(current_center, confidence):
    """判断是否应该发送数据给STM32"""
    global multi_frame_detection

    confidence_threshold = multi_frame_detection['confidence_threshold']
    send_threshold = multi_frame_detection['send_threshold']
    last_send_center = multi_frame_detection['last_send_center']

    # 置信度检查
    if confidence < confidence_threshold:
        return False

    # 位置变化检查
    if last_send_center is not None:
        distance = calculate_distance(current_center, last_send_center)
        if distance < send_threshold:
            return False  # 位置变化太小，不需要发送

    return True

# 串口通信全局变量
uart = None
SERIAL_PORT = UART.UART2
BAUD_RATE = 115200
TX_PIN = 11
RX_PIN = 12

# 数据包格式
PACKET_HEADER = [0xAA, 0x55]
PACKET_TAIL = [0x0D, 0x0A]
SCREEN_CENTER_X = 200  # 400x240图像的中心
SCREEN_CENTER_Y = 120

# ==================== 重要改进：优化的检测参数设置 ====================
# 矩形检测参数 - 使用LAB颜色空间
RECT_THRESHOLD = [(0, 100, -20, 20, -20, 20)]  # 黑色矩形的LAB阈值
MIN_RECT_AREA = 3000        # 降低最小面积要求，提高检测率
MAX_RECT_AREA = 50000       # 添加最大面积限制，过滤噪声
MIN_RECT_ASPECT_RATIO = 0.3 # 最小宽高比，过滤细长形状
MAX_RECT_ASPECT_RATIO = 3.0 # 最大宽高比，过滤细长形状

# 激光点检测参数
LASER_THRESHOLD = [
    (47, 80, 9, 91, -55, 63),
    (16, 37, 23, 74, -48, 52)
]
MIN_LASER_PIXELS = 20

# ROI设置
USE_ROI = False
ROI_X = 50
ROI_Y = 30
ROI_WIDTH = 300
ROI_HEIGHT = 180

# 图像参数
picture_width = 400
picture_height = 240
sensor_id = 2
DISPLAY_MODE = "LCD"
DISPLAY_WIDTH = 800
DISPLAY_HEIGHT = 480
SCALE_FACTOR = 2.0
SCALED_WIDTH = int(picture_width * SCALE_FACTOR)
SCALED_HEIGHT = int(picture_height * SCALE_FACTOR)

# 按键定义
BUTTON_WIDTH = 150
BUTTON_HEIGHT = 60
BUTTON_Y = SCALED_HEIGHT - BUTTON_HEIGHT - 10
BUTTON_SPACING = 200
BUTTON_START_X = 50
BUTTON1_POS = (BUTTON_START_X, BUTTON_Y)
BUTTON2_POS = (BUTTON_START_X + BUTTON_SPACING, BUTTON_Y)
BUTTON3_POS = (BUTTON_START_X + BUTTON_SPACING * 2, BUTTON_Y)
BUTTON_ACTIVE_COLOR = (0, 255, 0)
BUTTON_INACTIVE_COLOR = (80, 80, 80)
BUTTON_TEXT_COLOR = (255, 255, 255)

sensor = None

def init_uart():
    """初始化串口通信"""
    global uart

    try:
        # 配置引脚功能
        fpioa = FPIOA()
        fpioa.set_function(TX_PIN, FPIOA.UART2_TXD)
        fpioa.set_function(RX_PIN, FPIOA.UART2_RXD)

        # 初始化串口
        uart = UART(SERIAL_PORT, BAUD_RATE)
        print(f"✅ 串口初始化成功 - 波特率: {BAUD_RATE}")
        return True

    except Exception as e:
        print(f"❌ 串口初始化失败: {e}")
        return False

def send_coordinates(rect_center, screen_center):
    """发送坐标数据给STM32"""
    global uart

    if uart is None:
        return False

    try:
        # 准备数据包
        packet = []
        packet.extend(PACKET_HEADER)

        # 矩形中心坐标
        if rect_center is not None:
            rect_x, rect_y = int(rect_center[0]), int(rect_center[1])
        else:
            rect_x, rect_y = 0xFFFF, 0xFFFF

        # 屏幕中心坐标
        screen_x, screen_y = int(screen_center[0]), int(screen_center[1])

        # 将16位坐标拆分为两个8位
        packet.extend([(rect_x >> 8) & 0xFF, rect_x & 0xFF])
        packet.extend([(rect_y >> 8) & 0xFF, rect_y & 0xFF])
        packet.extend([(screen_x >> 8) & 0xFF, screen_x & 0xFF])
        packet.extend([(screen_y >> 8) & 0xFF, screen_y & 0xFF])

        # 计算校验和
        checksum = sum(packet[2:]) % 256
        packet.append(checksum)
        packet.extend(PACKET_TAIL)

        # 发送数据
        uart.write(bytes(packet))

        # 调试输出
        if rect_center is not None:
            print(f"📡 发送坐标 - 矩形:({rect_x},{rect_y}) 屏幕:({screen_x},{screen_y})")
        else:
            print(f"📡 发送坐标 - 无矩形 屏幕:({screen_x},{screen_y})")

        return True

    except Exception as e:
        print(f"❌ 串口发送失败: {e}")
        return False

# ==================== 重要改进：统一的LAB阈值管理系统 ====================
def save_threshold_to_dict():
    """保存阈值到字典 - 统一使用LAB格式"""
    global threshold_dict, threshold_edit_mode, threshold_current
    try:
        print("开始保存LAB阈值到字典...")

        if threshold_edit_mode == 'rect':
            # 矩形阈值：直接保存6个LAB参数
            threshold_dict[threshold_edit_mode].append(tuple(threshold_current))
            print("Success: 矩形LAB阈值已保存到字典 " + str(threshold_current))
        elif threshold_edit_mode == 'laser':
            # 激光点阈值：直接保存6个LAB参数
            threshold_dict[threshold_edit_mode].append(tuple(threshold_current))
            print("Success: 激光点LAB阈值已保存到字典 " + str(threshold_current))

        # 更新全局阈值变量
        update_global_thresholds_from_dict()
        print("Success: LAB阈值保存完成")
        return True

    except Exception as e:
        print("Error: 保存LAB阈值失败 " + str(e))
        return False

def update_global_thresholds_from_dict():
    """从字典更新全局阈值变量 - 统一LAB格式"""
    global RECT_THRESHOLD, LASER_THRESHOLD, threshold_dict
    try:
        # 更新矩形LAB阈值
        if 'rect' in threshold_dict and len(threshold_dict['rect']) > 0:
            RECT_THRESHOLD[0] = threshold_dict['rect'][-1]  # 使用最新保存的LAB阈值
            print("Update: 矩形LAB阈值更新为 " + str(RECT_THRESHOLD[0]))

        # 更新激光点LAB阈值
        if 'laser' in threshold_dict and len(threshold_dict['laser']) > 0:
            LASER_THRESHOLD[0] = threshold_dict['laser'][-1]  # 使用最新保存的LAB阈值
            print("Update: 激光点LAB阈值更新为 " + str(LASER_THRESHOLD[0]))

    except Exception as e:
        print("Error: 更新全局LAB阈值失败 " + str(e))

def load_threshold_from_dict():
    """从字典加载阈值 - 统一LAB格式"""
    global threshold_dict, threshold_current, threshold_edit_mode
    try:
        print("开始从字典加载LAB阈值...")

        if threshold_edit_mode == 'rect':
            # 加载最新的矩形LAB阈值
            if 'rect' in threshold_dict and len(threshold_dict['rect']) > 0:
                latest_rect = threshold_dict['rect'][-1]  # 获取最新保存的LAB阈值
                threshold_current = list(latest_rect)
                print("Success: 矩形LAB阈值已从字典加载 " + str(latest_rect))
            else:
                print("Warning: 字典中无矩形LAB阈值，使用默认值")

        elif threshold_edit_mode == 'laser':
            # 加载最新的激光点LAB阈值
            if 'laser' in threshold_dict and len(threshold_dict['laser']) > 0:
                latest_laser = threshold_dict['laser'][-1]  # 获取最新保存的LAB阈值
                threshold_current = list(latest_laser)
                print("Success: 激光点LAB阈值已从字典加载 " + str(latest_laser))
            else:
                print("Warning: 字典中无激光点LAB阈值，使用默认值")

        # 同时更新全局阈值变量
        update_global_thresholds_from_dict()
        print("Success: LAB阈值加载完成")
        return True

    except Exception as e:
        print("Error: 加载LAB阈值失败 " + str(e))
        return False

def get_default_thresholds():
    """获取默认LAB阈值参数"""
    global RECT_THRESHOLD, LASER_THRESHOLD, MIN_RECT_AREA, MIN_LASER_PIXELS

    # 恢复默认LAB阈值
    RECT_THRESHOLD = [(0, 100, -20, 20, -20, 20)]  # 黑色矩形LAB阈值
    LASER_THRESHOLD = [(47, 80, 9, 91, -55, 63), (16, 37, 23, 74, -48, 52)]  # 激光点LAB阈值
    MIN_RECT_AREA = 3000
    MIN_LASER_PIXELS = 20

    print("🔄 已恢复默认LAB阈值参数")

# ==================== 重要改进：增强的LAB颜色空间矩形检测算法 ====================
def detect_rectangles(img):
    """
    使用LAB颜色空间检测矩形 - 重要算法优化
    1. 增强的噪声过滤（形态学操作）
    2. 严格的矩形特征验证
    3. 多帧稳定性检测
    4. 智能数据发送控制
    """
    try:
        # ==================== 重要修复：保护原始彩色图像 ====================
        # ROI处理 - 始终创建副本以保护原始彩色图像
        if USE_ROI:
            detect_img = img.copy(roi=(ROI_X, ROI_Y, ROI_WIDTH, ROI_HEIGHT))
        else:
            detect_img = img.copy()  # 重要：创建副本，避免修改原始彩色图像

        # ==================== 核心改进：LAB颜色空间二值化 + 形态学操作 ====================
        # 使用LAB颜色空间直接进行二值化，提高光照适应性
        binary_img = detect_img.binary(RECT_THRESHOLD)

        # ==================== 重要改进：噪声过滤增强 ====================
        # 1. 腐蚀操作去除小噪声点
        binary_img.erode(1, threshold=1)  # 轻微腐蚀，去除细小噪声

        # 2. 膨胀操作恢复主要形状
        binary_img.dilate(1, threshold=1)  # 膨胀恢复，填补小空洞

        # 查找所有满足面积阈值的矩形
        rects = binary_img.find_rects(threshold=MIN_RECT_AREA)

        # 初始化检测结果
        detection_success = False
        final_center = None

        if not rects:
            # 添加到多帧缓冲区
            add_detection_to_buffer({'success': False, 'center': None, 'confidence': 0.0})
            update_detection_stats(False)
            return False, 0, [], None

        # ==================== 重要改进：增强的矩形特征验证 ====================
        validated_rects = []
        for rect in rects:
            # 1. 基础面积和宽高比检查
            area = rect.w() * rect.h()
            aspect_ratio = max(rect.w(), rect.h()) / min(rect.w(), rect.h())

            if area < MIN_RECT_AREA or area > MAX_RECT_AREA:
                continue
            if aspect_ratio < MIN_RECT_ASPECT_RATIO or aspect_ratio > MAX_RECT_ASPECT_RATIO:
                continue

            # 2. 矩形度检查（轮廓面积与外接矩形面积的比值）
            rect_corners = rect.corners()
            if len(rect_corners) != 4:
                continue

            # 计算矩形度（应该接近1.0）
            rect_area = rect.w() * rect.h()
            # 这里简化处理，实际应该计算轮廓面积
            rectangularity = 1.0  # 简化版本，后续可以改进

            if rectangularity < 0.7:  # 矩形度阈值
                continue

            # 3. 角度检查（检查是否接近矩形的角度）
            corners = rect_corners
            angles = []
            for i in range(4):
                p1 = corners[i]
                p2 = corners[(i + 1) % 4]
                p3 = corners[(i + 2) % 4]

                # 计算角度（简化版本）
                import math
                v1 = (p1[0] - p2[0], p1[1] - p2[1])
                v2 = (p3[0] - p2[0], p3[1] - p2[1])

                # 计算向量夹角
                dot_product = v1[0] * v2[0] + v1[1] * v2[1]
                mag1 = (v1[0]**2 + v1[1]**2)**0.5
                mag2 = (v2[0]**2 + v2[1]**2)**0.5

                if mag1 > 0 and mag2 > 0:
                    cos_angle = dot_product / (mag1 * mag2)
                    cos_angle = max(-1, min(1, cos_angle))  # 限制范围
                    angle = math.acos(cos_angle) * 180 / math.pi
                    angles.append(angle)

            # 检查角度是否接近90度
            if len(angles) == 4:
                angle_deviation = sum(abs(angle - 90) for angle in angles) / 4
                if angle_deviation > 30:  # 角度偏差阈值
                    continue

            # 4. 边缘直线度检查（简化版本）
            # 这里可以添加更复杂的边缘检查逻辑
            edge_straightness = 1.0  # 简化版本

            if edge_straightness < 0.8:  # 直线度阈值
                continue

            # 通过所有验证的矩形
            validated_rects.append({
                'rect': rect,
                'area': area,
                'aspect_ratio': aspect_ratio,
                'rectangularity': rectangularity,
                'angle_deviation': angle_deviation if 'angle_deviation' in locals() else 0,
                'edge_straightness': edge_straightness
            })

        if not validated_rects:
            add_detection_to_buffer({'success': False, 'center': None, 'confidence': 0.0})
            update_detection_stats(False)
            print("⚠️ LAB检测：所有矩形未通过特征验证")
            return False, 0, [], None

        # ==================== 重要改进：优化的矩形配对算法 ====================
        # 按面积排序（从大到小）
        validated_rects.sort(key=lambda r: r['area'], reverse=True)

        # 寻找最佳的内外矩形对
        best_pair = None
        min_center_distance = float('inf')
        best_inner_center = None
        best_outer_center = None
        best_confidence = 0.0

        # 尝试所有可能的矩形对组合
        for i in range(len(validated_rects)):
            for j in range(i+1, len(validated_rects)):
                rect1_data = validated_rects[i]
                rect2_data = validated_rects[j]
                rect1 = rect1_data['rect']
                rect2 = rect2_data['rect']

                # 确保一个是外框一个是内框（外框面积大于内框）
                if rect1_data['area'] < rect2_data['area']:
                    inner_data = rect1_data
                    outer_data = rect2_data
                else:
                    inner_data = rect2_data
                    outer_data = rect1_data

                inner_rect = inner_data['rect']
                outer_rect = outer_data['rect']

                # 计算中心点
                inner_corners = inner_rect.corners()
                inner_center_x = sum([corner[0] for corner in inner_corners]) / 4
                inner_center_y = sum([corner[1] for corner in inner_corners]) / 4

                outer_corners = outer_rect.corners()
                outer_center_x = sum([corner[0] for corner in outer_corners]) / 4
                outer_center_y = sum([corner[1] for corner in outer_corners]) / 4

                # 计算中心点距离
                distance = ((inner_center_x - outer_center_x)**2 + (inner_center_y - outer_center_y)**2)**0.5

                # 计算配对置信度（基于多个因素）
                distance_score = max(0, 1.0 - distance / 20.0)  # 距离越近分数越高
                area_ratio = inner_data['area'] / outer_data['area']
                area_score = 1.0 - abs(area_ratio - 0.5)  # 理想比例约0.5
                feature_score = (inner_data['rectangularity'] + outer_data['rectangularity'] +
                               inner_data['edge_straightness'] + outer_data['edge_straightness']) / 4.0

                pair_confidence = (distance_score + area_score + feature_score) / 3.0

                # 寻找最佳配对
                if distance < min_center_distance and pair_confidence > 0.5:
                    min_center_distance = distance
                    best_pair = (inner_rect, outer_rect)
                    best_inner_center = (inner_center_x, inner_center_y)
                    best_outer_center = (outer_center_x, outer_center_y)
                    best_confidence = pair_confidence

        # ==================== 重要改进：更宽松但智能的中心重合判断 ====================
        CENTER_MATCH_THRESHOLD = 12.0  # 适当放宽阈值

        if best_pair is not None and min_center_distance <= CENTER_MATCH_THRESHOLD:
            inner_rect, outer_rect = best_pair
            inner_center_x, inner_center_y = best_inner_center
            outer_center_x, outer_center_y = best_outer_center

            # 坐标转换
            if USE_ROI:
                inner_center_x = ROI_X + inner_center_x
                inner_center_y = ROI_Y + inner_center_y
                outer_center_x = ROI_X + outer_center_x
                outer_center_y = ROI_Y + outer_center_y

            # 计算平均中心点
            avg_center_x = (inner_center_x + outer_center_x) / 2
            avg_center_y = (inner_center_y + outer_center_y) / 2
            final_center = (avg_center_x, avg_center_y)

            # ==================== 重要改进：多帧稳定性检测 ====================
            # 添加当前检测结果到缓冲区
            add_detection_to_buffer({
                'success': True,
                'center': final_center,
                'confidence': best_confidence
            })

            # 获取稳定的检测结果
            stable_center, stable_confidence = get_stable_detection()

            if stable_center is not None and stable_confidence > multi_frame_detection['confidence_threshold']:
                detection_success = True
                final_center = stable_center

                # ==================== 重要改进：增强的可视化反馈 ====================
                # 绘制内外矩形 - 使用不同颜色区分
                for rect, color, label in zip([inner_rect, outer_rect],
                                            [(255, 0, 255), (0, 255, 0)],
                                            ["Inner", "Outer"]):
                    corners = rect.corners()
                    for j in range(4):
                        next_j = (j + 1) % 4
                        if USE_ROI:
                            x1, y1 = ROI_X + corners[j][0], ROI_Y + corners[j][1]
                            x2, y2 = ROI_X + corners[next_j][0], ROI_Y + corners[next_j][1]
                        else:
                            x1, y1 = corners[j][0], corners[j][1]
                            x2, y2 = corners[next_j][0], corners[next_j][1]
                        img.draw_line(x1, y1, x2, y2, color=color, thickness=3)

                # 绘制稳定的中心点标记
                img.draw_circle(int(stable_center[0]), int(stable_center[1]), 10,
                               color=(255, 0, 0), thickness=4)
                img.draw_string_advanced(int(stable_center[0]) - 30, int(stable_center[1]) - 50,
                                       16, "STABLE", color=(255, 0, 0))

                # 显示置信度信息
                img.draw_string_advanced(int(stable_center[0]) - 40, int(stable_center[1]) + 30,
                                       12, f"置信度: {stable_confidence:.2f}", color=(255, 255, 255))

                print(f"✅ 稳定LAB矩形检测：置信度 {stable_confidence:.3f}")

                # 更新成功统计
                update_detection_stats(True)

                # 返回稳定的检测结果
                return True, 2, [best_inner_center, best_outer_center], stable_center
            else:
                # 检测到矩形但不够稳定
                print(f"⚠️ LAB检测：矩形不够稳定，置信度 {stable_confidence:.3f}")
                update_detection_stats(False)
                return False, 0, [], None
        else:
            # 中心未重合或未找到合适配对
            add_detection_to_buffer({'success': False, 'center': None, 'confidence': 0.0})
            update_detection_stats(False)
            if best_pair is None:
                print("⚠️ LAB检测：未找到合适矩形对")
            else:
                print(f"⚠️ LAB检测：中心未重合，距离 {min_center_distance:.2f}px > {CENTER_MATCH_THRESHOLD}")
            return False, 0, [], None

    except Exception as e:
        print(f"LAB矩形检测错误: {e}")
        add_detection_to_buffer({'success': False, 'center': None, 'confidence': 0.0})
        update_detection_stats(False)
        return False, 0, [], None

# 激光点检测函数（保持与step6相同，已经使用LAB颜色空间）
def detect_laser_point(img):
    """检测图像中的蓝紫激光点 - 使用LAB颜色空间"""
    try:
        if USE_ROI:
            detect_img = img.copy(roi=(ROI_X, ROI_Y, ROI_WIDTH, ROI_HEIGHT))
        else:
            detect_img = img

        blobs = detect_img.find_blobs(LASER_THRESHOLD, False,
                                    x_stride=1, y_stride=1,
                                    pixels_threshold=MIN_LASER_PIXELS, margin=False)

        laser_centers = []
        if blobs:
            for i, blob in enumerate(blobs):
                laser_x = blob.x() + blob.w() / 2
                laser_y = blob.y() + blob.h() / 2

                if USE_ROI:
                    global_laser_x = ROI_X + laser_x
                    global_laser_y = ROI_Y + laser_y
                    img.draw_rectangle(ROI_X + blob.x(), ROI_Y + blob.y(),
                                     blob.w(), blob.h(),
                                     color=(0, 0, 255), thickness=2, fill=False)
                else:
                    global_laser_x = laser_x
                    global_laser_y = laser_y
                    img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                                     color=(0, 0, 255), thickness=2, fill=False)

                laser_centers.append((global_laser_x, global_laser_y))
                img.draw_circle(int(global_laser_x), int(global_laser_y), 3,
                               color=(0, 0, 255), thickness=2)
                img.draw_string_advanced(int(global_laser_x) - 15, int(global_laser_y) - 25,
                                       10, f"L{i+1}", color=(0, 0, 255))

            return True, len(blobs), laser_centers

        return False, 0, []

    except Exception as e:
        print(f"激光点检测错误: {e}")
        return False, 0, []

# ==================== 重要改进：统一的LAB阈值编辑界面 ====================
def show_threshold_edit_interface(img):
    """显示LAB阈值编辑界面 - 统一处理矩形和激光点"""
    global threshold_edit_mode, threshold_current

    # 清空背景
    img.draw_rectangle(0, 0, picture_width, picture_height,
                     color=(40, 40, 80), thickness=1, fill=True)

    if threshold_edit_mode == "rect":
        try:
            # ==================== 重要改进：全屏阈值预览 + 画中画彩色原图 ====================
            preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)

            # 1. 全屏LAB二值化预览作为背景界面
            preview_binary = preview_img.binary([tuple(threshold_current)])
            preview_fullscreen = preview_binary.to_rgb565()

            # 缩放二值化预览图到全屏400x240
            if preview_fullscreen.width() != picture_width or preview_fullscreen.height() != picture_height:
                # 计算缩放比例，保持宽高比
                scale_x = preview_fullscreen.width() / picture_width
                scale_y = preview_fullscreen.height() / picture_height
                scale = max(scale_x, scale_y)

                if scale > 1:
                    # 需要缩小
                    scale_factor = int(scale) + 1
                    preview_fullscreen.midpoint_pool(scale_factor, scale_factor)

                # 如果还是不匹配，进行裁剪或填充
                if preview_fullscreen.width() > picture_width or preview_fullscreen.height() > picture_height:
                    # 居中裁剪
                    crop_x = max(0, (preview_fullscreen.width() - picture_width) // 2)
                    crop_y = max(0, (preview_fullscreen.height() - picture_height) // 2)
                    crop_w = min(picture_width, preview_fullscreen.width())
                    crop_h = min(picture_height, preview_fullscreen.height())
                    preview_fullscreen = preview_fullscreen.copy(roi=(crop_x, crop_y, crop_w, crop_h))

            # 将全屏二值化预览作为背景，直接替换img的内容
            img.draw_image(preview_fullscreen, 0, 0)

            # 2. 叠加小尺寸彩色原图窗口（画中画效果）
            color_original = preview_img.copy()  # 保留彩色原图

            # 设置小窗口尺寸为100x75像素
            target_width = 100
            target_height = 75

            # 计算缩放比例
            if color_original.width() > target_width or color_original.height() > target_height:
                scale_x = color_original.width() // target_width + 1
                scale_y = color_original.height() // target_height + 1
                scale = max(scale_x, scale_y)
                color_original.midpoint_pool(scale, scale)

            # 如果缩放后仍然过大，进行裁剪
            if color_original.width() > target_width or color_original.height() > target_height:
                crop_x = max(0, (color_original.width() - target_width) // 2)
                crop_y = max(0, (color_original.height() - target_height) // 2)
                crop_w = min(target_width, color_original.width())
                crop_h = min(target_height, color_original.height())
                color_original = color_original.copy(roi=(crop_x, crop_y, crop_w, crop_h))

            # 放置在右上角，留出边距
            pip_x = picture_width - color_original.width() - 5
            pip_y = 5

            # 绘制小窗口边框（画中画效果）
            border_thickness = 2
            img.draw_rectangle(pip_x - border_thickness, pip_y - border_thickness,
                             color_original.width() + 2 * border_thickness,
                             color_original.height() + 2 * border_thickness,
                             color=(255, 255, 255), thickness=border_thickness, fill=True)

            # 叠加彩色原图
            img.draw_image(color_original, pip_x, pip_y)

            # 3. 叠加界面信息（半透明背景）
            # 标题区域背景
            img.draw_rectangle(0, 0, picture_width, 25, color=(0, 0, 0), thickness=1, fill=True)
            img.draw_string_advanced(5, 5, 16, "矩形LAB阈值编辑 - 全屏预览", color=(255, 255, 0))

            # 参数信息区域背景
            info_text = f"LAB: ({threshold_current[0]},{threshold_current[1]},{threshold_current[2]},{threshold_current[3]},{threshold_current[4]},{threshold_current[5]})"
            img.draw_rectangle(0, picture_height - 20, picture_width, 20, color=(0, 0, 0), thickness=1, fill=True)
            img.draw_string_advanced(5, picture_height - 18, 12, info_text, color=(255, 255, 255))

            # 画中画标签
            img.draw_string_advanced(pip_x, pip_y + color_original.height() + 3, 8, "彩色原图", color=(255, 255, 255))

        except Exception as e:
            print(f"矩形阈值预览错误: {e}")
            # 错误时显示简单界面
            img.draw_rectangle(0, 0, picture_width, picture_height, color=(40, 40, 80), thickness=1, fill=True)
            img.draw_string_advanced(60, 10, 18, "矩形LAB阈值编辑", color=(255, 255, 0))
            img.draw_string_advanced(10, 35, 12, f"LAB: ({threshold_current[0]},{threshold_current[1]},{threshold_current[2]},{threshold_current[3]},{threshold_current[4]},{threshold_current[5]})", color=(255, 255, 255))

    elif threshold_edit_mode == "laser":
        try:
            # 激光点LAB阈值预览 - 保持原有布局（激光点检测效果更适合彩色显示）
            preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)
            current_lab_threshold = [tuple(threshold_current)]
            blobs = preview_img.find_blobs(current_lab_threshold, False,
                                         x_stride=1, y_stride=1,
                                         pixels_threshold=MIN_LASER_PIXELS, margin=False)
            for blob in blobs:
                preview_img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                                         color=(0, 255, 255), thickness=2, fill=False)
                preview_img.draw_circle(blob.x() + blob.w()//2, blob.y() + blob.h()//2, 3,
                                       color=(255, 0, 255), thickness=2)
            if preview_img.width() > 150:
                scale = preview_img.width() // 150 + 1
                preview_img.midpoint_pool(scale, scale)
            img.draw_image(preview_img, picture_width - preview_img.width() - 10, 10)
            img.draw_string_advanced(60, 10, 18, "激光点LAB阈值编辑", color=(255, 255, 0))
            img.draw_string_advanced(10, 35, 12, f"LAB: ({threshold_current[0]},{threshold_current[1]},{threshold_current[2]},{threshold_current[3]},{threshold_current[4]},{threshold_current[5]})", color=(255, 255, 255))
        except Exception as e:
            print(f"激光点阈值预览错误: {e}")
            img.draw_string_advanced(60, 10, 18, "激光点LAB阈值编辑", color=(255, 255, 0))

    # 显示编辑模式和统计信息
    img.draw_string_advanced(120, 200, 14, "编辑模式: " + threshold_edit_mode.upper() + " (LAB)", color=(0, 255, 255))

    # 显示字典状态
    rect_count = len(threshold_dict['rect']) if 'rect' in threshold_dict else 0
    laser_count = len(threshold_dict['laser']) if 'laser' in threshold_dict else 0
    img.draw_string_advanced(100, 180, 12, f"LAB字典: 矩形{rect_count}个 激光点{laser_count}个", color=(0, 255, 0))

    # 绘制边框
    img.draw_rectangle(2, 2, picture_width-4, picture_height-4,
                     color=(255, 255, 0), thickness=2, fill=False)

def draw_threshold_edit_buttons(img):
    """绘制LAB阈值编辑按钮 - 统一处理6个参数"""
    global threshold_edit_mode, threshold_current

    button_color = (150, 150, 150)
    text_color = (0, 0, 0)
    active_color = (0, 255, 0)

    # 顶部控制按钮
    img.draw_rectangle(20, 20, 120, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(50, 30, 20, "返回", color=text_color)

    switch_color = active_color if threshold_edit_mode == "laser" else button_color
    img.draw_rectangle(660, 20, 120, 40, color=switch_color, thickness=2, fill=True)
    img.draw_string_advanced(690, 30, 20, "切换", color=text_color)

    # 底部控制按钮
    img.draw_rectangle(20, 420, 100, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(45, 430, 18, "重置", color=text_color)

    img.draw_rectangle(140, 420, 100, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(165, 430, 18, "保存", color=text_color)

    img.draw_rectangle(540, 420, 120, 40, color=(100, 200, 100), thickness=2, fill=True)
    img.draw_string_advanced(555, 430, 16, "保存到字典", color=text_color)

    img.draw_rectangle(680, 420, 120, 40, color=(100, 100, 200), thickness=2, fill=True)
    img.draw_string_advanced(695, 430, 16, "从字典加载", color=text_color)

    # ==================== 重要改进：统一的6参数LAB编辑界面 ====================
    # 矩形和激光点都使用相同的6参数LAB编辑界面
    param_names = ["L_min", "L_max", "A_min", "A_max", "B_min", "B_max"]
    for i in range(6):
        y_pos = 80 + i * 55

        # 减少按钮 (左侧)
        img.draw_rectangle(20, y_pos, 60, 35, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(40, y_pos + 8, 16, "-", color=text_color)

        # 参数显示
        param_value = threshold_current[i] if i < len(threshold_current) else 0
        img.draw_string_advanced(100, y_pos + 8, 14, f"{param_names[i]}: {param_value}", color=(255, 255, 255))

        # 增加按钮 (右侧)
        img.draw_rectangle(720, y_pos, 60, 35, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(740, y_pos + 8, 16, "+", color=text_color)

def check_threshold_edit_click(x, y):
    """检测LAB阈值编辑界面的按钮点击 - 统一处理"""
    global threshold_edit_mode, threshold_current, current_mode, RECT_THRESHOLD, LASER_THRESHOLD

    # 返回按钮
    if 20 <= x <= 140 and 20 <= y <= 60:
        print("点击返回按钮")
        current_mode = 1
        return True

    # 切换按钮
    if 660 <= x <= 780 and 20 <= y <= 60:
        print("点击切换按钮")
        if threshold_edit_mode == "rect":
            threshold_edit_mode = "laser"
            threshold_current = list(LASER_THRESHOLD[0])  # 加载激光点LAB阈值
        else:
            threshold_edit_mode = "rect"
            threshold_current = list(RECT_THRESHOLD[0])   # 加载矩形LAB阈值
        print(f"切换到 {threshold_edit_mode} 模式，LAB阈值: {threshold_current}")
        return True

    # 重置按钮
    if 20 <= x <= 120 and 420 <= y <= 460:
        print("✅ 点击重置按钮")
        if threshold_edit_mode == "rect":
            threshold_current = [0, 100, -20, 20, -20, 20]  # 黑色矩形默认LAB阈值
            print("🔄 重置矩形LAB阈值为默认值")
        else:
            threshold_current = [47, 80, 9, 91, -55, 63]    # 激光点默认LAB阈值
            print("🔄 重置激光点LAB阈值为默认值")
        return True

    # 保存按钮
    if 140 <= x <= 240 and 420 <= y <= 460:
        print("✅ 点击保存按钮")
        if threshold_edit_mode == "rect":
            RECT_THRESHOLD[0] = tuple(threshold_current)
            print(f"💾 保存矩形LAB阈值到内存: {RECT_THRESHOLD[0]}")
        else:
            LASER_THRESHOLD[0] = tuple(threshold_current)
            print(f"💾 保存激光点LAB阈值到内存: {LASER_THRESHOLD[0]}")
        return True

    # 保存到字典按钮
    if 540 <= x <= 660 and 420 <= y <= 460:
        print("✅ 点击保存到字典按钮")
        if save_threshold_to_dict():
            print("📁 LAB阈值已成功保存到字典")
        else:
            print("❌ 保存到字典失败")
        return True

    # 从字典加载按钮
    if 680 <= x <= 800 and 420 <= y <= 460:
        print("✅ 点击从字典加载按钮")
        if load_threshold_from_dict():
            print("📁 LAB阈值已从字典加载")
        else:
            print("❌ 从字典加载失败")
        return True

    # ==================== 重要改进：统一的6参数调整逻辑 ====================
    # 矩形和激光点都使用相同的6参数调整逻辑
    for i in range(6):
        y_pos = 80 + i * 55
        # 减少按钮
        if 20 <= x <= 80 and y_pos <= y <= y_pos + 35:
            if i < 2:  # L参数 0-100
                threshold_current[i] = max(0, threshold_current[i] - 2)
            else:  # A,B参数 -128到127
                threshold_current[i] = max(-128, threshold_current[i] - 2)
            print(f"减少参数{i} ({['L_min','L_max','A_min','A_max','B_min','B_max'][i]}): {threshold_current[i]}")
            return True
        # 增加按钮
        if 720 <= x <= 780 and y_pos <= y <= y_pos + 35:
            if i < 2:  # L参数 0-100
                threshold_current[i] = min(100, threshold_current[i] + 2)
            else:  # A,B参数 -128到127
                threshold_current[i] = min(127, threshold_current[i] + 2)
            print(f"增加参数{i} ({['L_min','L_max','A_min','A_max','B_min','B_max'][i]}): {threshold_current[i]}")
            return True

    return False

# 绘制主界面按键
def draw_buttons(img):
    """绘制主界面按键"""
    global current_mode

    button1_color = BUTTON_ACTIVE_COLOR if current_mode == 1 else BUTTON_INACTIVE_COLOR
    img.draw_rectangle(BUTTON1_POS[0], BUTTON1_POS[1], BUTTON_WIDTH, BUTTON_HEIGHT,
                      color=button1_color, thickness=3, fill=True)
    img.draw_string_advanced(BUTTON1_POS[0] + 30, BUTTON1_POS[1] + 20, 20,
                           "基础部分", color=BUTTON_TEXT_COLOR)

    button2_color = BUTTON_ACTIVE_COLOR if current_mode == 2 else BUTTON_INACTIVE_COLOR
    img.draw_rectangle(BUTTON2_POS[0], BUTTON2_POS[1], BUTTON_WIDTH, BUTTON_HEIGHT,
                      color=button2_color, thickness=3, fill=True)
    img.draw_string_advanced(BUTTON2_POS[0] + 30, BUTTON2_POS[1] + 20, 20,
                           "进阶部分", color=BUTTON_TEXT_COLOR)

    button3_color = BUTTON_ACTIVE_COLOR if current_mode == 3 else BUTTON_INACTIVE_COLOR
    img.draw_rectangle(BUTTON3_POS[0], BUTTON3_POS[1], BUTTON_WIDTH, BUTTON_HEIGHT,
                      color=button3_color, thickness=3, fill=True)
    img.draw_string_advanced(BUTTON3_POS[0] + 30, BUTTON3_POS[1] + 20, 20,
                           "阈值编辑", color=BUTTON_TEXT_COLOR)

    for pos in [BUTTON1_POS, BUTTON2_POS, BUTTON3_POS]:
        img.draw_rectangle(pos[0]-2, pos[1]-2, BUTTON_WIDTH+4, BUTTON_HEIGHT+4,
                         color=(255, 255, 255), thickness=2, fill=False)

def check_button_click(x, y):
    """检测主界面按键点击"""
    if (BUTTON1_POS[0] <= x <= BUTTON1_POS[0] + BUTTON_WIDTH and
        BUTTON1_POS[1] <= y <= BUTTON1_POS[1] + BUTTON_HEIGHT):
        return 1
    if (BUTTON2_POS[0] <= x <= BUTTON2_POS[0] + BUTTON_WIDTH and
        BUTTON2_POS[1] <= y <= BUTTON2_POS[1] + BUTTON_HEIGHT):
        return 2
    if (BUTTON3_POS[0] <= x <= BUTTON3_POS[0] + BUTTON_WIDTH and
        BUTTON3_POS[1] <= y <= BUTTON3_POS[1] + BUTTON_HEIGHT):
        return 3
    return None

# ==================== 主程序 ====================
try:
    print("第七步：LAB颜色空间矩形检测优化测试开始")
    print("🔧 重要改进：")
    print("   1. 矩形检测从灰度方案迁移到LAB颜色空间")
    print("   2. 优化矩形识别算法，提高识别成功率")
    print("   3. 添加识别率统计功能")
    print("   4. 统一阈值管理机制")

    # 初始化串口
    if not init_uart():
        print("⚠️  串口初始化失败，程序继续运行但无通信功能")

    # 传感器初始化
    sensor = Sensor(id=sensor_id)
    sensor.reset()
    sensor.set_framesize(width=picture_width, height=picture_height, chn=CAM_CHN_ID_0)
    sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)

    # 显示器初始化
    Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
    MediaManager.init()
    sensor.run()

    # 触摸屏初始化
    tp = TOUCH(0)
    clock = time.clock()

    current_mode = 1
    last_send_time = 0
    SEND_INTERVAL = 100

    # 初始化LAB阈值
    update_global_thresholds_from_dict()
    print("✅ LAB阈值系统初始化完成")
    print(f"   矩形LAB阈值: {RECT_THRESHOLD[0]}")
    print(f"   激光点LAB阈值: {LASER_THRESHOLD[0]}")

    print("系统初始化完成，开始LAB颜色空间检测...")

    frame_count = 0

    while True:
        clock.tick()
        os.exitpoint()

        try:
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            frame_count += 1

            # 处理触摸输入
            points = tp.read()
            if len(points) > 0:
                touch_x, touch_y = points[0].x, points[0].y

                if current_mode == 3:
                    threshold_clicked = check_threshold_edit_click(touch_x, touch_y)
                    if threshold_clicked:
                        time.sleep_ms(300)
                else:
                    clicked_button = check_button_click(touch_x, touch_y)
                    if clicked_button:
                        old_mode = current_mode
                        current_mode = clicked_button
                        if current_mode == 3:
                            threshold_edit_mode = "rect"
                            threshold_current = list(RECT_THRESHOLD[0])  # 加载矩形LAB阈值
                        print(f"🔄 界面切换: {old_mode} → {current_mode}")
                        time.sleep_ms(300)

            # ==================== 重要改进：增强的主界面显示 ====================
            if current_mode == 1:
                # 基础界面 - LAB矩形检测 + 识别率统计
                img.draw_string_advanced(10, 10, 22, "K230激光定位系统", color=(255, 255, 0))
                img.draw_string_advanced(10, 32, 18, "LAB矩形+激光点检测", color=(0, 255, 255))
                img.draw_string_advanced(10, 52, 14, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))

                # 串口状态显示
                uart_status = "✅已连接" if uart else "❌未连接"
                img.draw_string_advanced(300, 10, 14, f"串口: {uart_status}", color=(0, 255, 0))

                # ==================== 重要改进：LAB矩形检测 ====================
                rect_success, rect_count, rect_centers, rect_total_center = detect_rectangles(img)

                # ==================== 重要改进：识别率统计显示 ====================
                stats = get_detection_stats_text()
                img.draw_string_advanced(10, 75, 14, stats['total_text'], color=(255, 255, 255))
                img.draw_string_advanced(10, 95, 14, stats['success_rate_text'], color=(0, 255, 0) if rect_detection_stats['success_rate'] > 50 else (255, 255, 0))
                img.draw_string_advanced(10, 115, 14, stats['recent_text'], color=(0, 255, 0) if rect_detection_stats['recent_success_rate'] > 50 else (255, 255, 0))

                # 显示检测结果
                if rect_success:
                    img.draw_string_advanced(10, 140, 14, f"LAB矩形检测成功! 数量: {rect_count}", color=(0, 255, 0))
                else:
                    img.draw_string_advanced(10, 140, 14, "LAB矩形检测失败", color=(255, 0, 0))

                # 显示当前LAB阈值信息
                img.draw_string_advanced(10, 160, 12, f"矩形LAB: {RECT_THRESHOLD[0][:2]}...", color=(200, 200, 200))

                # ==================== 重要改进：智能数据发送控制 ====================
                current_time = time.ticks_ms()
                if current_time - last_send_time >= SEND_INTERVAL:
                    # 只有在检测成功且满足发送条件时才发送数据
                    if rect_success and rect_total_center is not None:
                        # 获取稳定的检测结果
                        stable_center, stable_confidence = get_stable_detection()

                        if stable_center is not None and should_send_data(stable_center, stable_confidence):
                            # 发送稳定的高置信度数据
                            send_success = send_coordinates(stable_center, (SCREEN_CENTER_X, SCREEN_CENTER_Y))
                            if send_success:
                                # 更新最后发送的中心点
                                multi_frame_detection['last_send_center'] = stable_center
                                print(f"📡 发送稳定数据 - 置信度: {stable_confidence:.3f}")
                            last_send_time = current_time
                        else:
                            # 数据不够稳定，不发送
                            if stable_center is not None:
                                print(f"⏸️ 数据不稳定，暂停发送 - 置信度: {stable_confidence:.3f}")
                            else:
                                print("⏸️ 无稳定检测结果，暂停发送")
                    else:
                        # 检测失败时发送无效标记（可选）
                        # send_coordinates(None, (SCREEN_CENTER_X, SCREEN_CENTER_Y))
                        print("⏸️ 检测失败，暂停发送")
                        last_send_time = current_time

            elif current_mode == 2:
                # 进阶界面
                img.draw_string_advanced(10, 10, 22, "K230激光定位系统", color=(255, 255, 0))
                img.draw_string_advanced(10, 32, 18, "进阶部分：功能开发中", color=(255, 165, 0))
                uart_status = "✅已连接" if uart else "❌未连接"
                img.draw_string_advanced(300, 10, 14, f"串口: {uart_status}", color=(0, 255, 0))
                img.draw_string_advanced(50, 100, 20, "此功能正在开发中", color=(255, 255, 0))

                # 显示当前统计信息
                stats = get_detection_stats_text()
                img.draw_string_advanced(50, 130, 16, "当前识别统计:", color=(255, 255, 255))
                img.draw_string_advanced(50, 150, 14, stats['total_text'], color=(255, 255, 255))
                img.draw_string_advanced(50, 170, 14, stats['success_rate_text'], color=(0, 255, 0))

            elif current_mode == 3:
                # LAB阈值编辑界面
                show_threshold_edit_interface(img)

            # 图像放大和显示
            img = img.scale(SCALE_FACTOR, SCALE_FACTOR)
            if current_mode == 3:
                draw_threshold_edit_buttons(img)
            else:
                draw_buttons(img)
            Display.show_image(img, x=0, y=0)

            # 每100帧输出一次状态信息
            if frame_count % 100 == 0:
                mode_names = {1: "LAB基础检测", 2: "进阶部分", 3: "LAB阈值编辑"}
                mode_name = mode_names.get(current_mode, "未知")
                stats = get_detection_stats_text()
                print(f"📊 LAB检测状态 - 帧数: {frame_count}, FPS: {clock.fps():.1f}")
                print(f"📱 当前模式: {current_mode} ({mode_name})")
                print(f"📈 识别统计: {stats['success_rate_text']}, {stats['recent_text']}")
                print(f"🎯 LAB阈值: 矩形{RECT_THRESHOLD[0][:2]}... 激光点{LASER_THRESHOLD[0][:2]}...")
                print("=" * 60)

        except Exception as e:
            print(f"主循环错误: {e}")
            time.sleep_ms(100)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    print("清理资源...")
    # 输出最终统计信息
    final_stats = get_detection_stats_text()
    print("📊 最终识别统计:")
    print(f"   {final_stats['total_text']}")
    print(f"   {final_stats['success_rate_text']}")
    print(f"   {final_stats['recent_text']}")

    if isinstance(sensor, Sensor):
        sensor.stop()
    if uart:
        uart.deinit()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
    print("第七步LAB颜色空间优化测试完成")
    print("🎉 重要改进总结:")
    print("   ✅ 矩形检测已迁移到LAB颜色空间")
    print("   ✅ 识别算法已优化，提高成功率")
    print("   ✅ 识别率统计功能已添加")
    print("   ✅ 阈值管理机制已统一")
