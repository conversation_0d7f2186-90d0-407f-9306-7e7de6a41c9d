# Step12 双模式切换系统 - 功能说明文档

## 概述
Step12在Step11的基础上添加了双模式切换系统，实现了基础模式和进阶模式的无缝切换，为后续功能扩展提供了良好的架构基础。

## 核心功能

### 1. 双模式切换系统
- **基础模式 (MODE_BASIC)**: 对应Step11的所有功能
  - YOLO模型检测
  - STM32串口通信
  - 实时可视化显示
  - 性能监控
  
- **进阶模式 (MODE_ADVANCED)**: 为后续扩展预留
  - 多目标跟踪功能预留
  - 高级算法接口预留
  - 扩展功能模块预留

### 2. 模式切换按钮
- **按钮位置**: 屏幕左下角
- **按钮样式**: 
  - 激活状态：绿色背景 + 黑色文字
  - 非激活状态：灰色边框 + 白色文字
- **按钮尺寸**: 120x40像素，间距10像素

### 3. 状态管理
- **当前模式显示**: 信息面板实时显示当前模式
- **模式切换反馈**: 控制台输出切换信息
- **状态持久化**: 运行期间保持模式状态

## 新增配置

### 模式切换配置 (MODE_BUTTON_CONFIG)
```python
MODE_BUTTON_CONFIG = {
    'button_width': 120,               # 按钮宽度
    'button_height': 40,               # 按钮高度
    'button_spacing': 10,              # 按钮间距
    'button_margin_x': 20,             # 按钮左边距
    'button_margin_y': 400,            # 按钮上边距
    'active_color': (0, 255, 0),      # 激活状态颜色（绿色）
    'inactive_color': (128, 128, 128), # 非激活状态颜色（灰色）
    'text_color': (255, 255, 255),    # 文字颜色（白色）
    'border_thickness': 3,             # 边框厚度
}
```

### 可视化配置更新
```python
VISUALIZATION_CONFIG = {
    # ... 原有配置 ...
    'draw_mode_buttons': True,         # 绘制模式切换按钮
}
```

## 新增函数

### 1. draw_mode_buttons(osd_img)
- **功能**: 绘制模式切换按钮
- **参数**: osd_img - OSD图像对象
- **特性**: 
  - 根据当前模式高亮显示对应按钮
  - 支持自定义按钮样式和位置
  - 异常处理确保系统稳定性

### 2. switch_mode()
- **功能**: 执行模式切换
- **返回值**: 切换成功返回True，失败返回False
- **特性**:
  - 全局模式状态管理
  - 切换反馈信息输出
  - 异常处理机制

### 3. check_button_click(x, y)
- **功能**: 检测按钮点击（为触摸屏交互预留）
- **参数**: x, y - 点击坐标
- **返回值**: 点击的模式或None
- **说明**: 当前版本为预留接口，实际使用自动切换演示

## 界面布局

### 信息面板更新
- **系统标题**: "Step12 双模式系统 - [当前模式]"
- **模式颜色**: 基础模式（绿色），进阶模式（橙色）
- **位置**: 屏幕左上角

### 按钮布局
```
屏幕左下角:
┌─────────────┐  ┌─────────────┐
│  基础模式   │  │  进阶模式   │
└─────────────┘  └─────────────┘
```

## 使用方法

### 1. 运行系统
```bash
python "step by step\step12_双模式切换系统.py"
```

### 2. 模式切换
- **自动切换**: 每300帧自动切换一次（演示模式）
- **手动切换**: 可通过修改代码实现触摸屏或按键控制

### 3. 功能验证
```bash
python "step by step\test_mode_switch.py"
```

## 扩展指南

### 1. 添加新模式
1. 在模式定义中添加新常量
2. 更新switch_mode()函数
3. 添加对应的按钮绘制逻辑
4. 实现模式特定功能

### 2. 实现触摸屏交互
1. 导入触摸屏库
2. 在主循环中添加触摸检测
3. 调用check_button_click()函数
4. 根据返回值执行相应操作

### 3. 进阶模式功能开发
1. 在detection()函数中添加模式判断
2. 实现进阶模式特定的处理逻辑
3. 更新可视化显示
4. 添加相应的配置选项

## 技术特点

### 1. 向后兼容
- 完全保持Step11的所有功能
- 基础模式下行为与Step11完全一致
- 不影响现有的YOLO检测和串口通信

### 2. 扩展性设计
- 模块化的模式管理
- 清晰的接口定义
- 易于添加新功能

### 3. 用户体验
- 直观的视觉反馈
- 流畅的模式切换
- 清晰的状态显示

## 注意事项

1. **性能影响**: 模式切换不会影响系统性能
2. **资源管理**: 所有资源在模式切换时保持稳定
3. **错误处理**: 完善的异常处理确保系统稳定性
4. **扩展预留**: 进阶模式为功能扩展预留，当前仅显示界面差异

## 后续开发建议

1. **触摸屏集成**: 实现真正的用户交互
2. **配置文件**: 将模式配置外部化
3. **功能模块**: 为进阶模式开发具体功能
4. **性能优化**: 针对不同模式优化性能参数
