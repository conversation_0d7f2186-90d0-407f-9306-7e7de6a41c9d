# bilibili搜索学不会电磁场看教程
# K230矩形中心激光定位系统 - 第十二步：双模式切换系统
# 基于Step11进行扩展，增加基础模式和扩展模式切换功能
#
# Step12核心功能：
# 1. 双模式切换 - 基础模式（矩形中心点）+ 扩展模式（动态移动点）
# 2. 基础模式 - 继承Step11的YOLO检测和矩形中心点输出
# 3. 扩展模式 - 在矩形中心点周围生成动态移动点
# 4. 可视化界面 - 模式按钮、虚拟圆、移动点显示
# 5. 串口通信 - 根据模式发送不同的坐标数据
#
# 设计目标：
# - 提供灵活的操作模式选择
# - 保持系统稳定性和性能
# - 增强用户交互体验
# - 维持STM32通信协议兼容性

import os
import ujson
import aicube
from media.sensor import *
from media.display import *
from media.media import *
from time import *
import nncase_runtime as nn
import ulab.numpy as np
import time
import utime
import image
import random
import gc
import math
from machine import UART
from machine import FPIOA

# ==================== Step12新增：双模式配置 ====================
# 模式定义
MODE_BASIC = 0      # 基础模式：矩形中心点
MODE_EXTENDED = 1   # 扩展模式：动态移动点

# 模式配置（重新设计：纵向布局优化）
MODE_CONFIG = {
    'current_mode': MODE_BASIC,     # 当前模式（默认基础模式）

    # 按钮尺寸优化（增大触摸面积）
    'button_width': 90,             # 按钮宽度（优化）
    'button_height': 70,            # 按钮高度（增大）

    # 纵向布局配置（屏幕右侧边缘）
    'button_x': 700,                # 按钮X坐标（右侧边缘）
    'basic_button_y': 50,           # 基础模式按钮Y坐标（上方）
    'extended_button_y': 140,       # 扩展模式按钮Y坐标（下方）
    'button_spacing': 20,           # 按钮间距

    # 按钮样式配置
    'active_color': (0, 200, 0),    # 激活状态颜色（绿色）
    'inactive_color': (100, 100, 100), # 非激活状态颜色（灰色）
    'border_color': (255, 255, 255), # 边框颜色（白色）
    'text_color': (255, 255, 255),   # 文字颜色（白色）
    'border_width': 2,               # 边框宽度
}

# 扩展模式配置
EXTENDED_CONFIG = {
    'circle_radius': 50,            # 虚拟圆半径（像素）
    'movement_speed': 0.14,          # 移动速度（弧度/秒）
    'movement_amplitude': 1.0,      # 摆动幅度（0-1）
    'movement_mode': 'circular',    # 移动模式：circular（圆周）、pendulum（摆动）
    'show_virtual_circle': True,    # 显示虚拟圆
    'circle_color': (100, 100, 255), # 虚拟圆颜色（蓝色）
    'moving_point_color': (255, 100, 100), # 移动点颜色（红色）
    'moving_point_size': 8,         # 移动点大小
}

# ==================== Step12继承：STM32串口通信配置 ====================
# 串口配置
SERIAL_CONFIG = {
    'port': UART.UART2,
    'baudrate': 115200,
    'tx_pin': 11,
    'rx_pin': 12,
    'packet_header': [0xAA, 0x55],     # 包头
    'packet_tail': [0x0D, 0x0A],       # 包尾
    'send_interval': 20,               # 发送间隔(ms)
    'screen_center_x': 580,            # 屏幕中心X坐标
    'screen_center_y': 420,            # 屏幕中心Y坐标
}

# 可视化配置（Step12增强）
VISUALIZATION_CONFIG = {
    'draw_center_points': True,        # 绘制中心点
    'draw_screen_center': True,        # 绘制屏幕中心
    'draw_connection_line': True,      # 绘制连接线
    'draw_info_panel': True,           # 绘制信息面板
    'draw_mode_buttons': True,         # 绘制模式按钮
    'center_color': (255, 0, 0),      # 矩形中心点颜色（红色）
    'screen_center_color': (255, 255, 0),  # 屏幕中心颜色（黄色）
    'line_color': (0, 255, 255),      # 连接线颜色（青色）
    'button_active_color': (0, 255, 0),    # 激活按钮颜色（绿色）
    'button_inactive_color': (100, 100, 100), # 非激活按钮颜色（灰色）
}

# 全局变量
uart = None
last_send_time = 0
frame_count = 0
detection_count = 0

# FPS计算相关全局变量
fps_start_time = 0
fps_frame_count = 0
last_fps = 0.0

# Step12新增：扩展模式相关变量
movement_angle = 0.0        # 当前移动角度
last_movement_time = 0      # 上次移动时间
current_moving_point = None # 当前移动点坐标

# ==================== 继承det_video.py的配置 ====================
# 调试模式: True 会在屏幕上绘制检测框和信息, False 则不绘制
debug_mode = True

# 显示模式: "hdmi" 或 "lcd"
display_mode = "lcd"

# 常量定义（继承det_video.py）
def ALIGN_UP(x, align):
    return ((x + align - 1) & ~(align - 1))

# 摄像头通道定义
CAM_CHN_ID_0 = 0
CAM_CHN_ID_2 = 2

# 注意：像素格式常量和显示层常量由media.sensor和media.display自动导入

if display_mode == "lcd":
    DISPLAY_WIDTH = ALIGN_UP(800, 16)
    DISPLAY_HEIGHT = 480
else:
    DISPLAY_WIDTH = ALIGN_UP(1920, 16)
    DISPLAY_HEIGHT = 1080

OUT_RGB888P_WIDTH = ALIGN_UP(1080, 16)
OUT_RGB888P_HEIGH = 720

root_path = "/sdcard/new/"
config_path = root_path + "deploy_config.json"
deploy_conf = {}

# ==================== 继承det_video.py的工具类 ====================
class ScopedTiming:
    def __init__(self, info="", enable_profile=True):
        self.info = info
        self.enable_profile = enable_profile

    def __enter__(self):
        if self.enable_profile:
            self.start_time = time.time_ns()
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        if self.enable_profile:
            elapsed_time = time.time_ns() - self.start_time
            print(f"{self.info} took {elapsed_time / 1000000:.2f} ms")

def read_deploy_config(config_path):
    """读取配置文件 - 继承det_video.py"""
    with open(config_path, 'r') as json_file:
        try:
            config = ujson.load(json_file)
        except ValueError as e:
            print("JSON parsing error:", e)
            return None
    return config

# ==================== Step12核心：双模式切换算法 ====================
def switch_mode(new_mode):
    """切换操作模式 - 重新设计：确保状态持久性和可靠切换"""
    global movement_angle, last_movement_time, current_moving_point

    try:
        if new_mode not in [MODE_BASIC, MODE_EXTENDED]:
            print(f"❌ 无效模式参数: {new_mode}")
            print(f"   有效模式: {MODE_BASIC}(基础模式), {MODE_EXTENDED}(扩展模式)")
            return False

        old_mode = MODE_CONFIG['current_mode']
        old_mode_name = "基础模式" if old_mode == MODE_BASIC else "扩展模式"
        new_mode_name = "基础模式" if new_mode == MODE_BASIC else "扩展模式"

        # 验证模式切换的必要性
        if old_mode == new_mode:
            print(f"ℹ️  模式切换跳过: 当前已经是{new_mode_name}")
            print(f"   当前模式状态: {new_mode_name}已激活")
            return True

        print(f"🔄 开始模式切换: {old_mode_name} → {new_mode_name}")

        # 执行模式切换
        MODE_CONFIG['current_mode'] = new_mode

        # 重置坐标平滑器（避免不同模式间的坐标干扰）
        coordinate_smoother.reset()
        print(f"   🔄 坐标平滑器已重置")

        # 根据新模式进行相应配置
        if new_mode == MODE_EXTENDED:
            # 扩展模式配置
            movement_angle = 0.0
            last_movement_time = 0  # 重置为0，让calculate_moving_point重新初始化
            current_moving_point = None

            print(f"   ✅ 扩展模式功能已激活:")
            print(f"      - 动态移动点算法已启用")
            print(f"      - 虚拟圆半径: {EXTENDED_CONFIG['circle_radius']}px")
            print(f"      - 移动速度: {EXTENDED_CONFIG['movement_speed']}rad/s")
            print(f"      - 移动模式: {EXTENDED_CONFIG['movement_mode']}")
            print(f"      - 串口输出: 移动点坐标")
            print(f"   ✅ 状态持久性: 扩展模式将持续激活直到用户手动切换")

        else:  # MODE_BASIC
            # 基础模式配置
            print(f"   ✅ 基础模式功能已激活:")
            print(f"      - 矩形中心点算法已启用")
            print(f"      - 串口输出: 矩形中心点坐标")
            print(f"   ✅ 状态持久性: 基础模式将持续激活直到用户手动切换")

        # 验证模式切换结果
        if MODE_CONFIG['current_mode'] == new_mode:
            print(f"✅ 模式切换成功验证:")
            print(f"   期望模式: {new_mode_name}")
            print(f"   实际模式: {('基础模式' if MODE_CONFIG['current_mode'] == MODE_BASIC else '扩展模式')}")
            print(f"   切换状态: 成功")
            return True
        else:
            print(f"❌ 模式切换失败验证:")
            print(f"   期望模式: {new_mode_name}")
            print(f"   实际模式: {('基础模式' if MODE_CONFIG['current_mode'] == MODE_BASIC else '扩展模式')}")
            print(f"   切换状态: 失败")
            return False

    except Exception as e:
        print(f"❌ 模式切换异常: {e}")
        print(f"   切换请求: {old_mode_name} → {new_mode_name}")
        import traceback
        traceback.print_exc()
        return False

def calculate_moving_point(center_x, center_y):
    """计算扩展模式下的动态移动点 - Step12核心算法（修复：增强调试）"""
    global movement_angle, last_movement_time, current_moving_point

    try:
        if MODE_CONFIG['current_mode'] != MODE_EXTENDED:
            return None

        current_time = time.time()

        # 计算时间差
        if last_movement_time == 0:
            last_movement_time = current_time
            movement_angle = 0.0  # 确保角度初始化
            print(f"🔧 扩展模式初始化: 中心点({center_x}, {center_y})")
            return None

        time_delta = current_time - last_movement_time
        last_movement_time = current_time

        # 更新移动角度
        movement_angle += EXTENDED_CONFIG['movement_speed'] * time_delta

        # 保持角度在0-2π范围内
        if movement_angle >= 2 * math.pi:
            movement_angle -= 2 * math.pi

        # 根据移动模式计算位置
        radius = EXTENDED_CONFIG['circle_radius']

        if EXTENDED_CONFIG['movement_mode'] == 'circular':
            # 圆周运动
            moving_x = center_x + int(radius * math.cos(movement_angle))
            moving_y = center_y + int(radius * math.sin(movement_angle))

        elif EXTENDED_CONFIG['movement_mode'] == 'pendulum':
            # 摆动运动
            amplitude = EXTENDED_CONFIG['movement_amplitude']
            # 使用正弦函数创建摆动效果
            swing_angle = amplitude * math.sin(movement_angle)
            moving_x = center_x + int(radius * math.cos(swing_angle))
            moving_y = center_y + int(radius * math.sin(swing_angle))

        else:
            # 默认圆周运动
            moving_x = center_x + int(radius * math.cos(movement_angle))
            moving_y = center_y + int(radius * math.sin(movement_angle))

        # 边界检查
        moving_x = max(0, min(OUT_RGB888P_WIDTH - 1, moving_x))
        moving_y = max(0, min(OUT_RGB888P_HEIGH - 1, moving_y))

        current_moving_point = (moving_x, moving_y)

        # 调试信息：每50帧输出一次移动点信息
        if frame_count % 50 == 0:
            print(f"🔧 扩展模式调试:")
            print(f"   中心点: ({center_x}, {center_y})")
            print(f"   移动角度: {movement_angle:.2f}rad")
            print(f"   移动点: ({moving_x}, {moving_y})")
            print(f"   半径: {radius}px, 模式: {EXTENDED_CONFIG['movement_mode']}")

        return current_moving_point

    except Exception as e:
        print(f"移动点计算错误: {e}")
        print(f"   中心点: ({center_x}, {center_y})")
        print(f"   当前角度: {movement_angle}")
        return None

def get_output_coordinates(center_x, center_y):
    """获取输出坐标 - 根据模式返回不同坐标（修复：增强调试和验证）"""
    try:
        current_mode = MODE_CONFIG['current_mode']

        if current_mode == MODE_BASIC:
            # 基础模式：返回矩形中心点坐标
            if frame_count % 100 == 0:
                print(f"🔧 基础模式输出: 矩形中心点({center_x}, {center_y})")
            return center_x, center_y

        elif current_mode == MODE_EXTENDED:
            # 扩展模式：返回动态移动点坐标
            moving_point = calculate_moving_point(center_x, center_y)
            if moving_point:
                if frame_count % 100 == 0:
                    print(f"🔧 扩展模式输出: 移动点({moving_point[0]}, {moving_point[1]})")
                    print(f"   中心点: ({center_x}, {center_y})")
                    print(f"   偏移: ({moving_point[0] - center_x}, {moving_point[1] - center_y})")
                return moving_point[0], moving_point[1]
            else:
                # 如果移动点计算失败，返回中心点
                if frame_count % 100 == 0:
                    print(f"⚠️  扩展模式移动点计算失败，使用中心点({center_x}, {center_y})")
                return center_x, center_y

        else:
            # 默认返回中心点
            print(f"⚠️  未知模式{current_mode}，使用中心点({center_x}, {center_y})")
            return center_x, center_y

    except Exception as e:
        print(f"坐标获取错误: {e}")
        print(f"   中心点: ({center_x}, {center_y})")
        print(f"   当前模式: {MODE_CONFIG['current_mode']}")
        return center_x, center_y

# ==================== Step12继承：STM32串口通信模块 ====================
def init_uart():
    """初始化串口通信 - 继承Step11功能"""
    global uart
    try:
        fpioa = FPIOA()
        # 修复：使用正确的FPIOA属性名称
        fpioa.set_function(SERIAL_CONFIG['tx_pin'], FPIOA.UART2_TXD)
        fpioa.set_function(SERIAL_CONFIG['rx_pin'], FPIOA.UART2_RXD)

        # 修复：使用简化的UART初始化方式
        uart = UART(SERIAL_CONFIG['port'], SERIAL_CONFIG['baudrate'])
        print(f"✅ 串口初始化成功: UART{SERIAL_CONFIG['port']}, 波特率: {SERIAL_CONFIG['baudrate']}")
        return True
    except Exception as e:
        print(f"❌ 串口初始化失败: {e}")
        return False

def send_coordinates_to_stm32(rect_x, rect_y):
    """发送坐标数据到STM32 - 严格按照C结构体格式（Step12：根据模式发送不同坐标）"""
    global uart

    if uart is None:
        return False

    try:
        # 严格按照STM32 C结构体定义组装数据包
        packet = []

        # 包头 (2字节)
        packet.extend(SERIAL_CONFIG['packet_header'])  # 0xAA, 0x55

        # 矩形中心坐标 (4字节，大端序) - Step12根据模式发送不同坐标
        packet.extend([(rect_x >> 8) & 0xFF, rect_x & 0xFF])        # rect_x (大端序)
        packet.extend([(rect_y >> 8) & 0xFF, rect_y & 0xFF])        # rect_y (大端序)

        # 屏幕中心坐标 (4字节，大端序)
        screen_x = SERIAL_CONFIG['screen_center_x']
        screen_y = SERIAL_CONFIG['screen_center_y']
        packet.extend([(screen_x >> 8) & 0xFF, screen_x & 0xFF])    # screen_x (大端序)
        packet.extend([(screen_y >> 8) & 0xFF, screen_y & 0xFF])    # screen_y (大端序)

        # 校验和 (1字节) - 从rect_x到screen_y的所有8个数据字节
        checksum = sum(packet[2:]) % 256
        packet.append(checksum)

        # 包尾 (2字节)
        packet.extend(SERIAL_CONFIG['packet_tail'])  # 0x0D, 0x0A

        # 发送数据包 (总长度11字节)
        uart.write(bytes(packet))

        return True

    except Exception as e:
        print(f"❌ 串口发送失败: {e}")
        return False

# ==================== Step12增强：可视化绘制模块 ====================
def draw_mode_buttons(osd_img):
    """绘制模式切换按钮 - 重新设计：纵向布局和美化样式"""
    try:
        if not VISUALIZATION_CONFIG['draw_mode_buttons']:
            return

        # 纵向布局配置
        button_x = MODE_CONFIG['button_x']
        basic_y = MODE_CONFIG['basic_button_y']
        extended_y = MODE_CONFIG['extended_button_y']
        button_w = MODE_CONFIG['button_width']
        button_h = MODE_CONFIG['button_height']

        # 颜色配置
        active_color = MODE_CONFIG['active_color']
        inactive_color = MODE_CONFIG['inactive_color']
        border_color = MODE_CONFIG['border_color']
        text_color = MODE_CONFIG['text_color']
        border_width = MODE_CONFIG['border_width']

        # 调试信息：检查按钮坐标（纵向布局）
        if frame_count % 100 == 0:  # 每100帧输出一次调试信息
            current_mode_name = "基础模式" if MODE_CONFIG['current_mode'] == MODE_BASIC else "扩展模式"
            print(f"🎨 纵向按钮布局调试:")
            print(f"   屏幕尺寸: {DISPLAY_WIDTH}x{DISPLAY_HEIGHT}")
            print(f"   基础模式按钮: ({button_x}, {basic_y}) 尺寸: {button_w}x{button_h}")
            print(f"   扩展模式按钮: ({button_x}, {extended_y}) 尺寸: {button_w}x{button_h}")
            print(f"   当前激活模式: {current_mode_name}")

        # 绘制基础模式按钮
        if MODE_CONFIG['current_mode'] == MODE_BASIC:
            # 激活状态：绿色填充
            osd_img.draw_rectangle(button_x, basic_y, button_w, button_h, color=active_color, thickness=-1)
        else:
            # 非激活状态：灰色填充
            osd_img.draw_rectangle(button_x, basic_y, button_w, button_h, color=inactive_color, thickness=-1)

        # 基础模式按钮边框
        osd_img.draw_rectangle(button_x, basic_y, button_w, button_h, color=border_color, thickness=border_width)

        # 基础模式按钮文字（居中显示）
        text_x = button_x + 8  # 文字左边距
        text_y = basic_y + (button_h - 20) // 2   # 文字垂直居中
        osd_img.draw_string_advanced(text_x, text_y, 16, "基础模式", color=text_color)

        # 绘制扩展模式按钮
        if MODE_CONFIG['current_mode'] == MODE_EXTENDED:
            # 激活状态：绿色填充
            osd_img.draw_rectangle(button_x, extended_y, button_w, button_h, color=active_color, thickness=-1)
        else:
            # 非激活状态：灰色填充
            osd_img.draw_rectangle(button_x, extended_y, button_w, button_h, color=inactive_color, thickness=-1)

        # 扩展模式按钮边框
        osd_img.draw_rectangle(button_x, extended_y, button_w, button_h, color=border_color, thickness=border_width)

        # 扩展模式按钮文字（居中显示）
        text_x = button_x + 8  # 文字左边距
        text_y = extended_y + (button_h - 20) // 2 # 文字垂直居中
        osd_img.draw_string_advanced(text_x, text_y, 16, "扩展模式", color=text_color)

    except Exception as e:
        print(f"❌ 绘制模式按钮错误: {e}")
        print(f"   基础按钮坐标: ({button_x}, {basic_y})")
        print(f"   扩展按钮坐标: ({button_x}, {extended_y})")
        print(f"   屏幕尺寸: {DISPLAY_WIDTH}x{DISPLAY_HEIGHT}")
        import traceback
        traceback.print_exc()

def draw_screen_center(osd_img):
    """绘制屏幕中心点 - 继承Step11功能"""
    try:
        if not VISUALIZATION_CONFIG['draw_screen_center']:
            return

        center_x = SERIAL_CONFIG['screen_center_x'] * DISPLAY_WIDTH // OUT_RGB888P_WIDTH
        center_y = SERIAL_CONFIG['screen_center_y'] * DISPLAY_HEIGHT // OUT_RGB888P_HEIGH
        color = VISUALIZATION_CONFIG['screen_center_color']

        # 绘制中心圆点
        osd_img.draw_circle(center_x, center_y, 8, color=color, thickness=3)

        # 绘制十字标记
        cross_size = 15
        osd_img.draw_line(center_x - cross_size, center_y, center_x + cross_size, center_y,
                         color=color, thickness=3)
        osd_img.draw_line(center_x, center_y - cross_size, center_x, center_y + cross_size,
                         color=color, thickness=3)

        # 绘制标签
        osd_img.draw_string_advanced(center_x - 40, center_y - 40, 24, "SCREEN", color=color)

    except Exception as e:
        print(f"绘制屏幕中心错误: {e}")

def draw_target_center(osd_img, target_x, target_y):
    """绘制目标中心点 - 修改：移除文字标识，优化颜色"""
    try:
        if not VISUALIZATION_CONFIG['draw_center_points']:
            return

        # 使用红色表示矩形中心点
        center_color = (255, 0, 0)  # 红色

        # 绘制目标中心点（增强可见性）
        osd_img.draw_circle(target_x, target_y, 12, color=center_color, thickness=4, fill=True)
        osd_img.draw_circle(target_x, target_y, 15, color=(255, 255, 255), thickness=2, fill=False)  # 白色外圈

        # 移除文字标识，改为在图例中说明

    except Exception as e:
        print(f"绘制目标中心错误: {e}")

def draw_extended_mode_elements(osd_img, center_x, center_y, moving_point):
    """绘制扩展模式元素 - 修改：移除文字标识，优化颜色区分"""
    try:
        if MODE_CONFIG['current_mode'] != MODE_EXTENDED:
            return

        # 绘制虚拟圆（使用蓝色）
        if EXTENDED_CONFIG['show_virtual_circle'] and center_x is not None and center_y is not None:
            radius = EXTENDED_CONFIG['circle_radius'] * DISPLAY_WIDTH // OUT_RGB888P_WIDTH
            circle_color = (0, 100, 255)  # 蓝色

            # 确保半径合理
            radius = max(5, min(100, radius))  # 限制半径在5-100像素之间

            # 绘制虚拟圆（增强可见性）
            osd_img.draw_circle(center_x, center_y, radius, color=circle_color, thickness=3, fill=False)
            osd_img.draw_circle(center_x, center_y, radius + 2, color=(255, 255, 255), thickness=1, fill=False)  # 白色外圈

            # 调试信息：显示虚拟圆信息
            if frame_count % 100 == 0:
                print(f"🔧 虚拟圆调试:")
                print(f"   中心: ({center_x}, {center_y})")
                print(f"   半径: {radius}px (原始: {EXTENDED_CONFIG['circle_radius']}px)")

        # 绘制移动点（使用橙色）
        if moving_point:
            moving_x = moving_point[0] * DISPLAY_WIDTH // OUT_RGB888P_WIDTH
            moving_y = moving_point[1] * DISPLAY_HEIGHT // OUT_RGB888P_HEIGH
            point_color = (255, 165, 0)  # 橙色
            point_size = EXTENDED_CONFIG['moving_point_size']

            # 确保移动点在屏幕范围内
            moving_x = max(0, min(DISPLAY_WIDTH - 1, moving_x))
            moving_y = max(0, min(DISPLAY_HEIGHT - 1, moving_y))

            # 绘制移动点（增强可见性，移除文字标识）
            osd_img.draw_circle(moving_x, moving_y, point_size + 3, color=(255, 255, 255), thickness=2, fill=False)  # 白色外圈
            osd_img.draw_circle(moving_x, moving_y, point_size, color=point_color, thickness=4, fill=True)  # 橙色填充

            # 移除"MOVE"文字标识，改为在图例中说明

            # 调试信息：显示移动点信息
            if frame_count % 100 == 0:
                print(f"🔧 移动点调试:")
                print(f"   原始坐标: ({moving_point[0]}, {moving_point[1]})")
                print(f"   显示坐标: ({moving_x}, {moving_y})")
        else:
            # 如果没有移动点，显示调试信息
            if frame_count % 100 == 0 and MODE_CONFIG['current_mode'] == MODE_EXTENDED:
                print(f"⚠️  扩展模式但无移动点数据")

    except Exception as e:
        print(f"绘制扩展模式元素错误: {e}")
        print(f"   中心点: ({center_x}, {center_y})")
        print(f"   移动点: {moving_point}")
        print(f"   当前模式: {MODE_CONFIG['current_mode']}")

def draw_connection_line(osd_img, target_x, target_y):
    """绘制连接线 - 继承Step11功能（使用输出坐标）"""
    try:
        if not VISUALIZATION_CONFIG['draw_connection_line']:
            return

        screen_center_x = SERIAL_CONFIG['screen_center_x'] * DISPLAY_WIDTH // OUT_RGB888P_WIDTH
        screen_center_y = SERIAL_CONFIG['screen_center_y'] * DISPLAY_HEIGHT // OUT_RGB888P_HEIGH
        color = VISUALIZATION_CONFIG['line_color']

        # 绘制连接线
        osd_img.draw_line(target_x, target_y, screen_center_x, screen_center_y,
                         color=color, thickness=3)

        # 计算并显示距离
        distance = math.sqrt((target_x - screen_center_x)**2 + (target_y - screen_center_y)**2)

        mid_x = (target_x + screen_center_x) // 2
        mid_y = (target_y + screen_center_y) // 2
        osd_img.draw_string_advanced(mid_x - 30, mid_y - 15, 20, f"{distance:.1f}", color=color)

    except Exception as e:
        print(f"绘制连接线错误: {e}")

def draw_legend_panel(osd_img):
    """绘制右下角图例说明 - Step12新增功能"""
    try:
        # 图例区域配置
        legend_x = DISPLAY_WIDTH - 200  # 右下角X坐标
        legend_y = DISPLAY_HEIGHT - 150  # 右下角Y坐标
        legend_width = 180
        legend_height = 130

        # 绘制图例背景
        osd_img.draw_rectangle(legend_x, legend_y, legend_width, legend_height,
                              color=(0, 0, 0), thickness=2, fill=True)  # 黑色背景
        osd_img.draw_rectangle(legend_x, legend_y, legend_width, legend_height,
                              color=(255, 255, 255), thickness=2, fill=False)  # 白色边框

        # 图例标题
        title_x = legend_x + 10
        title_y = legend_y + 10
        osd_img.draw_string_advanced(title_x, title_y, 18, "图例说明", color=(255, 255, 255))

        # 图例项目
        item_y = title_y + 25
        line_height = 20

        # 屏幕中心点（黄色）
        osd_img.draw_circle(title_x + 10, item_y + 8, 6, color=(255, 255, 0), thickness=3, fill=True)
        osd_img.draw_string_advanced(title_x + 25, item_y, 16, "屏幕中心", color=(255, 255, 255))
        item_y += line_height

        # 矩形中心点（红色）
        osd_img.draw_circle(title_x + 10, item_y + 8, 6, color=(255, 0, 0), thickness=3, fill=True)
        osd_img.draw_string_advanced(title_x + 25, item_y, 16, "矩形中心", color=(255, 255, 255))
        item_y += line_height

        # 扩展模式专有图例
        if MODE_CONFIG['current_mode'] == MODE_EXTENDED:
            # 虚拟圆（蓝色）
            osd_img.draw_circle(title_x + 10, item_y + 8, 6, color=(0, 100, 255), thickness=2, fill=False)
            osd_img.draw_string_advanced(title_x + 25, item_y, 16, "虚拟圆", color=(255, 255, 255))
            item_y += line_height

            # 移动点（橙色）
            osd_img.draw_circle(title_x + 10, item_y + 8, 6, color=(255, 165, 0), thickness=3, fill=True)
            osd_img.draw_string_advanced(title_x + 25, item_y, 16, "移动点", color=(255, 255, 255))
            item_y += line_height

        # 连接线（青色）
        osd_img.draw_line(title_x + 5, item_y + 8, title_x + 15, item_y + 8,
                         color=(0, 255, 255), thickness=2)
        osd_img.draw_string_advanced(title_x + 25, item_y, 16, "连接线", color=(255, 255, 255))

    except Exception as e:
        print(f"绘制图例面板错误: {e}")

def draw_enhanced_info_panel(osd_img, center_x, center_y, output_x, output_y, fps):
    """绘制增强信息面板 - 修改：优化布局，为右下角图例预留空间"""
    try:
        if not VISUALIZATION_CONFIG['draw_info_panel']:
            return

        # 基础信息（调整位置避开右下角图例）
        info_x = 20
        info_y = 80  # 避开按钮区域
        line_height = 30  # 减小行高节省空间
        max_info_y = DISPLAY_HEIGHT - 180  # 为右下角图例预留空间

        # 系统信息
        osd_img.draw_string_advanced(info_x, info_y, 26, "Step12 双模式系统", color=(255, 255, 0))
        info_y += line_height

        # 当前模式（增强显示）
        mode_name = "基础模式" if MODE_CONFIG['current_mode'] == MODE_BASIC else "扩展模式"
        mode_color = VISUALIZATION_CONFIG['button_active_color']
        osd_img.draw_string_advanced(info_x, info_y, 22, f"当前模式: {mode_name}", color=mode_color)
        info_y += line_height

        # 矩形中心坐标
        if center_x is not None and center_y is not None:
            # 转换回原始坐标系
            orig_center_x = center_x * OUT_RGB888P_WIDTH // DISPLAY_WIDTH
            orig_center_y = center_y * OUT_RGB888P_HEIGH // DISPLAY_HEIGHT

            osd_img.draw_string_advanced(info_x, info_y, 24,
                                       f"矩形中心: ({orig_center_x}, {orig_center_y})",
                                       color=(255, 0, 0))
            info_y += line_height

        # 输出坐标（发送给STM32的坐标）
        if output_x is not None and output_y is not None:
            # 转换回原始坐标系
            orig_output_x = output_x * OUT_RGB888P_WIDTH // DISPLAY_WIDTH
            orig_output_y = output_y * OUT_RGB888P_HEIGH // DISPLAY_HEIGHT

            output_color = (0, 255, 0) if MODE_CONFIG['current_mode'] == MODE_EXTENDED else (255, 0, 0)
            osd_img.draw_string_advanced(info_x, info_y, 24,
                                       f"输出坐标: ({orig_output_x}, {orig_output_y})",
                                       color=output_color)

            # 偏差计算（基于输出坐标）
            offset_x = orig_output_x - SERIAL_CONFIG['screen_center_x']
            offset_y = orig_output_y - SERIAL_CONFIG['screen_center_y']
            info_y += line_height
            osd_img.draw_string_advanced(info_x, info_y, 24,
                                       f"偏差: ΔX={offset_x:+d}, ΔY={offset_y:+d}",
                                       color=(255, 255, 0))
        else:
            osd_img.draw_string_advanced(info_x, info_y, 24, "输出坐标: 未检测到", color=(255, 0, 0))

        info_y += line_height

        # 扩展模式信息
        if MODE_CONFIG['current_mode'] == MODE_EXTENDED:
            osd_img.draw_string_advanced(info_x, info_y, 20,
                                       f"圆半径: {EXTENDED_CONFIG['circle_radius']}px",
                                       color=(0, 255, 255))
            info_y += line_height
            osd_img.draw_string_advanced(info_x, info_y, 20,
                                       f"移动角度: {movement_angle:.2f}rad",
                                       color=(0, 255, 255))
            info_y += line_height

        # 性能信息
        osd_img.draw_string_advanced(info_x, info_y, 24, f"FPS: {fps:.1f}", color=(255, 255, 255))
        info_y += line_height

        osd_img.draw_string_advanced(info_x, info_y, 24, f"总帧数: {frame_count}", color=(255, 255, 255))
        info_y += line_height

        # 检测成功率
        detection_rate = (detection_count / frame_count * 100) if frame_count > 0 else 0
        rate_color = (0, 255, 0) if detection_rate >= 60 else (255, 0, 0)
        osd_img.draw_string_advanced(info_x, info_y, 24, f"检测率: {detection_rate:.1f}%", color=rate_color)

    except Exception as e:
        print(f"绘制增强信息面板错误: {e}")

def calculate_fps():
    """计算FPS - 修复函数属性问题"""
    current_time = time.time()

    # 使用全局变量而不是函数属性
    global fps_start_time, fps_frame_count, last_fps

    # 初始化全局变量
    if 'fps_start_time' not in globals():
        fps_start_time = current_time
        fps_frame_count = 0
        last_fps = 0.0

    fps_frame_count += 1
    elapsed_time = current_time - fps_start_time

    if elapsed_time >= 1.0:  # 每秒更新一次FPS
        last_fps = fps_frame_count / elapsed_time
        fps_start_time = current_time
        fps_frame_count = 0

    return last_fps

# ==================== Step12新增：安全属性访问函数 ====================
def safe_get_attribute(obj, attr_name, default_value="未知"):
    """安全获取对象属性，避免AttributeError"""
    try:
        return getattr(obj, attr_name, default_value)
    except Exception as e:
        print(f"   ⚠️  获取属性 {attr_name} 失败: {e}")
        return default_value

def print_system_info(sensor, kmodel_name, labels, confidence_threshold):
    """安全打印系统信息"""
    try:
        print("🔍 系统状态检查:")

        # 安全获取传感器信息
        sensor_id = safe_get_attribute(sensor, 'id', '2')
        print(f"   传感器ID: {sensor_id}")

        # 显示配置信息
        print(f"   显示模式: {display_mode}")
        print(f"   显示尺寸: {DISPLAY_WIDTH}x{DISPLAY_HEIGHT}")
        print(f"   推理尺寸: {OUT_RGB888P_WIDTH}x{OUT_RGB888P_HEIGH}")

        # YOLO配置信息
        print(f"   YOLO模型: {kmodel_name}")
        print(f"   检测类别: {labels}")
        print(f"   置信度阈值: {confidence_threshold}")

        # 双模式配置信息
        current_mode_name = "基础模式" if MODE_CONFIG['current_mode'] == MODE_BASIC else "扩展模式"
        print(f"   当前模式: {current_mode_name}")
        print(f"   虚拟圆半径: {EXTENDED_CONFIG['circle_radius']}px")

    except Exception as e:
        print(f"🔍 系统状态检查失败: {e}")
        print("   继续运行程序...")

# ==================== Step12新增：系统资源检查函数 ====================
def check_system_resources():
    """检查系统资源状态 - 预防MediaManager错误"""
    try:
        print("🔍 系统资源检查:")

        # 1. 内存检查
        try:
            import gc
            gc.collect()
            # 在MicroPython中，gc.mem_free()可能不可用，使用其他方法
            print("   ✅ 内存垃圾回收完成")
        except Exception as e:
            print(f"   ⚠️  内存检查失败: {e}")

        # 2. 检查是否有残留的MediaManager实例
        try:
            MediaManager.deinit()
            print("   ✅ 清理了残留的MediaManager实例")
        except:
            print("   ℹ️  无残留MediaManager实例")

        # 3. 等待资源释放
        time.sleep(0.2)

        return True

    except Exception as e:
        print(f"   ❌ 系统资源检查失败: {e}")
        return False

def safe_media_manager_init():
    """安全的MediaManager初始化 - 多重尝试机制（修复：增强稳定性）"""
    max_attempts = 3

    for attempt in range(max_attempts):
        try:
            print(f"🔄 MediaManager初始化尝试 {attempt + 1}/{max_attempts}")

            # 1. 清理和等待
            if attempt > 0:
                print(f"   清理之前的尝试...")
                try:
                    MediaManager.deinit()
                    print(f"   已清理MediaManager")
                except:
                    print(f"   无需清理MediaManager")

                # 递增等待时间
                wait_time = 0.5 + (attempt * 0.5)  # 0.5s, 1.0s, 1.5s
                print(f"   等待 {wait_time}s 后重试...")
                time.sleep(wait_time)
                gc.collect()

            # 2. 尝试初始化
            print(f"   正在初始化MediaManager...")
            MediaManager.init()
            print("✅ MediaManager初始化成功")

            # 3. 验证初始化结果
            print(f"   验证MediaManager状态...")
            time.sleep(0.1)  # 短暂等待确保初始化完成

            return True

        except Exception as e:
            print(f"   ❌ 尝试 {attempt + 1} 失败: {e}")

            # 分析错误类型
            error_msg = str(e).lower()
            if "vb config failed" in error_msg:
                print(f"   📋 错误类型: 视频缓冲区配置失败")
                print(f"   💡 建议: 可能需要重启开发板释放资源")
            elif "resource busy" in error_msg:
                print(f"   📋 错误类型: 资源被占用")
                print(f"   💡 建议: 等待更长时间或检查其他程序")
            else:
                print(f"   📋 错误类型: 未知错误")

            if attempt < max_attempts - 1:
                print(f"   🔄 准备重试...")
            else:
                print(f"   ❌ 所有尝试均失败")
                print(f"   💡 最终建议:")
                print(f"      1. 重启开发板: reboot")
                print(f"      2. 检查系统资源使用情况")
                print(f"      3. 确认固件版本兼容性")

    return False

# ==================== Step12新增：坐标平滑处理功能 ====================
class CoordinateSmoothing:
    """坐标平滑处理类 - 解决坐标数据抖动问题"""

    def __init__(self, buffer_size=3):
        """初始化坐标平滑器
        Args:
            buffer_size: 缓冲区大小，默认3个坐标点
        """
        self.buffer_size = buffer_size
        self.x_buffer = []
        self.y_buffer = []

    def add_coordinate(self, x, y):
        """添加新的坐标点到缓冲区
        Args:
            x, y: 新的坐标点
        Returns:
            tuple: 平滑后的坐标 (smooth_x, smooth_y)
        """
        try:
            # 添加新坐标到缓冲区
            self.x_buffer.append(x)
            self.y_buffer.append(y)

            # 保持缓冲区大小
            if len(self.x_buffer) > self.buffer_size:
                self.x_buffer.pop(0)
            if len(self.y_buffer) > self.buffer_size:
                self.y_buffer.pop(0)

            # 计算平均值
            smooth_x = sum(self.x_buffer) // len(self.x_buffer)
            smooth_y = sum(self.y_buffer) // len(self.y_buffer)

            return smooth_x, smooth_y

        except Exception as e:
            print(f"坐标平滑处理错误: {e}")
            return x, y  # 出错时返回原始坐标

    def get_buffer_status(self):
        """获取缓冲区状态信息"""
        return {
            'buffer_size': len(self.x_buffer),
            'x_buffer': self.x_buffer.copy(),
            'y_buffer': self.y_buffer.copy()
        }

    def reset(self):
        """重置缓冲区"""
        self.x_buffer.clear()
        self.y_buffer.clear()

# 全局坐标平滑器实例
coordinate_smoother = CoordinateSmoothing(buffer_size=3)

def smooth_coordinates(raw_x, raw_y, mode_name="未知"):
    """坐标平滑处理函数
    Args:
        raw_x, raw_y: 原始坐标
        mode_name: 模式名称（用于调试）
    Returns:
        tuple: (平滑后的坐标, 调试信息)
    """
    try:
        # 获取平滑后的坐标
        smooth_x, smooth_y = coordinate_smoother.add_coordinate(raw_x, raw_y)

        # 计算平滑效果
        offset_x = smooth_x - raw_x
        offset_y = smooth_y - raw_y
        offset_distance = (offset_x**2 + offset_y**2)**0.5

        # 调试信息
        debug_info = {
            'mode': mode_name,
            'raw': (raw_x, raw_y),
            'smooth': (smooth_x, smooth_y),
            'offset': (offset_x, offset_y),
            'offset_distance': offset_distance,
            'buffer_status': coordinate_smoother.get_buffer_status()
        }

        return (smooth_x, smooth_y), debug_info

    except Exception as e:
        print(f"坐标平滑处理错误: {e}")
        return (raw_x, raw_y), {'error': str(e)}

# ==================== Step12新增：触摸屏按键切换功能 ====================
def check_button_touch(touch_x, touch_y):
    """检查触摸点是否在按钮区域内 - 重新设计：精确按钮区域判断"""
    try:
        # 获取纵向布局按钮配置
        button_x = MODE_CONFIG['button_x']
        basic_y = MODE_CONFIG['basic_button_y']
        extended_y = MODE_CONFIG['extended_button_y']
        button_w = MODE_CONFIG['button_width']
        button_h = MODE_CONFIG['button_height']

        # 计算按钮区域边界（纵向布局）
        button_x_min, button_x_max = button_x, button_x + button_w
        basic_y_min, basic_y_max = basic_y, basic_y + button_h
        extended_y_min, extended_y_max = extended_y, extended_y + button_h

        # 详细调试信息
        print(f"� 按钮区域检测:")
        print(f"   用户触摸坐标: ({touch_x}, {touch_y})")
        print(f"   基础模式按钮: X[{button_x_min}-{button_x_max}] Y[{basic_y_min}-{basic_y_max}]")
        print(f"   扩展模式按钮: X[{button_x_min}-{button_x_max}] Y[{extended_y_min}-{extended_y_max}]")

        # 检查基础模式按钮（上方按钮，纵向布局）
        if (button_x_min <= touch_x <= button_x_max and
            basic_y_min <= touch_y <= basic_y_max):
            print(f"✅ 用户点击基础模式按钮（上方）")
            print(f"   触摸点({touch_x}, {touch_y})在基础模式按钮区域内")
            print(f"   将切换到基础模式")
            return 'basic'

        # 检查扩展模式按钮（下方按钮，纵向布局）
        elif (button_x_min <= touch_x <= button_x_max and
              extended_y_min <= touch_y <= extended_y_max):
            print(f"✅ 用户点击扩展模式按钮（下方）")
            print(f"   触摸点({touch_x}, {touch_y})在扩展模式按钮区域内")
            print(f"   将切换到扩展模式")
            return 'extended'

        # 触摸点不在任何按钮区域内
        else:
            print(f"❌ 触摸点不在任何按钮区域内")
            print(f"   触摸坐标({touch_x}, {touch_y})超出所有按钮范围")
            print(f"   不会触发模式切换")
            return None

    except Exception as e:
        print(f"❌ 按钮区域检测错误: {e}")
        print(f"   触摸坐标: ({touch_x}, {touch_y})")
        try:
            print(f"   纵向按钮配置: X={button_x}, 基础Y={basic_y}, 扩展Y={extended_y}")
            print(f"   按钮尺寸: {button_w}x{button_h}")
        except:
            print(f"   按钮配置读取失败")
        return None

# ==================== Step12重新设计：基于step4的触摸检测机制 ====================

# 全局触摸屏对象（基于step4的实现方式）
touch_screen = None

def init_touch_screen():
    """初始化触摸屏 - 基于step4的成功实现"""
    global touch_screen
    try:
        from machine import TOUCH
        touch_screen = TOUCH(0)
        print("✅ 触摸屏初始化成功（基于step4机制）")
        return True
    except ImportError:
        print("⚠️  TOUCH模块不可用，将使用备用方案")
        return False
    except Exception as e:
        print(f"❌ 触摸屏初始化失败: {e}")
        return False

def handle_touch_input():
    """处理触摸输入 - 重新设计：纯手动控制，移除所有自动切换"""
    try:
        # 重新设计：完全移除自动切换逻辑，实现纯手动控制
        # 禁止任何基于帧数、时间或条件的自动模式切换

        # 方案1: 使用step4的TOUCH机制（主要方案）
        global touch_screen
        if touch_screen is not None:
            try:
                # 基于step4的触摸检测方式
                points = touch_screen.read()
                if len(points) > 0:
                    touch_x, touch_y = points[0].x, points[0].y
                    print(f"🔍 step4机制触摸检测 - 坐标: ({touch_x}, {touch_y})")
                    return touch_x, touch_y
            except Exception as e:
                print(f"⚠️  TOUCH读取错误: {e}")
        else:
            # 调试：每200帧显示一次触摸检测状态
            global frame_count
            if frame_count % 200 == 0:
                print(f"🔍 触摸检测状态检查:")
                print(f"   step4 TOUCH机制: {'可用' if touch_screen is not None else '不可用'}")
                print(f"   当前帧数: {frame_count}")
                print(f"   等待真实用户触摸输入...")

        # 方案2: GPIO按键检测（备用方案）
        try:
            # 注释说明：取消以下注释以启用GPIO按键检测
            # from machine import Pin
            # basic_button = Pin(10, Pin.IN, Pin.PULL_UP)    # GPIO10 - 基础模式按键
            # extended_button = Pin(11, Pin.IN, Pin.PULL_UP) # GPIO11 - 扩展模式按键
            #
            # # 检测基础模式按键
            # if not basic_button.value():  # 按键按下为低电平
            #     print("� GPIO检测：用户按下基础模式按键")
            #     # 返回基础模式按钮中心坐标
            #     return MODE_CONFIG['basic_button_x'] + MODE_CONFIG['button_width']//2, \
            #            MODE_CONFIG['button_y'] + MODE_CONFIG['button_height']//2
            #
            # # 检测扩展模式按键
            # elif not extended_button.value():  # 按键按下为低电平
            #     print("� GPIO检测：用户按下扩展模式按键")
            #     # 返回扩展模式按钮中心坐标
            #     return MODE_CONFIG['extended_button_x'] + MODE_CONFIG['button_width']//2, \
            #            MODE_CONFIG['button_y'] + MODE_CONFIG['button_height']//2
            pass
        except ImportError:
            pass  # GPIO不可用时忽略

        # 方案2: 串口触摸数据接收（用于外部触摸控制器）
        try:
            # 注释说明：取消以下注释以启用串口触摸检测
            # if uart and uart.any():
            #     touch_data = uart.read()
            #     # 解析触摸数据格式，例如: "TOUCH:X,Y\n"
            #     if touch_data and touch_data.startswith(b"TOUCH:"):
            #         coords = touch_data.decode().strip().split(':')[1].split(',')
            #         touch_x, touch_y = int(coords[0]), int(coords[1])
            #         print(f"� 串口检测：用户触摸坐标({touch_x}, {touch_y})")
            #         return touch_x, touch_y
            pass
        except:
            pass  # 串口数据解析失败时忽略

        # 方案3: I2C触摸屏控制器（用于直连触摸屏）
        try:
            # 注释说明：取消以下注释以启用I2C触摸屏检测
            # from machine import I2C, Pin
            # i2c = I2C(0, scl=Pin(9), sda=Pin(8), freq=400000)
            # touch_addr = 0x38  # 触摸屏控制器I2C地址
            # if touch_addr in i2c.scan():
            #     touch_data = i2c.readfrom(touch_addr, 6)
            #     if touch_data[0] & 0x80:  # 检查触摸标志位
            #         touch_x = ((touch_data[1] & 0x0F) << 8) | touch_data[2]
            #         touch_y = ((touch_data[3] & 0x0F) << 8) | touch_data[4]
            #         print(f"� I2C检测：用户触摸坐标({touch_x}, {touch_y})")
            #         return touch_x, touch_y
            pass
        except ImportError:
            pass  # I2C不可用时忽略

        # 方案4: 测试模式（仅用于功能验证，实际部署时应移除）
        # 注释说明：以下代码仅用于测试，实际部署时请完全移除
        # 可以通过修改以下代码来手动触发按钮测试

        # 手动测试开关（设置为True时启用测试模式）
        ENABLE_TEST_MODE = False  # 禁用测试模式，启用真实触摸检测

        if ENABLE_TEST_MODE:
            # 基于step4机制的按钮测试 - 修复：添加双向模式切换测试
            global frame_count

            # 纵向布局测试坐标
            basic_test_x = MODE_CONFIG['button_x'] + MODE_CONFIG['button_width'] // 2
            basic_test_y = MODE_CONFIG['basic_button_y'] + MODE_CONFIG['button_height'] // 2
            extended_test_x = MODE_CONFIG['button_x'] + MODE_CONFIG['button_width'] // 2
            extended_test_y = MODE_CONFIG['extended_button_y'] + MODE_CONFIG['button_height'] // 2

            # 循环测试：每300帧切换一次模式
            if frame_count % 600 < 300:
                # 前300帧测试扩展模式按钮
                print(f"🔧 测试模式：模拟点击扩展模式按钮（纵向布局）")
                print(f"   测试坐标: ({extended_test_x}, {extended_test_y})")
                return extended_test_x, extended_test_y
            else:
                # 后300帧测试基础模式按钮
                print(f"🔧 测试模式：模拟点击基础模式按钮（纵向布局）")
                print(f"   测试坐标: ({basic_test_x}, {basic_test_y})")
                return basic_test_x, basic_test_y

        # 没有检测到任何触摸事件
        return None, None

    except Exception as e:
        print(f"❌ 触摸输入处理错误: {e}")
        return None, None

def process_button_click(button_type):
    """处理按钮点击事件 - 重新设计：确保立即响应和状态同步"""
    try:
        current_mode = MODE_CONFIG['current_mode']
        current_mode_name = "基础模式" if current_mode == MODE_BASIC else "扩展模式"
        target_mode_name = "基础模式" if button_type == 'basic' else "扩展模式"

        print(f"� 用户按钮点击处理:")
        print(f"   点击按钮: {target_mode_name}按钮")
        print(f"   当前模式: {current_mode_name}")
        print(f"   请求模式: {target_mode_name}")

        # 处理基础模式按钮点击
        if button_type == 'basic':
            if current_mode != MODE_BASIC:
                print(f"🔄 执行模式切换: {current_mode_name} → 基础模式")
                success = switch_mode(MODE_BASIC)
                if success:
                    print(f"✅ 基础模式切换成功:")
                    print(f"   - 按钮状态: 基础模式(绿色激活), 扩展模式(灰色)")
                    print(f"   - 串口数据: 立即切换为矩形中心点坐标")
                    print(f"   - 可视化: 显示红色矩形中心点")
                    print(f"   - 状态持久: 基础模式将持续直到下次手动切换")
                    return True
                else:
                    print(f"❌ 基础模式切换失败")
                    return False
            else:
                print(f"ℹ️  当前已是基础模式，无需切换")
                print(f"   模式状态: 基础模式保持激活")
                return True

        # 处理扩展模式按钮点击
        elif button_type == 'extended':
            if current_mode != MODE_EXTENDED:
                print(f"🔄 执行模式切换: {current_mode_name} → 扩展模式")
                success = switch_mode(MODE_EXTENDED)
                if success:
                    print(f"✅ 扩展模式切换成功:")
                    print(f"   - 按钮状态: 扩展模式(绿色激活), 基础模式(灰色)")
                    print(f"   - 串口数据: 立即切换为动态移动点坐标")
                    print(f"   - 可视化: 显示蓝色虚拟圆 + 橙色移动点")
                    print(f"   - 状态持久: 扩展模式将持续直到下次手动切换")
                    return True
                else:
                    print(f"❌ 扩展模式切换失败")
                    return False
            else:
                print(f"ℹ️  当前已是扩展模式，无需切换")
                print(f"   模式状态: 扩展模式保持激活")
                return True

        # 处理无效按钮类型
        else:
            print(f"❌ 无效的按钮类型: {button_type}")
            print(f"   有效按钮类型: 'basic'(基础模式), 'extended'(扩展模式)")
            return False

    except Exception as e:
        print(f"❌ 按钮点击处理异常: {e}")
        print(f"   按钮类型: {button_type}")
        print(f"   当前模式: {MODE_CONFIG.get('current_mode', '未知')}")
        import traceback
        traceback.print_exc()
        return False

# ==================== Step12新增：K230平台特定解决方案 ====================
def k230_sensor_startup_fix():
    """K230平台特定的传感器启动修复方案"""
    print("🔧 K230平台特定修复方案:")
    print("💡 针对buffer_size 0问题的专门解决方案")

    # 方案A: 重置传感器状态
    print("🔄 方案A: 重置传感器状态...")
    try:
        # 完全重置传感器
        sensor = Sensor(id=2)
        sensor.reset()
        time.sleep(0.5)  # 等待重置完成

        # 重新配置传感器
        sensor.set_hmirror(False)
        sensor.set_vflip(False)

        # 简化配置 - 只配置必要的参数
        sensor.set_framesize(width=OUT_RGB888P_WIDTH, height=OUT_RGB888P_HEIGH, chn=CAM_CHN_ID_2)
        sensor.set_pixformat(PIXEL_FORMAT_RGB_888_PLANAR, chn=CAM_CHN_ID_2)

        print("   ✅ 传感器重置和重新配置完成")
        return sensor

    except Exception as e:
        print(f"   ❌ 方案A失败: {e}")
        return None

def alternative_media_init():
    """替代的媒体初始化方案 - 不使用MediaManager"""
    print("🔧 替代媒体初始化方案:")
    print("💡 完全跳过MediaManager，使用最小化配置")

    try:
        # 确保MediaManager完全清理
        try:
            MediaManager.deinit()
            print("   清理MediaManager")
        except:
            pass

        # 等待资源完全释放
        time.sleep(1.5)
        gc.collect()

        # 重新初始化传感器
        sensor = k230_sensor_startup_fix()
        if sensor is None:
            return None, False

        # 简化显示配置
        try:
            if display_mode == "lcd":
                Display.init(Display.ST7701, to_ide=True)
            else:
                Display.init(Display.LT9611, to_ide=True)
            print("   ✅ 简化显示配置完成")
        except Exception as e:
            print(f"   ⚠️  显示配置失败: {e}，但继续运行")

        # 直接启动传感器
        print("🔄 直接启动传感器...")
        sensor.run()
        print("   ✅ 传感器直接启动成功")

        return sensor, False  # 返回传感器对象和use_media_manager=False

    except Exception as e:
        print(f"❌ 替代方案失败: {e}")
        return None, False

# ==================== Step12主检测函数（基于Step11扩展） ====================
def detection():
    """主检测函数 - Step12双模式切换版本（修复：增强错误处理）"""
    global frame_count, detection_count, last_send_time, fps_start_time, fps_frame_count, last_fps
    global movement_angle, last_movement_time, current_moving_point

    print("🚀 Step12 双模式切换系统启动")
    print("📋 系统特性:")
    print("   ✅ 基于Step11的YOLO检测")
    print("   ✅ 双模式切换（基础+扩展）")
    print("   ✅ 动态移动点生成")
    print("   ✅ 可视化模式按钮")
    print("   ✅ STM32串口通信（模式自适应）")

    # 显示模式配置
    print("🔧 模式配置:")
    print(f"   默认模式: {'基础模式' if MODE_CONFIG['current_mode'] == MODE_BASIC else '扩展模式'}")
    print(f"   虚拟圆半径: {EXTENDED_CONFIG['circle_radius']}px")
    print(f"   移动速度: {EXTENDED_CONFIG['movement_speed']}rad/s")
    print(f"   移动模式: {EXTENDED_CONFIG['movement_mode']}")

    # 初始化串口
    uart_status = "❌断开"
    if init_uart():
        uart_status = "✅连接"
    else:
        print("⚠️  串口初始化失败，程序继续运行但无通信功能")

    # 读取配置文件（继承det_video.py）
    deploy_conf = read_deploy_config(config_path)
    if not deploy_conf:
        print("Failed to load deploy_config.json, exiting.")
        return

    kmodel_name = deploy_conf["kmodel_path"]
    labels = deploy_conf["categories"]
    confidence_threshold = deploy_conf["confidence_threshold"]
    nms_threshold = deploy_conf["nms_threshold"]
    img_size = deploy_conf["img_size"]
    num_classes = deploy_conf["num_classes"]
    nms_option = deploy_conf["nms_option"]
    model_type = deploy_conf["model_type"]
    if model_type == "AnchorBaseDet":
        anchors = deploy_conf["anchors"][0] + deploy_conf["anchors"][1] + deploy_conf["anchors"][2]

    kmodel_frame_size = img_size
    frame_size = [OUT_RGB888P_WIDTH, OUT_RGB888P_HEIGH]
    strides = [8, 16, 32]

    # 计算padding（继承det_video.py）
    ori_w, ori_h = OUT_RGB888P_WIDTH, OUT_RGB888P_HEIGH
    width, height = kmodel_frame_size[0], kmodel_frame_size[1]
    ratio = min(float(width) / ori_w, float(height) / ori_h)
    new_w, new_h = int(ratio * ori_w), int(ratio * ori_h)
    dw, dh = (width - new_w) / 2, (height - new_h) / 2
    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))

    # 初始化KPU和AI2D（继承det_video.py）
    kpu = nn.kpu()
    ai2d = nn.ai2d()
    kpu.load_kmodel(root_path + kmodel_name)
    ai2d.set_dtype(nn.ai2d_format.NCHW_FMT, nn.ai2d_format.NCHW_FMT, np.uint8, np.uint8)
    ai2d.set_pad_param(True, [0, 0, 0, 0, top, bottom, left, right], 0, [114, 114, 114])
    ai2d.set_resize_param(True, nn.interp_method.tf_bilinear, nn.interp_mode.half_pixel)
    ai2d_builder = ai2d.build([1, 3, OUT_RGB888P_HEIGH, OUT_RGB888P_WIDTH], [1, 3, height, width])

    # 初始化摄像头（修复：完全按照det_video.py的配置方式）
    sensor = Sensor(id=2)
    sensor.reset()
    sensor.set_hmirror(False)
    sensor.set_vflip(False)

    # 根本性修复：重新设计初始化流程以解决buffer_size 0问题
    print("🔧 Step12根本性修复：重新设计初始化流程")
    print("💡 问题分析: buffer_size 0 表明MediaManager缓冲区分配失败")
    print("💡 解决方案: 采用K230平台推荐的初始化顺序")

    # 方案1：完全按照Step11的成功模式重新初始化
    print("🔄 方案1: 采用Step11成功模式的初始化顺序")
    try:
        # Step1: 传感器基础配置（在MediaManager之前）
        print("🔧 第1步: 传感器基础配置...")
        sensor.set_framesize(width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT)
        sensor.set_pixformat(PIXEL_FORMAT_YUV_SEMIPLANAR_420)
        sensor.set_framesize(width=OUT_RGB888P_WIDTH, height=OUT_RGB888P_HEIGH, chn=CAM_CHN_ID_2)
        sensor.set_pixformat(PIXEL_FORMAT_RGB_888_PLANAR, chn=CAM_CHN_ID_2)
        print("   ✅ 传感器配置完成")

        # Step2: 显示系统配置（在MediaManager之前）
        print("🔧 第2步: 显示系统配置...")
        sensor_bind_info = sensor.bind_info(x=0, y=0, chn=CAM_CHN_ID_0)
        Display.bind_layer(**sensor_bind_info, layer=Display.LAYER_VIDEO1)
        if display_mode == "lcd":
            Display.init(Display.ST7701, to_ide=True)
        else:
            Display.init(Display.LT9611, to_ide=True)
        print("   ✅ 显示系统配置完成")

        # Step3: 关键等待 - 让所有配置稳定
        print("🔄 第3步: 等待所有配置稳定...")
        time.sleep(1.0)  # 增加等待时间确保配置完全生效

        # Step4: MediaManager初始化（在所有配置完成后）
        print("🔧 第4步: MediaManager初始化...")
        MediaManager.init()
        print("   ✅ MediaManager初始化成功")

        # Step5: 关键等待 - 让MediaManager完全稳定
        print("🔄 第5步: 等待MediaManager完全稳定...")
        time.sleep(0.8)  # 更长的等待时间

        # Step6: 传感器启动（最后一步）
        print("🔧 第6步: 传感器启动...")
        print("💡 如果在此处卡死，说明需要尝试方案2")
        sensor.run()
        print("   ✅ 传感器启动成功")

        # Step7: 初始化触摸屏（基于step4机制）
        print("🔧 第7步: 触摸屏初始化...")
        touch_init_success = init_touch_screen()
        if touch_init_success:
            print("   ✅ 触摸屏初始化成功")
        else:
            print("   ⚠️  触摸屏初始化失败，将使用备用方案")

        use_media_manager = True
        print("🎉 方案1成功: 使用标准MediaManager模式")

    except Exception as e:
        print(f"❌ 方案1失败: {e}")
        print("🔄 尝试方案2: 无MediaManager模式")

        # 方案2：完全跳过MediaManager，直接启动传感器
        try:
            # 清理可能的MediaManager残留
            try:
                MediaManager.deinit()
                print("   清理MediaManager残留")
            except:
                pass

            # 等待资源完全释放
            print("🔄 等待资源释放...")
            time.sleep(2.0)
            gc.collect()

            # 直接启动传感器
            print("🔧 直接启动传感器（无MediaManager模式）...")
            sensor.run()
            print("   ✅ 传感器直接启动成功")

            use_media_manager = False
            print("🎉 方案2成功: 使用无MediaManager模式")

        except Exception as e2:
            print(f"❌ 方案2也失败: {e2}")
            print("🔄 尝试方案3: K230平台特定解决方案")

            # 方案3：使用K230平台特定的解决方案
            try:
                sensor, use_media_manager = alternative_media_init()
                if sensor is None:
                    print("❌ 方案3也失败")
                    print("💡 最终建议:")
                    print("   1. 重启开发板: reboot")
                    print("   2. 检查是否有其他程序占用摄像头")
                    print("   3. 尝试更新固件版本")
                    print("   4. 检查硬件连接")
                    print("   5. 联系技术支持")
                    return
                else:
                    print("🎉 方案3成功: 使用K230特定解决方案")

            except Exception as e3:
                print(f"❌ 方案3失败: {e3}")
                print("💡 所有解决方案均失败，建议:")
                print("   1. 立即重启开发板: reboot")
                print("   2. 检查固件版本是否为最新")
                print("   3. 验证硬件连接状态")
                print("   4. 尝试运行简单的传感器测试程序")
                return

    osd_img = image.Image(DISPLAY_WIDTH, DISPLAY_HEIGHT, image.ARGB8888)

    print("🎯 Step12系统初始化完成")
    print(f"📱 串口状态: {uart_status}")

    # 修复：使用安全的系统状态检查函数
    print_system_info(sensor, kmodel_name, labels, confidence_threshold)

    print("📊 开始双模式检测...")
    print("💡 提示: 系统默认启动基础模式，可通过按钮切换模式")

    # 注意：MediaManager初始化已在上面的新流程中完成
    # 这里直接进入检测循环

    try:

        rgb888p_img = None
        ai2d_input_tensor = None
        data = np.ones((1, 3, width, height), dtype=np.uint8)
        ai2d_output_tensor = nn.from_numpy(data)

        while True:
            frame_count += 1
            current_fps = calculate_fps()

            with ScopedTiming("total", debug_mode > 0):
                rgb888p_img = sensor.snapshot(chn=CAM_CHN_ID_2)
                # 修复：检查图像格式，确保兼容性
                if rgb888p_img and rgb888p_img.format() == image.RGBP888:
                    ai2d_input = rgb888p_img.to_numpy_ref()
                    ai2d_input_tensor = nn.from_numpy(ai2d_input)
                    ai2d_builder.run(ai2d_input_tensor, ai2d_output_tensor)

                    kpu.set_input_tensor(0, ai2d_output_tensor)
                    kpu.run()

                    results = []
                    for i in range(kpu.outputs_size()):
                        out_data = kpu.get_output_tensor(i)
                        result = out_data.to_numpy()
                        result = result.reshape((result.shape[0] * result.shape[1] * result.shape[2] * result.shape[3]))
                        del out_data
                        results.append(result)
                    gc.collect()

                    # YOLO后处理（继承det_video.py）
                    if model_type == "AnchorBaseDet":
                        det_boxes = aicube.anchorbasedet_post_process(results[0], results[1], results[2], kmodel_frame_size, frame_size, strides, num_classes, confidence_threshold, nms_threshold, anchors, nms_option)
                    elif model_type == "GFLDet":
                        det_boxes = aicube.gfldet_post_process(results[0], results[1], results[2], kmodel_frame_size, frame_size, strides, num_classes, confidence_threshold, nms_threshold, nms_option)
                    else:
                        det_boxes = aicube.anchorfreedet_post_process(results[0], results[1], results[2], kmodel_frame_size, frame_size, strides, num_classes, confidence_threshold, nms_threshold, nms_option)

                    osd_img.clear()

                    # Step12新增：双模式检测结果处理
                    target_found = False
                    center_display_x = None
                    center_display_y = None
                    output_display_x = None
                    output_display_y = None
                    output_orig_x = None
                    output_orig_y = None

                    if det_boxes:
                        for det_boxe in det_boxes:
                            class_id = det_boxe[0]
                            confidence = det_boxe[1]

                            # 检查检测到的类别是否为目标类别
                            if labels[class_id] in ["yuan", "person", "object"]:
                                x1, y1, x2, y2 = det_boxe[2], det_boxe[3], det_boxe[4], det_boxe[5]
                                bbox_width = x2 - x1
                                bbox_height = y2 - y1

                                # 计算矩形中心点
                                center_x = int(x1 + bbox_width // 2)
                                center_y = int(y1 + bbox_height // 2)

                                # 映射到显示分辨率
                                center_display_x = int(center_x * DISPLAY_WIDTH / OUT_RGB888P_WIDTH)
                                center_display_y = int(center_y * DISPLAY_HEIGHT / OUT_RGB888P_HEIGH)

                                # Step12核心：根据模式获取输出坐标（修复：确保移动点正确更新）
                                output_orig_x, output_orig_y = get_output_coordinates(center_x, center_y)

                                # 修复：在扩展模式下，确保current_moving_point被正确更新
                                if MODE_CONFIG['current_mode'] == MODE_EXTENDED:
                                    # 强制更新移动点（确保可视化正确显示）
                                    moving_point = calculate_moving_point(center_x, center_y)
                                    if moving_point:
                                        current_moving_point = moving_point
                                        print(f"🔧 扩展模式移动点更新: {current_moving_point}")
                                    else:
                                        print(f"⚠️  扩展模式移动点计算失败")

                                # 映射输出坐标到显示分辨率
                                output_display_x = int(output_orig_x * DISPLAY_WIDTH / OUT_RGB888P_WIDTH)
                                output_display_y = int(output_orig_y * DISPLAY_HEIGHT / OUT_RGB888P_HEIGH)

                                # 确保坐标在有效范围内
                                output_orig_x = max(0, min(OUT_RGB888P_WIDTH - 1, output_orig_x))
                                output_orig_y = max(0, min(OUT_RGB888P_HEIGH - 1, output_orig_y))

                                target_found = True
                                detection_count += 1

                                # 当debug_mode为True时打印模式信息
                                if debug_mode and frame_count % 50 == 0:
                                    mode_name = "基础模式" if MODE_CONFIG['current_mode'] == MODE_BASIC else "扩展模式"
                                    print(f"Step12 {mode_name}检测:")
                                    print(f"  矩形中心: ({center_x}, {center_y})")
                                    print(f"  输出坐标: ({output_orig_x}, {output_orig_y})")
                                    print(f"  置信度: {confidence:.2f}")
                                    if MODE_CONFIG['current_mode'] == MODE_EXTENDED and current_moving_point:
                                        print(f"  移动点: {current_moving_point}")
                                        print(f"  移动角度: {movement_angle:.2f}rad")

                                break  # 只处理第一个有效检测目标

                    # Step12重新设计：纯手动按键切换检测（移除所有自动切换）
                    try:
                        # 检测用户触摸输入（完全手动控制）
                        touch_x, touch_y = handle_touch_input()

                        # 仅在检测到实际触摸事件时处理
                        if touch_x is not None and touch_y is not None:
                            print(f"🔘 用户触摸事件检测:")
                            print(f"   触摸坐标: ({touch_x}, {touch_y})")

                            # 检查用户是否点击了按钮区域
                            button_type = check_button_touch(touch_x, touch_y)

                            if button_type:
                                print(f"🔘 用户按钮点击确认: {button_type}模式按钮")

                                # 立即处理用户按钮点击
                                if process_button_click(button_type):
                                    print(f"✅ 用户模式切换成功: {button_type}模式")
                                    print(f"   串口数据将立即同步新模式的坐标类型")

                                    # 注意：移动点变量重置已在switch_mode()中处理
                                    # 确保状态同步
                                    if button_type == 'extended':
                                        print(f"   扩展模式状态: 动态移动点算法已激活")
                                    else:
                                        print(f"   基础模式状态: 矩形中心点算法已激活")

                                else:
                                    print(f"❌ 用户模式切换失败: {button_type}模式")
                            else:
                                # 用户触摸了非按钮区域，不做任何操作
                                pass

                        # 注意：完全移除定时调试输出，避免干扰纯手动控制
                        # 系统现在完全依赖用户的主动操作

                    except Exception as e:
                        print(f"❌ 用户按键检测异常: {e}")
                        print(f"   发生帧数: {frame_count}")
                        import traceback
                        traceback.print_exc()

                    # 注意：已移除自动模式切换功能，现在完全由用户手动控制

                    # Step12新增：绘制双模式可视化元素
                    draw_mode_buttons(osd_img)
                    draw_screen_center(osd_img)

                    # 调试：每100帧输出当前模式状态
                    if frame_count % 100 == 0:
                        current_mode_name = "基础模式" if MODE_CONFIG['current_mode'] == MODE_BASIC else "扩展模式"
                        print(f"🔧 当前系统状态:")
                        print(f"   模式: {current_mode_name}")
                        print(f"   目标检测: {'是' if target_found else '否'}")
                        if MODE_CONFIG['current_mode'] == MODE_EXTENDED:
                            print(f"   移动点: {current_moving_point}")
                            print(f"   移动角度: {movement_angle:.2f}rad")

                    if target_found:
                        # 绘制矩形中心点
                        draw_target_center(osd_img, center_display_x, center_display_y)

                        # 绘制扩展模式元素（虚拟圆和移动点）
                        if MODE_CONFIG['current_mode'] == MODE_EXTENDED:
                            print(f"🔧 绘制扩展模式元素: 中心({center_display_x}, {center_display_y}), 移动点{current_moving_point}")
                            draw_extended_mode_elements(osd_img, center_display_x, center_display_y, current_moving_point)

                        # 绘制连接线（使用输出坐标）
                        draw_connection_line(osd_img, output_display_x, output_display_y)

                    # 绘制增强信息面板
                    draw_enhanced_info_panel(osd_img, center_display_x, center_display_y,
                                           output_display_x, output_display_y, current_fps)

                    # Step12新增：绘制右下角图例说明
                    draw_legend_panel(osd_img)

                    # Step12新增：STM32串口通信（集成坐标平滑处理）
                    current_time = time.ticks_ms()
                    if current_time - last_send_time >= SERIAL_CONFIG['send_interval']:
                        if target_found and output_orig_x is not None:
                            # 修复问题2：应用坐标平滑处理
                            mode_name = "基础模式" if MODE_CONFIG['current_mode'] == MODE_BASIC else "扩展模式"

                            # 对输出坐标进行平滑处理
                            (smooth_x, smooth_y), debug_info = smooth_coordinates(
                                output_orig_x, output_orig_y, mode_name
                            )

                            # 发送平滑后的坐标给STM32
                            send_success = send_coordinates_to_stm32(smooth_x, smooth_y)

                            # 调试输出：显示原始坐标和平滑后坐标的对比
                            if send_success and frame_count % 50 == 0:
                                print(f"📡 Step12 {mode_name}数据发送（坐标平滑）:")
                                print(f"   原始坐标: ({debug_info['raw'][0]}, {debug_info['raw'][1]})")
                                print(f"   平滑坐标: ({debug_info['smooth'][0]}, {debug_info['smooth'][1]})")
                                print(f"   平滑偏移: ({debug_info['offset'][0]:+d}, {debug_info['offset'][1]:+d})")
                                print(f"   偏移距离: {debug_info['offset_distance']:.1f}px")
                                print(f"   缓冲区大小: {debug_info['buffer_status']['buffer_size']}/3")
                                print(f"   数据包格式: STM32兼容(11字节)")
                                if MODE_CONFIG['current_mode'] == MODE_EXTENDED:
                                    print(f"   移动角度: {movement_angle:.2f}rad")
                        else:
                            # 未检测到目标时重置坐标平滑器
                            if frame_count % 100 == 0:
                                print(f"🚫 未检测到有效目标 - 未发送数据")
                                print(f"   检测成功率: {(detection_count / frame_count * 100):.1f}%")
                                print(f"   重置坐标平滑器缓冲区")
                                coordinate_smoother.reset()  # 重置缓冲区避免无效数据影响

                        last_send_time = current_time

                    Display.show_image(osd_img, 0, 0, Display.LAYER_OSD3)
                    gc.collect()
                rgb888p_img = None

    except Exception as e:
        print(f"❌ 程序运行错误: {e}")
    finally:
        # 清理资源（修复：增强错误处理和资源清理）
        print("🔄 正在清理资源...")

        # 1. 设置退出点
        try:
            os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
        except:
            pass

        # 2. 清理AI2D张量
        try:
            if 'ai2d_input_tensor' in locals() and ai2d_input_tensor:
                del ai2d_input_tensor
                print("   ✅ AI2D输入张量已清理")
        except Exception as e:
            print(f"   ⚠️  AI2D输入张量清理失败: {e}")

        try:
            if 'ai2d_output_tensor' in locals() and ai2d_output_tensor:
                del ai2d_output_tensor
                print("   ✅ AI2D输出张量已清理")
        except Exception as e:
            print(f"   ⚠️  AI2D输出张量清理失败: {e}")

        # 3. 停止传感器
        try:
            if 'sensor' in locals():
                sensor.stop()
                print("   ✅ 传感器已停止")
        except Exception as e:
            print(f"   ⚠️  传感器停止失败: {e}")

        # 4. 清理显示
        try:
            Display.deinit()
            print("   ✅ 显示系统已清理")
        except Exception as e:
            print(f"   ⚠️  显示系统清理失败: {e}")

        # 5. 清理MediaManager（根据初始化状态）
        try:
            if 'use_media_manager' in locals() and use_media_manager:
                MediaManager.deinit()
                print("   ✅ MediaManager已清理")
            else:
                print("   ℹ️  MediaManager未使用，跳过清理")
        except Exception as e:
            print(f"   ⚠️  MediaManager清理失败: {e}")

        # 6. 清理串口
        try:
            if uart:
                uart.deinit()
                print("   ✅ 串口已清理")
        except Exception as e:
            print(f"   ⚠️  串口清理失败: {e}")

        # 7. 强制垃圾回收和内存清理
        try:
            gc.collect()
            time.sleep(0.5)
            nn.shrink_memory_pool()
            print("   ✅ 内存清理完成")
        except Exception as e:
            print(f"   ⚠️  内存清理失败: {e}")

        print("🔄 资源清理完成")

        # Step12新增：最终双模式报告
        if frame_count > 0:
            detection_rate = detection_count / frame_count * 100
            # 修复：确保变量存在
            final_fps = last_fps if 'last_fps' in globals() else 0.0
            print("📊 Step12双模式系统最终报告:")
            print(f"   总帧数: {frame_count}")
            print(f"   检测成功: {detection_count}")
            print(f"   检测成功率: {detection_rate:.1f}%")
            print(f"   平均FPS: {final_fps:.1f}")
            print(f"   最终模式: {'基础模式' if MODE_CONFIG['current_mode'] == MODE_BASIC else '扩展模式'}")

            if detection_rate >= 70:
                print("📈 Step12系统评级: 优秀 - 双模式运行稳定")
            elif detection_rate >= 50:
                print("📈 Step12系统评级: 良好 - 双模式基本稳定")
            else:
                print("📈 Step12系统评级: 需调整 - 建议检查配置")

        print("第十二步双模式切换系统测试完成")
        print("🎉 Step12核心功能总结:")
        print("   ✅ 双模式切换 - 基础模式+扩展模式")
        print("   ✅ 动态移动点生成 - 圆周运动+摆动模式")
        print("   ✅ 可视化模式按钮 - 实时模式状态显示")
        print("   ✅ 智能坐标输出 - 根据模式发送不同坐标")
        print("   ✅ STM32串口通信 - 保持协议兼容性")
        print("   ✅ 增强可视化显示 - 虚拟圆+移动点+信息面板")
        print("   ✅ 性能监控系统 - 双模式状态监控")

    print("第十二步双模式切换系统测试完成")
    return 0

if __name__ == "__main__":
    detection()
