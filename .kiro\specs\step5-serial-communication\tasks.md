# Implementation Plan

- [x] 1. Set up project structure and serial communication foundation




  - Copy step4_交互式按键界面.py as the base for step5_串口通信功能.py
  - Add UART imports and GPIO configuration for serial communication
  - Initialize UART2 with 115200 baud rate and 8N1 configuration
  - _Requirements: 1.4, 3.1_

- [x] 2. Implement coordinate data structures and packet formatting





  - [x] 2.1 Create CoordinateData class for coordinate management


    - Define CoordinateData class with rect_x, rect_y, screen_x, screen_y fields
    - Implement to_packet_bytes() method to convert coordinates to 8-byte format
    - Add coordinate validation to ensure values are within 0-480 range
    - _Requirements: 1.5, 2.2_



  - [-] 2.2 Implement packet formation and checksum calculation



    - Create build_packet() function that formats coordinates into 13-byte packets
    - Implement checksum calculation using sum of coordinate bytes modulo 256
    - Add packet header (0xAA, 0x55) and tail (0xFF, 0xEE) formatting
    - _Requirements: 2.1, 2.3, 2.4_

- [ ] 3. Create SerialManager class for UART communication
  - [ ] 3.1 Implement SerialManager initialization and connection handling
    - Create SerialManager class with UART initialization
    - Add connection status tracking and error counting
    - Implement graceful initialization failure handling
    - _Requirements: 5.1, 5.3_

  - [ ] 3.2 Implement coordinate transmission functionality
    - Create send_coordinates() method for non-blocking packet transmission
    - Add error handling for transmission failures
    - Implement connection status checking before transmission
    - _Requirements: 1.1, 1.2, 5.2_

- [ ] 4. Integrate coordinate extraction with existing detection system
  - [ ] 4.1 Create coordinate extraction function
    - Implement extract_coordinates_from_detection() to process step4 detection results
    - Add logic to use actual rectangle center when detected, default center when not
    - Ensure coordinate extraction works with existing rect_total_center data
    - _Requirements: 1.1, 1.2_

  - [ ] 4.2 Integrate coordinate extraction into main loop
    - Modify main loop to call coordinate extraction after detection
    - Add coordinate data creation from detection results
    - Ensure integration doesn't affect existing step4 functionality
    - _Requirements: 3.1, 3.2_

- [ ] 5. Implement error handling and status management
  - [ ] 5.1 Create SerialErrorHandler class
    - Implement error counting and connection status tracking
    - Add graceful degradation when serial communication fails
    - Create reconnection attempt logic with timing delays
    - _Requirements: 5.1, 5.2, 5.3_

  - [ ] 5.2 Add error handling to main application flow
    - Integrate error handler with SerialManager
    - Add try-catch blocks around serial operations
    - Ensure main application continues running when serial fails
    - _Requirements: 3.3, 5.4_

- [ ] 6. Add serial communication to main application loop
  - [ ] 6.1 Integrate serial transmission into main loop
    - Add coordinate extraction and transmission after vision processing
    - Ensure serial operations don't block UI rendering
    - Maintain step4 FPS performance with serial communication active
    - _Requirements: 3.3, 3.4_

  - [ ] 6.2 Add optional status display for serial communication
    - Create optional debug output for serial communication status
    - Add connection status indication without affecting main UI
    - Implement frame rate monitoring to ensure performance
    - _Requirements: 5.5_

- [ ] 7. Create STM32 receiver code
  - [ ] 7.1 Implement STM32 packet parser structure
    - Create PacketState_t enum for parsing state machine
    - Define CoordinateData_t structure for received coordinates
    - Implement PacketParser class with state management
    - _Requirements: 4.1, 4.2_

  - [ ] 7.2 Implement STM32 packet processing logic
    - Create ProcessByte() method for byte-by-byte packet parsing
    - Add checksum verification for received packets
    - Implement coordinate reconstruction from 8-bit pairs to 16-bit values
    - _Requirements: 4.3, 4.4_

  - [ ] 7.3 Create STM32 UART interrupt handler
    - Implement UART_IRQHandler for receiving serial data
    - Add packet validation and coordinate extraction
    - Create example coordinate processing function
    - _Requirements: 4.5_

- [ ] 8. Create comprehensive testing and validation
  - [ ] 8.1 Implement unit tests for packet formation
    - Create test functions for coordinate to packet conversion
    - Add checksum calculation validation tests
    - Test packet structure and byte ordering
    - _Requirements: 2.1, 2.2, 2.3_

  - [ ] 8.2 Create integration tests for step4 compatibility
    - Verify all step4 features work identically with serial communication
    - Test threshold editing, dictionary saving, and interface switching
    - Ensure FPS performance remains stable
    - _Requirements: 3.1, 3.2, 3.4_

- [ ] 9. Create documentation and usage instructions
  - [ ] 9.1 Write step5 usage documentation
    - Create step5_使用说明.md with setup and usage instructions
    - Document serial communication configuration and troubleshooting
    - Add STM32 integration examples and wiring diagrams
    - _Requirements: All requirements_

  - [ ] 9.2 Create example STM32 project files
    - Provide complete STM32 example project with packet parser
    - Include UART configuration and coordinate processing examples
    - Add debugging and testing utilities for STM32 side
    - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_