# bilibili搜索学不会电磁场看教程
# K230矩形中心激光定位系统 - 第一步：基础显示功能
# 基于电赛识别基础部分.py的成功显示配置，确保LCD屏幕正常显示
# 参考：电赛识别基础部分.py

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *

sensor = None

try:
    print("第一步：基础显示功能测试开始")
    print("基于电赛识别基础部分.py的成功配置")

    # 图像参数设置 - 完全参考电赛识别基础部分.py
    picture_width = 400
    picture_height = 240
    sensor_id = 2

    # 显示模式设置 - 完全参考电赛识别基础部分.py
    DISPLAY_MODE = "LCD"

    # 根据模式设置显示宽高 - 完全参考电赛识别基础部分.py
    if DISPLAY_MODE == "VIRT":
        # 虚拟显示器模式
        DISPLAY_WIDTH = ALIGN_UP(1920, 16)
        DISPLAY_HEIGHT = 1080
    elif DISPLAY_MODE == "LCD":
        # 3.1寸屏幕模式
        DISPLAY_WIDTH = 800
        DISPLAY_HEIGHT = 480
    elif DISPLAY_MODE == "HDMI":
        # HDMI扩展板模式
        DISPLAY_WIDTH = 1920
        DISPLAY_HEIGHT = 1080
    else:
        raise ValueError("未知的 DISPLAY_MODE，请选择 'VIRT', 'LCD' 或 'HDMI'")

    print(f"显示模式: {DISPLAY_MODE}")
    print(f"图像分辨率: {picture_width}x{picture_height}")
    print(f"显示分辨率: {DISPLAY_WIDTH}x{DISPLAY_HEIGHT}")

    # 传感器初始化 - 完全参考电赛识别基础部分.py
    sensor = Sensor(id=sensor_id)
    sensor.reset()

    # 设置通道0的输出尺寸 - 完全参考电赛识别基础部分.py
    sensor.set_framesize(width=picture_width, height=picture_height, chn=CAM_CHN_ID_0)
    # 设置通道0的输出像素格式为RGB565 - 完全参考电赛识别基础部分.py
    sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)

    # 根据模式初始化显示器 - 完全参考电赛识别基础部分.py
    if DISPLAY_MODE == "VIRT":
        Display.init(Display.VIRT, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, fps=60)
    elif DISPLAY_MODE == "LCD":
        Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
    elif DISPLAY_MODE == "HDMI":
        Display.init(Display.LT9611, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)

    # 初始化媒体管理器 - 完全参考电赛识别基础部分.py
    MediaManager.init()
    # 启动传感器 - 完全参考电赛识别基础部分.py
    sensor.run()

    # 时钟对象用于FPS计算
    clock = time.clock()

    print("系统初始化完成，开始显示图像...")
    print("如果LCD屏幕有显示，说明基础显示功能正常")

    # 帧计数器
    frame_count = 0

    # 主循环 - 基于电赛识别基础部分.py，添加基础显示功能
    while True:
        clock.tick()
        os.exitpoint()

        try:
            # 捕获通道0的图像 - 完全参考电赛识别基础部分.py
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            
            frame_count += 1

            # 绘制基础信息 - 验证文字绘制功能
            img.draw_string_advanced(10, 10, 25, "K230激光定位系统", color=(255, 255, 0))
            img.draw_string_advanced(10, 40, 20, "第一步：基础显示功能", color=(0, 255, 255))
            img.draw_string_advanced(10, 65, 16, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
            img.draw_string_advanced(10, 85, 14, f"帧数: {frame_count}", color=(255, 255, 255))
            img.draw_string_advanced(10, 105, 14, f"分辨率: {picture_width}x{picture_height}", color=(255, 255, 255))
            img.draw_string_advanced(10, 125, 14, f"显示: {DISPLAY_WIDTH}x{DISPLAY_HEIGHT}", color=(255, 255, 255))

            # 绘制测试图形 - 验证绘制功能正常
            # 绘制中心十字线
            center_x = picture_width // 2
            center_y = picture_height // 2
            img.draw_line(center_x - 30, center_y, center_x + 30, center_y, color=(255, 0, 0), thickness=2)
            img.draw_line(center_x, center_y - 30, center_x, center_y + 30, color=(255, 0, 0), thickness=2)

            # 绘制中心圆
            img.draw_circle(center_x, center_y, 20, color=(0, 255, 0), thickness=2, fill=False)
            img.draw_circle(center_x, center_y, 10, color=(0, 255, 0), thickness=1, fill=False)

            # 绘制四个角的标记 - 验证边界绘制
            corner_size = 15
            # 左上角
            img.draw_rectangle(5, 145, corner_size, corner_size, color=(255, 0, 255), thickness=2, fill=False)
            # 右上角
            img.draw_rectangle(picture_width - corner_size - 5, 145, corner_size, corner_size, color=(255, 0, 255), thickness=2, fill=False)
            # 左下角
            img.draw_rectangle(5, picture_height - corner_size - 5, corner_size, corner_size, color=(255, 0, 255), thickness=2, fill=False)
            # 右下角
            img.draw_rectangle(picture_width - corner_size - 5, picture_height - corner_size - 5, corner_size, corner_size, color=(255, 0, 255), thickness=2, fill=False)

            # 绘制动态元素 - 验证动画效果
            # 旋转的线条
            import math
            angle = (frame_count * 2) % 360
            rad = math.radians(angle)
            line_length = 25
            end_x = center_x + int(line_length * math.cos(rad))
            end_y = center_y + int(line_length * math.sin(rad))
            img.draw_line(center_x, center_y, end_x, end_y, color=(255, 255, 0), thickness=2)

            # 显示状态信息
            img.draw_string_advanced(10, picture_height - 60, 14, "状态: 基础显示功能测试", color=(0, 255, 0))
            img.draw_string_advanced(10, picture_height - 45, 12, "LCD显示: 正常", color=(0, 255, 0))
            img.draw_string_advanced(10, picture_height - 30, 12, "图形绘制: 正常", color=(0, 255, 0))
            img.draw_string_advanced(10, picture_height - 15, 12, "下一步: 矩形检测功能", color=(255, 255, 0))

            # 显示捕获的图像，中心对齐，居中显示 - 完全参考电赛识别基础部分.py
            Display.show_image(img, x=int((DISPLAY_WIDTH - picture_width) / 2), y=int((DISPLAY_HEIGHT - picture_height) / 2))

            # 每100帧输出一次状态信息
            if frame_count % 100 == 0:
                print(f"显示正常 - 帧数: {frame_count}, FPS: {clock.fps():.1f}")

        except Exception as e:
            print(f"主循环错误: {e}")
            time.sleep_ms(100)  # 出错时稍作延迟

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
    import traceback
    traceback.print_exc()
finally:
    print("清理资源...")
    # 停止传感器运行 - 完全参考电赛识别基础部分.py
    if isinstance(sensor, Sensor):
        sensor.stop()
    # 反初始化显示模块 - 完全参考电赛识别基础部分.py
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    # 释放媒体缓冲区 - 完全参考电赛识别基础部分.py
    MediaManager.deinit()
    print("第一步测试完成")
