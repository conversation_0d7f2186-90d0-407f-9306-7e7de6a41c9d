# K230矩形检测系统 - 阈值配置示例文件
# 此文件展示了不同环境下的LAB阈值配置示例

# ==================== 基本配置格式 ====================
# LAB阈值格式: (L_min, L_max, A_min, A_max, B_min, B_max)
# L: 亮度通道 (0-100)
# A: 绿红轴 (-128到127)
# B: 蓝黄轴 (-128到127)

# ==================== 矩形检测阈值示例 ====================

# 1. 黑色矩形 (室内正常光照)
rect_black_indoor = (13, 35, 20, -5, -44, 16)

# 2. 黑色矩形 (室外强光)
rect_black_outdoor = (5, 25, 15, -10, -50, 10)

# 3. 黑色矩形 (弱光环境)
rect_black_lowlight = (0, 20, 25, 0, -40, 20)

# 4. 深蓝色矩形
rect_blue = (20, 50, 10, 30, -80, -20)

# 5. 深绿色矩形
rect_green = (25, 55, -40, -10, -20, 20)

# ==================== 激光点检测阈值示例 ====================

# 1. 蓝紫激光点 (标准)
laser_blue_standard = (47, 80, 9, 91, -55, 63)

# 2. 蓝紫激光点 (备用)
laser_blue_backup = (16, 37, 23, 74, -48, 52)

# 3. 红色激光点
laser_red = (30, 70, 20, 80, 10, 60)

# 4. 绿色激光点
laser_green = (40, 80, -60, -20, -30, 30)

# ==================== 环境适应性配置 ====================

# 室内环境配置
indoor_config = {
    'rect_threshold': (13, 35, 20, -5, -44, 16),
    'laser_threshold': (47, 80, 9, 91, -55, 63),
    'min_area': 2000,
    'max_area': 60000
}

# 室外环境配置
outdoor_config = {
    'rect_threshold': (5, 25, 15, -10, -50, 10),
    'laser_threshold': (40, 75, 15, 85, -60, 55),
    'min_area': 3000,
    'max_area': 80000
}

# 弱光环境配置
lowlight_config = {
    'rect_threshold': (0, 20, 25, 0, -40, 20),
    'laser_threshold': (20, 60, 20, 90, -70, 70),
    'min_area': 1500,
    'max_area': 50000
}

# ==================== 使用说明 ====================

# 1. 根据实际环境选择合适的阈值配置
# 2. 在step8阈值编辑界面中手动调整参数
# 3. 使用"保存到字典"功能保存有效配置
# 4. 通过"从字典加载"功能快速切换配置

# ==================== 调试技巧 ====================

# 1. 先调整L值（亮度），确定目标物体的亮度范围
# 2. 再调整A值（绿红轴），区分颜色
# 3. 最后调整B值（蓝黄轴），精细化颜色识别
# 4. 观察二值化预览效果，确保目标清晰分离

# ==================== 常见问题解决 ====================

# 问题1: 检测不到目标
# 解决: 放宽L值范围，降低A、B值要求

# 问题2: 误检太多
# 解决: 缩小L值范围，提高A、B值精度

# 问题3: 光照变化影响大
# 解决: 使用多组阈值，根据环境自动切换

# 问题4: 目标边缘不清晰
# 解决: 微调A、B值，增强颜色对比度
