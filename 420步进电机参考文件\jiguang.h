#ifndef JIGUANG_H
#define JIGUANG_H
// 定义 3200 转 = 360°
#define PULSES_PER_REV 3200.0f  // 3200 脉冲 = 1 转
#define DEGREES_PER_REV 360.0f  // 1 转 = 360°

// 计算每clk对应的角度
#define ANGLE_PER_CLK (DEGREES_PER_REV / PULSES_PER_REV)  // 0.1125° per clk
//控制pitch轴的电机（带激光笔）零点位置朝x轴正方向,控制yaw轴的电机，零点位置朝x轴正方向（符合右手螺旋定理）
#define pitch_addr 4
#define rotation_addr 1
#define pitch_dir 0
#define rotation_dir 1
// 俯仰轴和偏航轴的角度限制（全为正）
#define PITCH_MAX PITCH_MAX_ANGLE/ANGLE_PER_CLK
#define YAW_MAX YAW_MAX_ANGLE/ANGLE_PER_CLK
#define PITCH_MAX_ANGLE 135  
#define YAW_MAX_ANGLE 180 
#define pitch_mid__clk PITCH_MAX/2
#define yaw_mid__clk YAW_MAX/2

#include <stdbool.h>
// 函数声明
float clks_to_angle(int clks, bool is_pitch_axis, bool is_clockwise);
float calculate_current_angle(int current_clks, int zero_clks, bool is_pitch_axis);
void draw_square();
void draw_sine_wave(float amplitude, float frequency, float duration);
int move_yaw();
int move_pitch();

#endif
