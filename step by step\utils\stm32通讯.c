// STM32通讯代码示例
// 用于与K230进行串口通信的STM32端代码

#include "main.h"
#include "usart.h"
#include <stdio.h>
#include <string.h>

// 数据包格式定义
#define PACKET_HEADER_1 0xAA
#define PACKET_HEADER_2 0x55
#define PACKET_TAIL_1   0x0D
#define PACKET_TAIL_2   0x0A

// 数据包结构
typedef struct {
    uint8_t header[2];      // 包头 0xAA 0x55
    uint16_t rect_x;        // 矩形中心X坐标
    uint16_t rect_y;        // 矩形中心Y坐标
    uint16_t screen_x;      // 屏幕中心X坐标
    uint16_t screen_y;      // 屏幕中心Y坐标
    uint8_t checksum;       // 校验和
    uint8_t tail[2];        // 包尾 0x0D 0x0A
} CoordinatePacket;

// 接收缓冲区
uint8_t rx_buffer[256];
uint8_t rx_index = 0;

// 坐标数据
uint16_t target_x = 0;
uint16_t target_y = 0;
uint16_t center_x = 200;
uint16_t center_y = 120;
uint8_t motion_state = 0;

/**
 * @brief 计算校验和
 * @param data 数据指针
 * @param len 数据长度
 * @return 校验和
 */
uint8_t calculate_checksum(uint8_t *data, uint8_t len) {
    uint16_t sum = 0;
    for (int i = 0; i < len; i++) {
        sum += data[i];
    }
    return (uint8_t)(sum % 256);
}

/**
 * @brief 解析接收到的数据包
 * @param packet 数据包指针
 * @return 解析结果 1-成功 0-失败
 */
uint8_t parse_packet(CoordinatePacket *packet) {
    // 检查包头
    if (packet->header[0] != PACKET_HEADER_1 || packet->header[1] != PACKET_HEADER_2) {
        return 0;
    }
    
    // 检查包尾
    if (packet->tail[0] != PACKET_TAIL_1 || packet->tail[1] != PACKET_TAIL_2) {
        return 0;
    }
    
    // 计算校验和
    uint8_t calculated_checksum = calculate_checksum((uint8_t*)&packet->rect_x, 9);
    if (calculated_checksum != packet->checksum) {
        return 0;
    }
    
    // 提取坐标数据
    target_x = (packet->rect_x >> 8) | (packet->rect_x << 8);  // 字节序转换
    target_y = (packet->rect_y >> 8) | (packet->rect_y << 8);
    center_x = (packet->screen_x >> 8) | (packet->screen_x << 8);
    center_y = (packet->screen_y >> 8) | (packet->screen_y << 8);
    motion_state = packet->motion_state;
    
    return 1;
}

/**
 * @brief 串口接收中断回调函数
 * @param huart 串口句柄
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart) {
    if (huart->Instance == USART1) {
        // 检查是否接收到完整数据包
        if (rx_index >= sizeof(CoordinatePacket)) {
            CoordinatePacket *packet = (CoordinatePacket*)rx_buffer;
            
            if (parse_packet(packet)) {
                // 数据包解析成功，处理坐标数据
                printf("接收到坐标: 目标(%d,%d) 中心(%d,%d) 运动状态:%d\r\n", 
                       target_x, target_y, center_x, center_y, motion_state);
                
                // 在这里添加你的控制逻辑
                // 例如：控制舵机、电机等
                process_coordinate_data();
            }
            
            // 重置接收缓冲区
            rx_index = 0;
            memset(rx_buffer, 0, sizeof(rx_buffer));
        }
        
        // 继续接收下一个字节
        HAL_UART_Receive_IT(&huart1, &rx_buffer[rx_index++], 1);
    }
}

/**
 * @brief 处理坐标数据的控制逻辑
 */
void process_coordinate_data(void) {
    // 计算偏差
    int16_t error_x = target_x - center_x;
    int16_t error_y = target_y - center_y;
    
    // 根据运动状态调整控制参数
    float kp = 1.0f;
    switch (motion_state) {
        case 0:  // 静止
            kp = 1.0f;
            break;
        case 1:  // 慢速运动
            kp = 1.2f;
            break;
        case 2:  // 快速运动
            kp = 1.5f;
            break;
    }
    
    // 简单的比例控制
    int16_t control_x = (int16_t)(error_x * kp);
    int16_t control_y = (int16_t)(error_y * kp);
    
    // 在这里添加具体的控制输出
    // 例如：PWM输出、舵机控制等
    
    printf("控制输出: X=%d, Y=%d\r\n", control_x, control_y);
}

/**
 * @brief 初始化串口通信
 */
void init_uart_communication(void) {
    // 启动串口接收中断
    HAL_UART_Receive_IT(&huart1, &rx_buffer[0], 1);
    
    printf("STM32串口通信初始化完成\r\n");
    printf("等待K230数据...\r\n");
}

/**
 * @brief 主循环中的通信处理
 */
void communication_loop(void) {
    // 这里可以添加定期的状态检查
    // 或者发送反馈数据给K230
    
    static uint32_t last_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 每1秒输出一次状态
    if (current_time - last_time >= 1000) {
        printf("系统状态: 目标(%d,%d) 运动状态:%d\r\n", 
               target_x, target_y, motion_state);
        last_time = current_time;
    }
}
