# Step12进阶模式虚拟圆周摆动可视化演示
# 使用matplotlib创建摆动轨迹的可视化演示

import math
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import numpy as np

# 模拟Step12配置
ADVANCED_MODE_CONFIG = {
    'virtual_circle_radius': 40,
    'swing_speed': 2.0,
    'swing_amplitude': 90,
    'swing_mode': 'sine',
    'initial_angle': 0,
}

class SwingVisualizer:
    def __init__(self):
        self.swing_angle = 0.0
        self.swing_direction = 1
        self.center_x = 500
        self.center_y = 400
        self.swing_points = []
        self.frame_count = 0
        
        # 设置图形
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.ax.set_xlim(400, 600)
        self.ax.set_ylim(350, 450)
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)
        self.ax.set_title('Step12 进阶模式 - 虚拟圆周摆动演示', fontsize=14, fontweight='bold')
        
        # 绘制元素
        self.center_point, = self.ax.plot([], [], 'ro', markersize=10, label='矩形中心点')
        self.virtual_circle, = self.ax.plot([], [], 'c-', linewidth=2, label='虚拟圆')
        self.swing_point, = self.ax.plot([], [], 'mo', markersize=8, label='摆动点')
        self.swing_trail, = self.ax.plot([], [], 'm-', alpha=0.5, linewidth=1, label='摆动轨迹')
        self.connection_line, = self.ax.plot([], [], 'g--', linewidth=2, label='连接线')
        
        # 屏幕中心点（模拟）
        screen_center_x = 540
        screen_center_y = 430
        self.ax.plot(screen_center_x, screen_center_y, 'ys', markersize=12, label='屏幕中心')
        
        self.ax.legend(loc='upper right')
        
        # 信息文本
        self.info_text = self.ax.text(0.02, 0.98, '', transform=self.ax.transAxes, 
                                     verticalalignment='top', fontfamily='monospace',
                                     bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    def calculate_swing_point(self):
        """计算摆动点坐标"""
        radius = ADVANCED_MODE_CONFIG['virtual_circle_radius']
        speed = ADVANCED_MODE_CONFIG['swing_speed']
        amplitude = ADVANCED_MODE_CONFIG['swing_amplitude']
        
        # 更新摆动角度
        self.swing_angle += speed * self.swing_direction
        if self.swing_angle >= amplitude:
            self.swing_direction = -1
            self.swing_angle = amplitude
        elif self.swing_angle <= -amplitude:
            self.swing_direction = 1
            self.swing_angle = -amplitude
        
        # 计算摆动点坐标
        angle_rad = math.radians(self.swing_angle)
        swing_x = self.center_x + radius * math.cos(angle_rad)
        swing_y = self.center_y + radius * math.sin(angle_rad)
        
        return swing_x, swing_y
    
    def update_frame(self, frame):
        """更新动画帧"""
        self.frame_count = frame
        
        # 计算摆动点
        swing_x, swing_y = self.calculate_swing_point()
        
        # 记录摆动轨迹
        self.swing_points.append((swing_x, swing_y))
        if len(self.swing_points) > 100:  # 保持最近100个点
            self.swing_points.pop(0)
        
        # 更新中心点
        self.center_point.set_data([self.center_x], [self.center_y])
        
        # 更新虚拟圆
        circle_angles = np.linspace(0, 2*np.pi, 100)
        radius = ADVANCED_MODE_CONFIG['virtual_circle_radius']
        circle_x = self.center_x + radius * np.cos(circle_angles)
        circle_y = self.center_y + radius * np.sin(circle_angles)
        self.virtual_circle.set_data(circle_x, circle_y)
        
        # 更新摆动点
        self.swing_point.set_data([swing_x], [swing_y])
        
        # 更新摆动轨迹
        if len(self.swing_points) > 1:
            trail_x, trail_y = zip(*self.swing_points)
            self.swing_trail.set_data(trail_x, trail_y)
        
        # 更新连接线（到屏幕中心）
        screen_center_x, screen_center_y = 540, 430
        self.connection_line.set_data([swing_x, screen_center_x], [swing_y, screen_center_y])
        
        # 更新信息文本
        distance = math.sqrt((swing_x - self.center_x)**2 + (swing_y - self.center_y)**2)
        offset_x = swing_x - screen_center_x
        offset_y = swing_y - screen_center_y
        
        info = f"""帧数: {frame}
摆动角度: {self.swing_angle:6.1f}°
摆动方向: {self.swing_direction:2d}
矩形中心: ({self.center_x}, {self.center_y})
摆动点: ({swing_x:6.1f}, {swing_y:6.1f})
圆周距离: {distance:5.1f}px
偏差: ΔX={offset_x:+6.1f}, ΔY={offset_y:+6.1f}"""
        
        self.info_text.set_text(info)
        
        return (self.center_point, self.virtual_circle, self.swing_point, 
                self.swing_trail, self.connection_line, self.info_text)
    
    def start_animation(self):
        """开始动画"""
        ani = animation.FuncAnimation(self.fig, self.update_frame, frames=200, 
                                    interval=50, blit=True, repeat=True)
        plt.tight_layout()
        plt.show()
        return ani

def create_static_comparison():
    """创建基础模式vs进阶模式的静态对比图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 基础模式
    ax1.set_title('基础模式 - 直接连接', fontsize=12, fontweight='bold')
    ax1.set_xlim(450, 550)
    ax1.set_ylim(350, 450)
    ax1.set_aspect('equal')
    ax1.grid(True, alpha=0.3)
    
    center_x, center_y = 500, 400
    screen_x, screen_y = 520, 420
    
    ax1.plot(center_x, center_y, 'ro', markersize=10, label='矩形中心点')
    ax1.plot(screen_x, screen_y, 'ys', markersize=10, label='屏幕中心')
    ax1.plot([center_x, screen_x], [center_y, screen_y], 'b-', linewidth=3, label='连接线')
    ax1.legend()
    ax1.text(0.02, 0.98, '发送坐标: 矩形中心点\n连接: 中心点 → 屏幕中心', 
             transform=ax1.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 进阶模式
    ax2.set_title('进阶模式 - 虚拟圆周摆动', fontsize=12, fontweight='bold')
    ax2.set_xlim(450, 550)
    ax2.set_ylim(350, 450)
    ax2.set_aspect('equal')
    ax2.grid(True, alpha=0.3)
    
    # 绘制虚拟圆
    circle_angles = np.linspace(0, 2*np.pi, 100)
    radius = 40
    circle_x = center_x + radius * np.cos(circle_angles)
    circle_y = center_y + radius * np.sin(circle_angles)
    ax2.plot(circle_x, circle_y, 'c-', linewidth=2, label='虚拟圆')
    
    # 摆动点示例
    swing_angle = 45  # 45度位置
    swing_x = center_x + radius * math.cos(math.radians(swing_angle))
    swing_y = center_y + radius * math.sin(math.radians(swing_angle))
    
    ax2.plot(center_x, center_y, 'ro', markersize=10, label='矩形中心点')
    ax2.plot(swing_x, swing_y, 'mo', markersize=8, label='摆动点')
    ax2.plot(screen_x, screen_y, 'ys', markersize=10, label='屏幕中心')
    ax2.plot([swing_x, screen_x], [swing_y, screen_y], 'g--', linewidth=3, label='连接线')
    ax2.legend()
    ax2.text(0.02, 0.98, f'发送坐标: 摆动点\n摆动角度: {swing_angle}°\n连接: 摆动点 → 屏幕中心', 
             transform=ax2.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    print("🚀 Step12进阶模式虚拟圆周摆动可视化演示")
    print("=" * 60)
    print("1. 动态摆动演示")
    print("2. 模式对比图")
    print("=" * 60)
    
    # 创建静态对比图
    create_static_comparison()
    
    # 创建动态演示
    print("启动动态摆动演示...")
    visualizer = SwingVisualizer()
    ani = visualizer.start_animation()
    
    print("✅ 演示完成")
