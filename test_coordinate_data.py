# 独立测试文件 - 测试CoordinateData类和数据包格式化功能
# 不依赖K230特定模块，可在任何Python环境中运行

from dataclasses import dataclass
from typing import List

@dataclass
class CoordinateData:
    """坐标数据管理类 - 用于管理矩形中心和屏幕中心坐标"""
    rect_x: int = 240      # 矩形中心X坐标 (默认: 屏幕中心)
    rect_y: int = 120      # 矩形中心Y坐标 (默认: 屏幕中心)  
    screen_x: int = 240    # 屏幕中心X坐标 (常量)
    screen_y: int = 120    # 屏幕中心Y坐标 (常量)
    
    def __post_init__(self):
        """初始化后验证坐标值范围"""
        self._validate_coordinates()
    
    def _validate_coordinates(self):
        """验证坐标值在0-480范围内"""
        coords = [self.rect_x, self.rect_y, self.screen_x, self.screen_y]
        coord_names = ['rect_x', 'rect_y', 'screen_x', 'screen_y']
        
        for coord, name in zip(coords, coord_names):
            if not (0 <= coord <= 480):
                print(f"Warning: {name}={coord} 超出范围 [0, 480], 将被限制")
                # 限制坐标值在有效范围内
                if coord < 0:
                    setattr(self, name, 0)
                elif coord > 480:
                    setattr(self, name, 480)
    
    def to_packet_bytes(self) -> List[int]:
        """将坐标转换为8字节数据包格式
        
        Returns:
            List[int]: 8字节列表，每个坐标分解为高字节和低字节
            格式: [rect_x_h, rect_x_l, rect_y_h, rect_y_l, 
                   screen_x_h, screen_x_l, screen_y_h, screen_y_l]
        """
        # 确保坐标值在有效范围内
        self._validate_coordinates()
        
        # 将每个16位坐标分解为高字节和低字节
        packet_bytes = [
            (self.rect_x >> 8) & 0xFF,   # rect_x 高字节
            self.rect_x & 0xFF,          # rect_x 低字节
            (self.rect_y >> 8) & 0xFF,   # rect_y 高字节  
            self.rect_y & 0xFF,          # rect_y 低字节
            (self.screen_x >> 8) & 0xFF, # screen_x 高字节
            self.screen_x & 0xFF,        # screen_x 低字节
            (self.screen_y >> 8) & 0xFF, # screen_y 高字节
            self.screen_y & 0xFF         # screen_y 低字节
        ]
        
        return packet_bytes


def build_packet(coord_data: CoordinateData) -> List[int]:
    """构建完整的13字节数据包
    
    数据包格式:
    [Header1, Header2, 坐标数据(8字节), Checksum, Tail1, Tail2]
    [0xAA, 0x55, rect_x_h, rect_x_l, rect_y_h, rect_y_l, 
     screen_x_h, screen_x_l, screen_y_h, screen_y_l, checksum, 0xFF, 0xEE]
    
    Args:
        coord_data: CoordinateData对象
        
    Returns:
        List[int]: 13字节完整数据包
    """
    # 数据包头部和尾部
    PACKET_HEADER = [0xAA, 0x55]
    PACKET_TAIL = [0xFF, 0xEE]
    
    # 获取坐标字节数据
    coord_bytes = coord_data.to_packet_bytes()
    
    # 计算校验和 (坐标字节之和模256)
    checksum = sum(coord_bytes) % 256
    
    # 构建完整数据包
    packet = PACKET_HEADER + coord_bytes + [checksum] + PACKET_TAIL
    
    return packet


def calculate_checksum(coord_bytes: List[int]) -> int:
    """计算坐标字节的校验和
    
    Args:
        coord_bytes: 8字节坐标数据列表
        
    Returns:
        int: 校验和 (字节和模256)
    """
    return sum(coord_bytes) % 256


def test_coordinate_data():
    """测试CoordinateData类的功能"""
    print("=== CoordinateData类测试 ===")
    
    # 测试1: 正常坐标值
    coord1 = CoordinateData(rect_x=300, rect_y=200, screen_x=240, screen_y=120)
    print(f"测试1 - 正常坐标: {coord1}")
    packet_bytes1 = coord1.to_packet_bytes()
    print(f"数据包字节: {packet_bytes1}")
    print(f"验证: rect_x=300 -> 高字节={packet_bytes1[0]}, 低字节={packet_bytes1[1]} -> 重构={(packet_bytes1[0] << 8) | packet_bytes1[1]}")
    
    # 测试2: 边界值
    coord2 = CoordinateData(rect_x=0, rect_y=480, screen_x=240, screen_y=120)
    print(f"测试2 - 边界坐标: {coord2}")
    packet_bytes2 = coord2.to_packet_bytes()
    print(f"数据包字节: {packet_bytes2}")
    
    # 测试3: 超出范围值 (应该被限制)
    coord3 = CoordinateData(rect_x=500, rect_y=-10, screen_x=240, screen_y=120)
    print(f"测试3 - 超范围坐标: {coord3}")
    packet_bytes3 = coord3.to_packet_bytes()
    print(f"数据包字节: {packet_bytes3}")
    
    print("=== CoordinateData类测试完成 ===\n")


def test_packet_formation():
    """测试数据包构建功能"""
    print("=== 数据包构建测试 ===")
    
    # 创建测试坐标
    coord = CoordinateData(rect_x=300, rect_y=200, screen_x=240, screen_y=120)
    
    # 构建数据包
    packet = build_packet(coord)
    print(f"完整数据包 ({len(packet)}字节): {packet}")
    print(f"数据包格式:")
    print(f"  头部: {packet[0:2]} (应为 [170, 85])")
    print(f"  坐标: {packet[2:10]}")
    print(f"  校验: {packet[10]} (计算值: {calculate_checksum(packet[2:10])})")
    print(f"  尾部: {packet[11:13]} (应为 [255, 238])")
    
    # 验证数据包结构
    assert len(packet) == 13, f"数据包长度错误: {len(packet)}, 应为13"
    assert packet[0] == 0xAA and packet[1] == 0x55, "数据包头部错误"
    assert packet[11] == 0xFF and packet[12] == 0xEE, "数据包尾部错误"
    assert packet[10] == calculate_checksum(packet[2:10]), "校验和错误"
    
    print("=== 数据包构建测试通过 ===\n")


def test_checksum_calculation():
    """测试校验和计算功能"""
    print("=== 校验和计算测试 ===")
    
    # 测试数据
    test_data = [0x01, 0x2C, 0x00, 0xC8, 0x00, 0xF0, 0x00, 0x78]
    expected_sum = sum(test_data)
    expected_checksum = expected_sum % 256
    
    calculated_checksum = calculate_checksum(test_data)
    
    print(f"测试数据: {test_data}")
    print(f"数据和: {expected_sum}")
    print(f"期望校验和: {expected_checksum}")
    print(f"计算校验和: {calculated_checksum}")
    
    assert calculated_checksum == expected_checksum, "校验和计算错误"
    print("=== 校验和计算测试通过 ===\n")


if __name__ == "__main__":
    print("K230串口通信功能 - 坐标数据结构和数据包格式化测试")
    print("=" * 60)
    
    # 运行所有测试
    test_coordinate_data()
    test_packet_formation() 
    test_checksum_calculation()
    
    print("所有测试完成!")
    print("=" * 60)