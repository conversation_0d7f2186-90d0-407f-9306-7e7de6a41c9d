# bilibili搜索学不会电磁场看教程
# K230矩形中心激光定位系统 - 第五步：串口通信功能
# 基于step4的交互式界面，添加后台串口通信功能
# 功能：将矩形中心坐标和屏幕中心坐标通过串口发送给STM32

import time
import os
import sys
from media.sensor import *
from media.display import *
from media.media import *
from machine import TOUCH
from machine import UART
from machine import FPIOA

# 常量定义
CAM_CHN_ID_0 = 0

# 阈值编辑全局变量
threshold_edit_mode = "rect"
threshold_current = [0, 80, 0, 255, 0, 255]

# 阈值存储机制
threshold_dict = {
    'rect': [(0, 80)],
    'laser': [(47, 80, 9, 91, -55, 63), (16, 37, 23, 74, -48, 52)]
}

# 串口通信全局变量
uart = None
SERIAL_PORT = UART.UART2
BAUD_RATE = 115200
TX_PIN = 11
RX_PIN = 12

# 数据包格式
PACKET_HEADER = [0xAA, 0x55]
PACKET_TAIL = [0x0D, 0x0A]
SCREEN_CENTER_X = 200  # 400x240图像的中心
SCREEN_CENTER_Y = 120

# 检测参数设置
RECT_THRESHOLD = [(0, 80)]
MIN_RECT_AREA = 4500
LASER_THRESHOLD = [
    (47, 80, 9, 91, -55, 63),
    (16, 37, 23, 74, -48, 52)
]
MIN_LASER_PIXELS = 20
USE_ROI = False
ROI_X = 50
ROI_Y = 30
ROI_WIDTH = 300
ROI_HEIGHT = 180

# 图像参数
picture_width = 400
picture_height = 240
sensor_id = 2
DISPLAY_MODE = "LCD"
DISPLAY_WIDTH = 800
DISPLAY_HEIGHT = 480
SCALE_FACTOR = 2.0
SCALED_WIDTH = int(picture_width * SCALE_FACTOR)
SCALED_HEIGHT = int(picture_height * SCALE_FACTOR)

# 按键定义
BUTTON_WIDTH = 150
BUTTON_HEIGHT = 60
BUTTON_Y = SCALED_HEIGHT - BUTTON_HEIGHT - 10
BUTTON_SPACING = 200
BUTTON_START_X = 50
BUTTON1_POS = (BUTTON_START_X, BUTTON_Y)
BUTTON2_POS = (BUTTON_START_X + BUTTON_SPACING, BUTTON_Y)
BUTTON3_POS = (BUTTON_START_X + BUTTON_SPACING * 2, BUTTON_Y)
BUTTON_ACTIVE_COLOR = (0, 255, 0)
BUTTON_INACTIVE_COLOR = (80, 80, 80)
BUTTON_TEXT_COLOR = (255, 255, 255)

sensor = None

def init_uart():
    """初始化串口通信"""
    global uart

    try:
        # 配置引脚功能
        fpioa = FPIOA()
        fpioa.set_function(TX_PIN, FPIOA.UART2_TXD)
        fpioa.set_function(RX_PIN, FPIOA.UART2_RXD)

        # 初始化串口
        uart = UART(SERIAL_PORT, BAUD_RATE)
        print(f"✅ 串口初始化成功 - 波特率: {BAUD_RATE}")
        return True

    except Exception as e:
        print(f"❌ 串口初始化失败: {e}")
        return False

def send_coordinates(rect_center, screen_center):
    """发送坐标数据给STM32"""
    global uart

    if uart is None:
        return False

    try:
        # 准备数据包
        packet = []
        packet.extend(PACKET_HEADER)

        # 矩形中心坐标
        if rect_center is not None:
            rect_x, rect_y = int(rect_center[0]), int(rect_center[1])
        else:
            rect_x, rect_y = 0xFFFF, 0xFFFF

        # 屏幕中心坐标
        screen_x, screen_y = int(screen_center[0]), int(screen_center[1])

        # 将16位坐标拆分为两个8位
        packet.extend([(rect_x >> 8) & 0xFF, rect_x & 0xFF])
        packet.extend([(rect_y >> 8) & 0xFF, rect_y & 0xFF])
        packet.extend([(screen_x >> 8) & 0xFF, screen_x & 0xFF])
        packet.extend([(screen_y >> 8) & 0xFF, screen_y & 0xFF])

        # 计算校验和
        checksum = sum(packet[2:]) % 256
        packet.append(checksum)
        packet.extend(PACKET_TAIL)

        # 发送数据
        uart.write(bytes(packet))

        # 调试输出
        if rect_center is not None:
            print(f"📡 发送坐标 - 矩形:({rect_x},{rect_y}) 屏幕:({screen_x},{screen_y})")
        else:
            print(f"📡 发送坐标 - 无矩形 屏幕:({screen_x},{screen_y})")

        return True

    except Exception as e:
        print(f"❌ 串口发送失败: {e}")
        return False

# 阈值相关函数（与step4相同）
def save_threshold_to_dict():
    global threshold_dict, threshold_edit_mode, threshold_current
    try:
        if threshold_edit_mode == 'rect':
            threshold_dict[threshold_edit_mode].append(threshold_current[:2])
            print("Success: 矩形阈值已保存到字典 " + str(threshold_current[:2]))
        elif threshold_edit_mode == 'laser':
            laser_threshold = [i - 127 if i > 127 else i for i in threshold_current]
            threshold_dict[threshold_edit_mode].append(laser_threshold)
            print("Success: 激光点阈值已保存到字典 " + str(laser_threshold))
        update_global_thresholds_from_dict()
        return True
    except Exception as e:
        print("Error: 保存阈值失败 " + str(e))
        return False

def update_global_thresholds_from_dict():
    global RECT_THRESHOLD, LASER_THRESHOLD, threshold_dict
    try:
        if 'rect' in threshold_dict and len(threshold_dict['rect']) > 0:
            RECT_THRESHOLD[0] = tuple(threshold_dict['rect'][-1])
            print("Update: 矩形阈值更新为 " + str(RECT_THRESHOLD[0]))
        if 'laser' in threshold_dict and len(threshold_dict['laser']) > 0:
            latest_laser = threshold_dict['laser'][-1]
            converted_laser = tuple([i + 127 if i < 0 else i for i in latest_laser])
            LASER_THRESHOLD[0] = converted_laser
            print("Update: 激光点阈值更新为 " + str(LASER_THRESHOLD[0]))
    except Exception as e:
        print("Error: 更新全局阈值失败 " + str(e))

def load_threshold_from_dict():
    global threshold_dict, threshold_current, threshold_edit_mode
    try:
        if threshold_edit_mode == 'rect':
            if 'rect' in threshold_dict and len(threshold_dict['rect']) > 0:
                latest_rect = threshold_dict['rect'][-1]
                threshold_current = list(latest_rect) + [0, 255, 0, 255]
                print("Success: 矩形阈值已从字典加载 " + str(latest_rect))
        elif threshold_edit_mode == 'laser':
            if 'laser' in threshold_dict and len(threshold_dict['laser']) > 0:
                latest_laser = threshold_dict['laser'][-1]
                threshold_current = [i + 127 if i < 0 else i for i in latest_laser]
                print("Success: 激光点阈值已从字典加载 " + str(latest_laser))
        update_global_thresholds_from_dict()
        return True
    except Exception as e:
        print("Error: 加载阈值失败 " + str(e))
        return False

def get_default_thresholds():
    global RECT_THRESHOLD, LASER_THRESHOLD, MIN_RECT_AREA, MIN_LASER_PIXELS
    RECT_THRESHOLD = [(0, 80)]
    LASER_THRESHOLD = [(47, 80, 9, 91, -55, 63), (16, 37, 23, 74, -48, 52)]
    MIN_RECT_AREA = 500
    MIN_LASER_PIXELS = 20
    print("🔄 已恢复默认阈值参数")

# 矩形检测函数
def detect_rectangles(img):
    """检测图像中的矩形并计算中心点"""
    try:
        if USE_ROI:
            detect_img = img.copy(roi=(ROI_X, ROI_Y, ROI_WIDTH, ROI_HEIGHT))
        else:
            detect_img = img

        gray_img = detect_img.to_grayscale()
        binary_img = gray_img.binary(RECT_THRESHOLD)
        rects = binary_img.find_rects(threshold=MIN_RECT_AREA)

        centers = []
        if rects:
            for i, rect in enumerate(rects):
                corners = rect.corners()
                center_x = sum([corner[0] for corner in corners]) / 4
                center_y = sum([corner[1] for corner in corners]) / 4

                if USE_ROI:
                    global_center_x = ROI_X + center_x
                    global_center_y = ROI_Y + center_y
                else:
                    global_center_x = center_x
                    global_center_y = center_y

                centers.append((global_center_x, global_center_y))

                for j in range(4):
                    next_j = (j + 1) % 4
                    if USE_ROI:
                        x1, y1 = ROI_X + corners[j][0], ROI_Y + corners[j][1]
                        x2, y2 = ROI_X + corners[next_j][0], ROI_Y + corners[next_j][1]
                    else:
                        x1, y1 = corners[j][0], corners[j][1]
                        x2, y2 = corners[next_j][0], corners[next_j][1]
                    img.draw_line(x1, y1, x2, y2, color=(0, 255, 0), thickness=2)

                img.draw_circle(int(global_center_x), int(global_center_y), 4,
                               color=(255, 0, 0), thickness=2)
                img.draw_string_advanced(int(global_center_x) - 15, int(global_center_y) - 25,
                                       10, f"R{i+1}", color=(255, 255, 0))

            if len(centers) > 1:
                total_center_x = sum([cx for cx, cy in centers]) / len(centers)
                total_center_y = sum([cy for cx, cy in centers]) / len(centers)
                total_center = (total_center_x, total_center_y)
                img.draw_circle(int(total_center_x), int(total_center_y), 8,
                               color=(0, 255, 255), thickness=3)
            else:
                total_center = centers[0] if centers else None

            return True, len(rects), centers, total_center

        return False, 0, [], None

    except Exception as e:
        print(f"矩形检测错误: {e}")
        return False, 0, [], None

# 激光点检测函数
def detect_laser_point(img):
    """检测图像中的蓝紫激光点"""
    try:
        if USE_ROI:
            detect_img = img.copy(roi=(ROI_X, ROI_Y, ROI_WIDTH, ROI_HEIGHT))
        else:
            detect_img = img

        blobs = detect_img.find_blobs(LASER_THRESHOLD, False,
                                    x_stride=1, y_stride=1,
                                    pixels_threshold=MIN_LASER_PIXELS, margin=False)

        laser_centers = []
        if blobs:
            for i, blob in enumerate(blobs):
                laser_x = blob.x() + blob.w() / 2
                laser_y = blob.y() + blob.h() / 2

                if USE_ROI:
                    global_laser_x = ROI_X + laser_x
                    global_laser_y = ROI_Y + laser_y
                    img.draw_rectangle(ROI_X + blob.x(), ROI_Y + blob.y(),
                                     blob.w(), blob.h(),
                                     color=(0, 0, 255), thickness=2, fill=False)
                else:
                    global_laser_x = laser_x
                    global_laser_y = laser_y
                    img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                                     color=(0, 0, 255), thickness=2, fill=False)

                laser_centers.append((global_laser_x, global_laser_y))
                img.draw_circle(int(global_laser_x), int(global_laser_y), 3,
                               color=(0, 0, 255), thickness=2)
                img.draw_string_advanced(int(global_laser_x) - 15, int(global_laser_y) - 25,
                                       10, f"L{i+1}", color=(0, 0, 255))

            return True, len(blobs), laser_centers

        return False, 0, []

    except Exception as e:
        print(f"激光点检测错误: {e}")
        return False, 0, []

# 阈值编辑界面函数
def show_threshold_edit_interface(img):
    global threshold_edit_mode, threshold_current

    img.draw_rectangle(0, 0, picture_width, picture_height,
                     color=(40, 40, 80), thickness=1, fill=True)

    if threshold_edit_mode == "rect":
        try:
            preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)
            preview_gray = preview_img.to_grayscale()
            preview_binary = preview_gray.binary([tuple(threshold_current[:2])])
            preview_small = preview_binary.to_rgb565()
            if preview_small.width() > 150:
                scale = preview_small.width() // 150 + 1
                preview_small.midpoint_pool(scale, scale)
            img.draw_image(preview_small, picture_width - preview_small.width() - 10, 10)
            img.draw_string_advanced(80, 10, 18, " 矩形阈值编辑", color=(255, 255, 0))
            img.draw_string_advanced(10, 35, 14, f"当前阈值: ({threshold_current[0]}, {threshold_current[1]})", color=(255, 255, 255))
        except Exception as e:
            img.draw_string_advanced(80, 10, 18, " 矩形阈值编辑", color=(255, 255, 0))
            img.draw_string_advanced(10, 35, 14, f"当前阈值: ({threshold_current[0]}, {threshold_current[1]})", color=(255, 255, 255))

    elif threshold_edit_mode == "laser":
        try:
            preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)
            current_lab_threshold = [tuple(threshold_current)]
            blobs = preview_img.find_blobs(current_lab_threshold, False,
                                         x_stride=1, y_stride=1,
                                         pixels_threshold=MIN_LASER_PIXELS, margin=False)
            for blob in blobs:
                preview_img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                                         color=(0, 255, 255), thickness=2, fill=False)
                preview_img.draw_circle(blob.x() + blob.w()//2, blob.y() + blob.h()//2, 3,
                                       color=(255, 0, 255), thickness=2)
            if preview_img.width() > 150:
                scale = preview_img.width() // 150 + 1
                preview_img.midpoint_pool(scale, scale)
            img.draw_image(preview_img, picture_width - preview_img.width() - 10, 10)
            img.draw_string_advanced(80, 10, 18, " 激光点阈值编辑", color=(255, 255, 0))
            img.draw_string_advanced(10, 35, 12, f"LAB: ({threshold_current[0]},...,{threshold_current[5]})", color=(255, 255, 255))
        except Exception as e:
            img.draw_string_advanced(80, 10, 18, " 激光点阈值编辑", color=(255, 255, 0))

    img.draw_string_advanced(140, 200, 14, "编辑模式: " + threshold_edit_mode.upper(), color=(0, 255, 255))
    rect_count = len(threshold_dict['rect']) if 'rect' in threshold_dict else 0
    laser_count = len(threshold_dict['laser']) if 'laser' in threshold_dict else 0
    img.draw_string_advanced(120, 180, 12, f"字典状态: 矩形{rect_count}个 激光点{laser_count}个", color=(0, 255, 0))
    img.draw_rectangle(2, 2, picture_width-4, picture_height-4,
                     color=(255, 255, 0), thickness=2, fill=False)

def draw_threshold_edit_buttons(img):
    button_color = (150, 150, 150)
    text_color = (0, 0, 0)
    active_color = (0, 255, 0)

    img.draw_rectangle(20, 20, 120, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(50, 30, 20, "返回", color=text_color)

    switch_color = active_color if threshold_edit_mode == "laser" else button_color
    img.draw_rectangle(660, 20, 120, 40, color=switch_color, thickness=2, fill=True)
    img.draw_string_advanced(690, 30, 20, "切换", color=text_color)

    img.draw_rectangle(20, 420, 100, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(45, 430, 18, "重置", color=text_color)

    img.draw_rectangle(140, 420, 100, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(165, 430, 18, "保存", color=text_color)

    img.draw_rectangle(540, 420, 120, 40, color=(100, 200, 100), thickness=2, fill=True)
    img.draw_string_advanced(555, 430, 16, "保存到字典", color=text_color)

    img.draw_rectangle(680, 420, 120, 40, color=(100, 100, 200), thickness=2, fill=True)
    img.draw_string_advanced(695, 430, 16, "从字典加载", color=text_color)

    if threshold_edit_mode == "rect":
        param_names = ["最小值", "最大值"]
        for i in range(2):
            y_pos = 100 + i * 80
            img.draw_rectangle(20, y_pos, 80, 40, color=button_color, thickness=2, fill=True)
            img.draw_string_advanced(45, y_pos + 10, 20, "-", color=text_color)
            img.draw_string_advanced(120, y_pos + 10, 16, f"{param_names[i]}: {threshold_current[i]}", color=(255, 255, 255))
            img.draw_rectangle(700, y_pos, 80, 40, color=button_color, thickness=2, fill=True)
            img.draw_string_advanced(725, y_pos + 10, 20, "+", color=text_color)
    elif threshold_edit_mode == "laser":
        param_names = ["L_min", "L_max", "A_min", "A_max", "B_min", "B_max"]
        for i in range(6):
            y_pos = 80 + i * 55
            img.draw_rectangle(20, y_pos, 60, 35, color=button_color, thickness=2, fill=True)
            img.draw_string_advanced(40, y_pos + 8, 16, "-", color=text_color)
            img.draw_string_advanced(100, y_pos + 8, 14, f"{param_names[i]}: {threshold_current[i]}", color=(255, 255, 255))
            img.draw_rectangle(720, y_pos, 60, 35, color=button_color, thickness=2, fill=True)
            img.draw_string_advanced(740, y_pos + 8, 16, "+", color=text_color)

def check_threshold_edit_click(x, y):
    global threshold_edit_mode, threshold_current, current_mode

    if 20 <= x <= 140 and 20 <= y <= 60:
        current_mode = 1
        return True

    if 660 <= x <= 780 and 20 <= y <= 60:
        if threshold_edit_mode == "rect":
            threshold_edit_mode = "laser"
            threshold_current = list(LASER_THRESHOLD[0])
        else:
            threshold_edit_mode = "rect"
            threshold_current = list(RECT_THRESHOLD[0]) + [0, 255, 0, 255]
        return True

    if 20 <= x <= 120 and 420 <= y <= 460:
        if threshold_edit_mode == "rect":
            threshold_current = [0, 80, 0, 255, 0, 255]
        else:
            threshold_current = [47, 80, 9, 91, -55, 63]
        return True

    if 140 <= x <= 240 and 420 <= y <= 460:
        if threshold_edit_mode == "rect":
            RECT_THRESHOLD[0] = tuple(threshold_current[:2])
        else:
            LASER_THRESHOLD[0] = tuple(threshold_current)
        return True

    if 540 <= x <= 660 and 420 <= y <= 460:
        save_threshold_to_dict()
        return True

    if 680 <= x <= 800 and 420 <= y <= 460:
        load_threshold_from_dict()
        return True

    if threshold_edit_mode == "rect":
        for i in range(2):
            y_pos = 100 + i * 80
            if 20 <= x <= 100 and y_pos <= y <= y_pos + 40:
                threshold_current[i] = max(0, threshold_current[i] - 1 )
                return True
            if 700 <= x <= 780 and y_pos <= y <= y_pos + 40:
                threshold_current[i] = min(255, threshold_current[i] + 1)
                return True

    elif threshold_edit_mode == "laser":
        for i in range(6):
            y_pos = 80 + i * 55
            if 20 <= x <= 80 and y_pos <= y <= y_pos + 35:
                if i < 2:
                    threshold_current[i] = max(0, threshold_current[i] - 1)
                else:
                    threshold_current[i] = max(-128, threshold_current[i] - 1)
                return True
            if 720 <= x <= 780 and y_pos <= y <= y_pos + 35:
                if i < 2:
                    threshold_current[i] = min(100, threshold_current[i] + 1)
                else:
                    threshold_current[i] = min(127, threshold_current[i] + 1)
                return True

    return False

# 绘制按键
def draw_buttons(img):
    button1_color = BUTTON_ACTIVE_COLOR if current_mode == 1 else BUTTON_INACTIVE_COLOR
    img.draw_rectangle(BUTTON1_POS[0], BUTTON1_POS[1], BUTTON_WIDTH, BUTTON_HEIGHT,
                      color=button1_color, thickness=3, fill=True)
    img.draw_string_advanced(BUTTON1_POS[0] + 30, BUTTON1_POS[1] + 20, 20,
                           "基础部分", color=BUTTON_TEXT_COLOR)

    button2_color = BUTTON_ACTIVE_COLOR if current_mode == 2 else BUTTON_INACTIVE_COLOR
    img.draw_rectangle(BUTTON2_POS[0], BUTTON2_POS[1], BUTTON_WIDTH, BUTTON_HEIGHT,
                      color=button2_color, thickness=3, fill=True)
    img.draw_string_advanced(BUTTON2_POS[0] + 30, BUTTON2_POS[1] + 20, 20,
                           "进阶部分", color=BUTTON_TEXT_COLOR)

    button3_color = BUTTON_ACTIVE_COLOR if current_mode == 3 else BUTTON_INACTIVE_COLOR
    img.draw_rectangle(BUTTON3_POS[0], BUTTON3_POS[1], BUTTON_WIDTH, BUTTON_HEIGHT,
                      color=button3_color, thickness=3, fill=True)
    img.draw_string_advanced(BUTTON3_POS[0] + 30, BUTTON3_POS[1] + 20, 20,
                           "阈值编辑", color=BUTTON_TEXT_COLOR)

    for pos in [BUTTON1_POS, BUTTON2_POS, BUTTON3_POS]:
        img.draw_rectangle(pos[0]-2, pos[1]-2, BUTTON_WIDTH+4, BUTTON_HEIGHT+4,
                         color=(255, 255, 255), thickness=2, fill=False)

def check_button_click(x, y):
    if (BUTTON1_POS[0] <= x <= BUTTON1_POS[0] + BUTTON_WIDTH and
        BUTTON1_POS[1] <= y <= BUTTON1_POS[1] + BUTTON_HEIGHT):
        return 1
    if (BUTTON2_POS[0] <= x <= BUTTON2_POS[0] + BUTTON_WIDTH and
        BUTTON2_POS[1] <= y <= BUTTON2_POS[1] + BUTTON_HEIGHT):
        return 2
    if (BUTTON3_POS[0] <= x <= BUTTON3_POS[0] + BUTTON_WIDTH and
        BUTTON3_POS[1] <= y <= BUTTON3_POS[1] + BUTTON_HEIGHT):
        return 3
    return None

# 主程序
try:
    print("第五步：串口通信功能测试开始")
    print("基于step4的成功配置，添加串口通信功能")

    # 初始化串口
    if not init_uart():
        print("⚠️  串口初始化失败，程序继续运行但无通信功能")

    # 传感器初始化
    sensor = Sensor(id=sensor_id)
    sensor.reset()
    sensor.set_framesize(width=picture_width, height=picture_height, chn=CAM_CHN_ID_0)
    sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)

    # 显示器初始化
    Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
    MediaManager.init()
    sensor.run()

    # 触摸屏初始化
    tp = TOUCH(0)
    clock = time.clock()

    current_mode = 1
    last_send_time = 0
    SEND_INTERVAL = 100

    print("系统初始化完成，开始交互式界面...")

    while True:
        clock.tick()
        os.exitpoint()

        try:
            img = sensor.snapshot(chn=CAM_CHN_ID_0)

            # 处理触摸输入
            points = tp.read()
            if len(points) > 0:
                touch_x, touch_y = points[0].x, points[0].y

                if current_mode == 3:
                    threshold_clicked = check_threshold_edit_click(touch_x, touch_y)
                    if threshold_clicked:
                        time.sleep_ms(300)
                else:
                    clicked_button = check_button_click(touch_x, touch_y)
                    if clicked_button:
                        old_mode = current_mode
                        current_mode = clicked_button
                        if current_mode == 3:
                            threshold_edit_mode = "rect"
                            threshold_current = list(RECT_THRESHOLD[0]) + [0, 255, 0, 255]
                        time.sleep_ms(300)

            # 主界面处理
            if current_mode == 1:
                img.draw_string_advanced(10, 10, 22, "K230激光定位系统", color=(255, 255, 0))
                img.draw_string_advanced(10, 32, 18, "基础部分：矩形+激光点检测", color=(0, 255, 255))
                img.draw_string_advanced(10, 52, 14, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))

                # 串口状态显示
                uart_status = "✅已连接" if uart else "❌未连接"
                img.draw_string_advanced(300, 10, 14, f"串口: {uart_status}", color=(0, 255, 0))

                # 检测矩形
                rect_success, rect_count, rect_centers, rect_total_center = detect_rectangles(img)

                # 定期发送坐标
                current_time = time.ticks_ms()
                if current_time - last_send_time >= SEND_INTERVAL:
                    send_coordinates(rect_total_center, (SCREEN_CENTER_X, SCREEN_CENTER_Y))
                    last_send_time = current_time

                # 显示检测结果
                if rect_success:
                    img.draw_string_advanced(10, 75, 14, f"矩形检测成功! 数量: {rect_count}", color=(0, 255, 0))

            elif current_mode == 2:
                img.draw_string_advanced(10, 10, 22, "K230激光定位系统", color=(255, 255, 0))
                img.draw_string_advanced(10, 32, 18, "进阶部分：功能开发中", color=(255, 165, 0))
                uart_status = "✅已连接" if uart else "❌未连接"
                img.draw_string_advanced(300, 10, 14, f"串口: {uart_status}", color=(0, 255, 0))
                img.draw_string_advanced(50, 100, 20, "此功能正在开发中", color=(255, 255, 0))

            elif current_mode == 3:
                show_threshold_edit_interface(img)

            # 图像放大和显示
            img = img.scale(SCALE_FACTOR, SCALE_FACTOR)
            if current_mode == 3:
                draw_threshold_edit_buttons(img)
            else:
                draw_buttons(img)
            Display.show_image(img, x=0, y=0)

        except Exception as e:
            print(f"主循环错误: {e}")
            time.sleep_ms(100)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    print("清理资源...")
    if isinstance(sensor, Sensor):
        sensor.stop()
    if uart:
        uart.deinit()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
    print("第五步测试完成")
