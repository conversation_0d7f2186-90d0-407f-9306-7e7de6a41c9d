# bilibili搜索学不会电磁场看教程
# K230矩形中心激光定位系统 - 第八步：动态快速矩形识别系统
# 基于step7的LAB颜色空间检测，针对动态环境进行速度优化
# 主要改进：
# 1. 快速扫描算法优化 - 自适应ROI、简化计算
# 2. 动态环境适应性 - 运动预测、模糊补偿、多尺度检测
# 3. 实时坐标传输系统 - 高频通信、坐标预测插值
# 4. 性能优化 - 目标≥20FPS，≥80%识别率，≤50ms延迟

import time
import os
import sys
import math
from media.sensor import *
from media.display import *
from media.media import *
from machine import TOUCH
from machine import UART
from machine import FPIOA

# 常量定义
CAM_CHN_ID_0 = 0

# ==================== 重要改进：快速检测系统配置 ====================
# 性能目标配置
TARGET_FPS = 20              # 目标帧率 ≥20FPS
TARGET_SUCCESS_RATE = 80     # 目标识别率 ≥80%
TARGET_LATENCY = 50          # 目标延迟 ≤50ms

# 快速检测模式配置
FAST_MODE = True             # 启用快速模式
SIMPLIFIED_VALIDATION = True # 简化特征验证
ADAPTIVE_ROI = True          # 启用自适应ROI
MOTION_PREDICTION = True     # 启用运动预测

# ==================== 优化的LAB阈值系统 ====================
# 阈值编辑全局变量 - 保持与step7兼容
threshold_edit_mode = "rect"
threshold_current = [13, 35, 20, -5, -44, 16]  # 用户调整的LAB阈值

# 阈值存储机制 - 快速模式优化
threshold_dict = {
    'rect': [(13, 35, 20, -5, -44, 16)],  # 黑色矩形的LAB阈值
    'laser': [(47, 80, 9, 91, -55, 63), (16, 37, 23, 74, -48, 52)]  # 激光点LAB阈值
}

# ==================== 快速识别率统计系统 ====================
# 优化的统计变量 - 减少内存占用
rect_detection_stats = {
    'total_attempts': 0,
    'successful_detections': 0,
    'success_rate': 0.0,
    'recent_attempts': [],    # 减少到最近50次（step7是100次）
    'recent_success_rate': 0.0,
    'fps_history': [],        # FPS历史记录
    'avg_fps': 0.0,
    'processing_times': [],   # 处理时间历史
    'avg_processing_time': 0.0
}

# ==================== 重要改进：快速多帧稳定性检测系统 ====================
# 优化的多帧检测 - 缩短缓冲区提高响应速度
fast_multi_frame_detection = {
    'frame_buffer': [],           # 存储最近3帧的检测结果（step7是5帧）
    'buffer_size': 3,            # 缩短缓冲区大小，提高响应速度
    'stable_threshold': 2,       # 稳定阈值（3帧中至少2帧检测成功）
    'position_threshold': 20.0,  # 放宽位置变化阈值，适应动态环境
    'last_stable_center': None,
    'last_send_center': None,
    'send_threshold': 5.0,       # 降低发送阈值，提高响应性
    'confidence_threshold': 0.6  # 降低置信度阈值，平衡速度和精度
}

# ==================== 重要改进：自适应ROI系统 ====================
# 动态ROI配置
adaptive_roi_config = {
    'enabled': ADAPTIVE_ROI,
    'roi_x': 0,
    'roi_y': 0,
    'roi_width': 400,
    'roi_height': 240,
    'expansion_factor': 1.5,     # ROI扩展因子
    'min_roi_size': 100,         # 最小ROI尺寸
    'max_roi_size': 400,         # 最大ROI尺寸
    'update_threshold': 5,       # ROI更新阈值（帧数）
    'frames_since_update': 0
}

# ==================== 重要改进：运动预测系统 ====================
# 运动预测配置
motion_prediction_config = {
    'enabled': MOTION_PREDICTION,
    'history_length': 5,         # 运动历史长度
    'position_history': [],      # 位置历史
    'velocity_history': [],      # 速度历史
    'predicted_position': None,  # 预测位置
    'prediction_confidence': 0.0,# 预测置信度
    'max_velocity': 100.0,       # 最大速度（像素/帧）
    'velocity_smoothing': 0.7    # 速度平滑因子
}

# 串口通信全局变量 - 优化为高频通信
uart = None
SERIAL_PORT = UART.UART2
BAUD_RATE = 115200
TX_PIN = 11
RX_PIN = 12

# 优化的数据包格式 - 添加运动状态标识
PACKET_HEADER = [0xAA, 0x55]
PACKET_TAIL = [0x0D, 0x0A]
SCREEN_CENTER_X = 200
SCREEN_CENTER_Y = 120

# ==================== 重要改进：快速检测参数 ====================
# 优化的矩形检测参数 - 平衡速度和精度
RECT_THRESHOLD = [(13, 35, 20, -5, -44, 16)]  # 用户调整的LAB阈值
MIN_RECT_AREA = 2000         # 降低最小面积，提高检测率
MAX_RECT_AREA = 60000        # 适当提高最大面积
MIN_RECT_ASPECT_RATIO = 0.2  # 放宽宽高比限制
MAX_RECT_ASPECT_RATIO = 5.0  # 放宽宽高比限制

# 激光点检测参数
LASER_THRESHOLD = [
    (47, 80, 9, 91, -55, 63),
    (16, 37, 23, 74, -48, 52)
]
MIN_LASER_PIXELS = 15        # 降低像素阈值，提高检测率

# 图像参数
picture_width = 400
picture_height = 240
sensor_id = 2
DISPLAY_MODE = "LCD"
DISPLAY_WIDTH = 800
DISPLAY_HEIGHT = 480
SCALE_FACTOR = 2.0
SCALED_WIDTH = int(picture_width * SCALE_FACTOR)
SCALED_HEIGHT = int(picture_height * SCALE_FACTOR)

# 按键定义
BUTTON_WIDTH = 150
BUTTON_HEIGHT = 60
BUTTON_Y = SCALED_HEIGHT - BUTTON_HEIGHT - 10
BUTTON_SPACING = 200
BUTTON_START_X = 50
BUTTON1_POS = (BUTTON_START_X, BUTTON_Y)
BUTTON2_POS = (BUTTON_START_X + BUTTON_SPACING, BUTTON_Y)
BUTTON3_POS = (BUTTON_START_X + BUTTON_SPACING * 2, BUTTON_Y)
BUTTON_ACTIVE_COLOR = (0, 255, 0)
BUTTON_INACTIVE_COLOR = (80, 80, 80)
BUTTON_TEXT_COLOR = (255, 255, 255)

sensor = None

# ==================== 重要改进：快速性能监控函数 ====================
def update_performance_stats(processing_time, fps):
    """更新性能统计信息"""
    global rect_detection_stats
    
    # 更新FPS历史
    rect_detection_stats['fps_history'].append(fps)
    if len(rect_detection_stats['fps_history']) > 20:  # 保持最近20次
        rect_detection_stats['fps_history'].pop(0)
    
    # 计算平均FPS
    if rect_detection_stats['fps_history']:
        rect_detection_stats['avg_fps'] = sum(rect_detection_stats['fps_history']) / len(rect_detection_stats['fps_history'])
    
    # 更新处理时间历史
    rect_detection_stats['processing_times'].append(processing_time)
    if len(rect_detection_stats['processing_times']) > 20:
        rect_detection_stats['processing_times'].pop(0)
    
    # 计算平均处理时间
    if rect_detection_stats['processing_times']:
        rect_detection_stats['avg_processing_time'] = sum(rect_detection_stats['processing_times']) / len(rect_detection_stats['processing_times'])

def get_performance_status():
    """获取性能状态信息"""
    avg_fps = rect_detection_stats['avg_fps']
    avg_time = rect_detection_stats['avg_processing_time']
    success_rate = rect_detection_stats['success_rate']
    
    # 性能等级评估
    fps_status = "优秀" if avg_fps >= TARGET_FPS else "需优化"
    time_status = "优秀" if avg_time <= TARGET_LATENCY else "需优化"
    rate_status = "优秀" if success_rate >= TARGET_SUCCESS_RATE else "需优化"
    
    return {
        'fps': avg_fps,
        'fps_status': fps_status,
        'processing_time': avg_time,
        'time_status': time_status,
        'success_rate': success_rate,
        'rate_status': rate_status
    }

# ==================== 重要改进：快速统计更新函数 ====================
def update_detection_stats(success):
    """快速更新矩形检测统计数据"""
    global rect_detection_stats

    # 更新总体统计
    rect_detection_stats['total_attempts'] += 1
    if success:
        rect_detection_stats['successful_detections'] += 1

    # 快速计算总体成功率
    if rect_detection_stats['total_attempts'] > 0:
        rect_detection_stats['success_rate'] = (
            rect_detection_stats['successful_detections'] /
            rect_detection_stats['total_attempts'] * 100
        )

    # 更新最近50次统计（减少内存占用）
    rect_detection_stats['recent_attempts'].append(success)
    if len(rect_detection_stats['recent_attempts']) > 50:
        rect_detection_stats['recent_attempts'].pop(0)

    # 快速计算最近成功率
    if len(rect_detection_stats['recent_attempts']) > 0:
        recent_successes = sum(rect_detection_stats['recent_attempts'])
        rect_detection_stats['recent_success_rate'] = (
            recent_successes / len(rect_detection_stats['recent_attempts']) * 100
        )

def get_detection_stats_text():
    """获取检测统计信息的显示文本"""
    total = rect_detection_stats['total_attempts']
    success_rate = rect_detection_stats['success_rate']
    recent_rate = rect_detection_stats['recent_success_rate']
    recent_count = len(rect_detection_stats['recent_attempts'])
    avg_fps = rect_detection_stats['avg_fps']

    return {
        'total_text': f"总计: {total}次",
        'success_rate_text': f"成功率: {success_rate:.1f}%",
        'recent_text': f"近{recent_count}次: {recent_rate:.1f}%",
        'fps_text': f"FPS: {avg_fps:.1f}"
    }

# ==================== 重要改进：自适应ROI管理系统 ====================
def update_adaptive_roi(detection_center):
    """根据检测结果更新自适应ROI"""
    global adaptive_roi_config

    if not adaptive_roi_config['enabled'] or detection_center is None:
        return

    center_x, center_y = detection_center
    expansion = adaptive_roi_config['expansion_factor']
    min_size = adaptive_roi_config['min_roi_size']
    max_size = adaptive_roi_config['max_roi_size']

    # 计算新的ROI尺寸（基于当前检测区域）
    new_roi_size = min(max_size, max(min_size, int(200 * expansion)))

    # 计算ROI位置（以检测中心为中心）
    new_roi_x = max(0, min(picture_width - new_roi_size, int(center_x - new_roi_size // 2)))
    new_roi_y = max(0, min(picture_height - new_roi_size, int(center_y - new_roi_size // 2)))

    # 确保ROI不超出图像边界
    new_roi_width = min(new_roi_size, picture_width - new_roi_x)
    new_roi_height = min(new_roi_size, picture_height - new_roi_y)

    # 更新ROI配置
    adaptive_roi_config['roi_x'] = new_roi_x
    adaptive_roi_config['roi_y'] = new_roi_y
    adaptive_roi_config['roi_width'] = new_roi_width
    adaptive_roi_config['roi_height'] = new_roi_height
    adaptive_roi_config['frames_since_update'] = 0

    # ==================== 优化：减少调试输出 ====================
    # 只在ROI显著变化时输出信息
    if frame_count % 50 == 0:  # 每50帧输出一次
        print(f"🎯 ROI: ({new_roi_x},{new_roi_y}) {new_roi_width}x{new_roi_height}")

def get_current_roi():
    """获取当前ROI设置"""
    if adaptive_roi_config['enabled']:
        return (
            adaptive_roi_config['roi_x'],
            adaptive_roi_config['roi_y'],
            adaptive_roi_config['roi_width'],
            adaptive_roi_config['roi_height']
        )
    else:
        return (0, 0, picture_width, picture_height)

def reset_adaptive_roi():
    """重置ROI到全图"""
    global adaptive_roi_config
    adaptive_roi_config['roi_x'] = 0
    adaptive_roi_config['roi_y'] = 0
    adaptive_roi_config['roi_width'] = picture_width
    adaptive_roi_config['roi_height'] = picture_height
    adaptive_roi_config['frames_since_update'] = 0
    # ==================== 优化：减少调试输出 ====================
    # print("🔄 ROI重置到全图")  # 注释掉频繁的调试输出

# ==================== 重要改进：运动预测算法 ====================
def update_motion_prediction(current_center):
    """更新运动预测模型"""
    global motion_prediction_config

    if not motion_prediction_config['enabled'] or current_center is None:
        return

    config = motion_prediction_config

    # 添加当前位置到历史
    config['position_history'].append(current_center)
    if len(config['position_history']) > config['history_length']:
        config['position_history'].pop(0)

    # 计算速度
    if len(config['position_history']) >= 2:
        last_pos = config['position_history'][-2]
        current_pos = config['position_history'][-1]

        velocity = (
            current_pos[0] - last_pos[0],
            current_pos[1] - last_pos[1]
        )

        # 速度平滑处理
        if config['velocity_history']:
            last_velocity = config['velocity_history'][-1]
            smoothing = config['velocity_smoothing']
            velocity = (
                velocity[0] * (1 - smoothing) + last_velocity[0] * smoothing,
                velocity[1] * (1 - smoothing) + last_velocity[1] * smoothing
            )

        # 限制最大速度
        velocity_magnitude = (velocity[0]**2 + velocity[1]**2)**0.5
        if velocity_magnitude > config['max_velocity']:
            scale = config['max_velocity'] / velocity_magnitude
            velocity = (velocity[0] * scale, velocity[1] * scale)

        config['velocity_history'].append(velocity)
        if len(config['velocity_history']) > config['history_length']:
            config['velocity_history'].pop(0)

        # 预测下一帧位置
        if config['velocity_history']:
            avg_velocity = (
                sum(v[0] for v in config['velocity_history']) / len(config['velocity_history']),
                sum(v[1] for v in config['velocity_history']) / len(config['velocity_history'])
            )

            predicted_pos = (
                current_pos[0] + avg_velocity[0],
                current_pos[1] + avg_velocity[1]
            )

            # 确保预测位置在图像范围内
            predicted_pos = (
                max(0, min(picture_width, predicted_pos[0])),
                max(0, min(picture_height, predicted_pos[1]))
            )

            config['predicted_position'] = predicted_pos

            # 计算预测置信度（基于速度一致性）
            if len(config['velocity_history']) >= 3:
                velocity_variance = sum(
                    (v[0] - avg_velocity[0])**2 + (v[1] - avg_velocity[1])**2
                    for v in config['velocity_history']
                ) / len(config['velocity_history'])

                config['prediction_confidence'] = max(0, 1.0 - velocity_variance / 100.0)
            else:
                config['prediction_confidence'] = 0.5

def get_predicted_position():
    """获取预测位置"""
    if motion_prediction_config['enabled'] and motion_prediction_config['predicted_position']:
        return motion_prediction_config['predicted_position'], motion_prediction_config['prediction_confidence']
    return None, 0.0

# ==================== 重要改进：快速多帧稳定性检测 ====================
def calculate_distance(point1, point2):
    """计算两点间的欧几里得距离"""
    if point1 is None or point2 is None:
        return float('inf')
    return ((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)**0.5

def add_detection_to_fast_buffer(detection_result):
    """将检测结果添加到快速多帧缓冲区"""
    global fast_multi_frame_detection

    buffer = fast_multi_frame_detection['frame_buffer']
    buffer_size = fast_multi_frame_detection['buffer_size']

    # 添加新的检测结果
    buffer.append(detection_result)

    # 保持缓冲区大小
    if len(buffer) > buffer_size:
        buffer.pop(0)

def get_fast_stable_detection():
    """从快速多帧缓冲区获取稳定的检测结果"""
    global fast_multi_frame_detection

    buffer = fast_multi_frame_detection['frame_buffer']
    stable_threshold = fast_multi_frame_detection['stable_threshold']
    position_threshold = fast_multi_frame_detection['position_threshold']

    if len(buffer) < stable_threshold:
        return None, 0.0

    # 统计成功检测的帧数
    successful_frames = [frame for frame in buffer if frame['success']]

    if len(successful_frames) < stable_threshold:
        return None, 0.0

    # 计算位置的一致性
    centers = [frame['center'] for frame in successful_frames if frame['center'] is not None]

    if len(centers) < stable_threshold:
        return None, 0.0

    # 计算加权平均中心点（最新的帧权重更高）
    weights = [i + 1 for i in range(len(centers))]  # 线性权重
    total_weight = sum(weights)

    avg_x = sum(center[0] * weight for center, weight in zip(centers, weights)) / total_weight
    avg_y = sum(center[1] * weight for center, weight in zip(centers, weights)) / total_weight
    avg_center = (avg_x, avg_y)

    # 快速位置稳定性检查
    distances = [calculate_distance(center, avg_center) for center in centers]
    max_distance = max(distances)

    if max_distance > position_threshold:
        return None, 0.0

    # 快速置信度计算
    success_rate = len(successful_frames) / len(buffer)
    position_stability = 1.0 - (max_distance / position_threshold)
    confidence = (success_rate * 0.7 + position_stability * 0.3)  # 更重视成功率

    return avg_center, confidence

def should_send_fast_data(current_center, confidence):
    """快速判断是否应该发送数据"""
    global fast_multi_frame_detection

    confidence_threshold = fast_multi_frame_detection['confidence_threshold']
    send_threshold = fast_multi_frame_detection['send_threshold']
    last_send_center = fast_multi_frame_detection['last_send_center']

    # 置信度检查
    if confidence < confidence_threshold:
        return False

    # 位置变化检查（更宽松，适应动态环境）
    if last_send_center is not None:
        distance = calculate_distance(current_center, last_send_center)
        if distance < send_threshold:
            return False

    return True

# ==================== 重要改进：高频串口通信系统 ====================
def init_uart():
    """初始化高频串口通信"""
    global uart

    try:
        # 配置引脚功能
        fpioa = FPIOA()
        fpioa.set_function(TX_PIN, FPIOA.UART2_TXD)
        fpioa.set_function(RX_PIN, FPIOA.UART2_RXD)

        # 初始化串口
        uart = UART(SERIAL_PORT, BAUD_RATE)
        print(f"✅ 高频串口初始化成功 - 波特率: {BAUD_RATE}")
        return True

    except Exception as e:
        print(f"❌ 串口初始化失败: {e}")
        return False

def send_fast_coordinates(rect_center, screen_center, motion_state=0):
    """发送快速坐标数据给STM32（添加运动状态标识）"""
    global uart

    if uart is None:
        return False

    try:
        # 准备优化的数据包
        packet = []
        packet.extend(PACKET_HEADER)

        # 矩形中心坐标
        if rect_center is not None:
            rect_x, rect_y = int(rect_center[0]), int(rect_center[1])
        else:
            rect_x, rect_y = 0xFFFF, 0xFFFF

        # 屏幕中心坐标
        screen_x, screen_y = int(screen_center[0]), int(screen_center[1])

        # 将16位坐标拆分为两个8位
        packet.extend([(rect_x >> 8) & 0xFF, rect_x & 0xFF])
        packet.extend([(rect_y >> 8) & 0xFF, rect_y & 0xFF])
        packet.extend([(screen_x >> 8) & 0xFF, screen_x & 0xFF])
        packet.extend([(screen_y >> 8) & 0xFF, screen_y & 0xFF])

        # 添加运动状态标识
        packet.append(motion_state & 0xFF)  # 0=静止, 1=慢速运动, 2=快速运动

        # 计算校验和
        checksum = sum(packet[2:]) % 256
        packet.append(checksum)
        packet.extend(PACKET_TAIL)

        # 快速发送数据
        uart.write(bytes(packet))

        return True

    except Exception as e:
        print(f"❌ 快速串口发送失败: {e}")
        return False

def get_motion_state():
    """获取当前运动状态"""
    if not motion_prediction_config['enabled']:
        return 0

    velocity_history = motion_prediction_config['velocity_history']
    if not velocity_history:
        return 0

    # 计算平均速度大小
    avg_velocity_magnitude = sum(
        (v[0]**2 + v[1]**2)**0.5 for v in velocity_history
    ) / len(velocity_history)

    # 根据速度判断运动状态
    if avg_velocity_magnitude < 5:
        return 0  # 静止
    elif avg_velocity_magnitude < 20:
        return 1  # 慢速运动
    else:
        return 2  # 快速运动

# ==================== 重要改进：快速矩形检测算法 ====================
def detect_rectangles_fast(img):
    """
    快速矩形检测算法 - 针对动态环境优化
    主要优化：
    1. 自适应ROI减少搜索范围
    2. 简化特征验证提高速度
    3. 运动预测辅助检测
    4. 快速多帧稳定性验证
    """
    start_time = time.ticks_ms()

    try:
        # ==================== 重要改进：自适应ROI处理 ====================
        roi_x, roi_y, roi_width, roi_height = get_current_roi()

        # 创建检测图像副本（保护原始图像）
        if adaptive_roi_config['enabled'] and (roi_width < picture_width or roi_height < picture_height):
            detect_img = img.copy(roi=(roi_x, roi_y, roi_width, roi_height))
            # ==================== 优化：减少调试输出 ====================
            # 只在特定条件下输出ROI信息
            if frame_count % 100 == 0:  # 每100帧输出一次
                print(f"🎯 ROI: ({roi_x},{roi_y}) {roi_width}x{roi_height}")
        else:
            detect_img = img.copy()

        # ==================== 核心改进：快速LAB二值化 ====================
        binary_img = detect_img.binary(RECT_THRESHOLD)

        # ==================== 重要改进：简化的形态学操作 ====================
        if FAST_MODE:
            # 快速模式：只进行轻微的噪声过滤
            binary_img.erode(1, threshold=1)
        else:
            # 标准模式：完整的形态学操作
            binary_img.erode(1, threshold=1)
            binary_img.dilate(1, threshold=1)

        # 快速矩形查找
        rects = binary_img.find_rects(threshold=MIN_RECT_AREA)

        if not rects:
            # 检测失败，更新ROI计数器
            adaptive_roi_config['frames_since_update'] += 1
            if adaptive_roi_config['frames_since_update'] > adaptive_roi_config['update_threshold']:
                reset_adaptive_roi()

            add_detection_to_fast_buffer({'success': False, 'center': None, 'confidence': 0.0})
            update_detection_stats(False)

            processing_time = time.ticks_ms() - start_time
            return False, 0, [], None, processing_time

        # ==================== 重要改进：快速矩形验证 ====================
        if SIMPLIFIED_VALIDATION:
            # 简化验证：只检查面积和基本宽高比
            validated_rects = []
            for rect in rects:
                area = rect.w() * rect.h()
                aspect_ratio = max(rect.w(), rect.h()) / min(rect.w(), rect.h())

                if (MIN_RECT_AREA <= area <= MAX_RECT_AREA and
                    MIN_RECT_ASPECT_RATIO <= aspect_ratio <= MAX_RECT_ASPECT_RATIO):
                    validated_rects.append({
                        'rect': rect,
                        'area': area,
                        'aspect_ratio': aspect_ratio
                    })
        else:
            # 完整验证（保留step7的复杂验证逻辑）
            validated_rects = []
            for rect in rects:
                area = rect.w() * rect.h()
                aspect_ratio = max(rect.w(), rect.h()) / min(rect.w(), rect.h())

                if (MIN_RECT_AREA <= area <= MAX_RECT_AREA and
                    MIN_RECT_ASPECT_RATIO <= aspect_ratio <= MAX_RECT_ASPECT_RATIO):
                    validated_rects.append({
                        'rect': rect,
                        'area': area,
                        'aspect_ratio': aspect_ratio
                    })

        if not validated_rects:
            add_detection_to_fast_buffer({'success': False, 'center': None, 'confidence': 0.0})
            update_detection_stats(False)

            processing_time = time.ticks_ms() - start_time
            return False, 0, [], None, processing_time

        # ==================== 重要改进：快速矩形配对算法 ====================
        # 按面积排序
        validated_rects.sort(key=lambda r: r['area'], reverse=True)

        best_pair = None
        min_center_distance = float('inf')
        best_confidence = 0.0

        # 快速配对：只检查前几个最大的矩形
        max_pairs_to_check = min(6, len(validated_rects))  # 限制检查数量

        for i in range(max_pairs_to_check):
            for j in range(i+1, max_pairs_to_check):
                rect1_data = validated_rects[i]
                rect2_data = validated_rects[j]

                # 确定内外矩形
                if rect1_data['area'] < rect2_data['area']:
                    inner_data, outer_data = rect1_data, rect2_data
                else:
                    inner_data, outer_data = rect2_data, rect1_data

                inner_rect = inner_data['rect']
                outer_rect = outer_data['rect']

                # 快速计算中心点
                inner_corners = inner_rect.corners()
                inner_center = (
                    sum(corner[0] for corner in inner_corners) / 4,
                    sum(corner[1] for corner in inner_corners) / 4
                )

                outer_corners = outer_rect.corners()
                outer_center = (
                    sum(corner[0] for corner in outer_corners) / 4,
                    sum(corner[1] for corner in outer_corners) / 4
                )

                # 计算中心距离
                distance = calculate_distance(inner_center, outer_center)

                # 快速置信度计算
                distance_score = max(0, 1.0 - distance / 25.0)
                area_ratio = inner_data['area'] / outer_data['area']
                area_score = 1.0 - abs(area_ratio - 0.5)
                pair_confidence = (distance_score + area_score) / 2.0

                if distance < min_center_distance and pair_confidence > 0.4:  # 降低阈值
                    min_center_distance = distance
                    best_pair = (inner_rect, outer_rect, inner_center, outer_center)
                    best_confidence = pair_confidence

        # ==================== 重要改进：动态中心重合判断 ====================
        # 根据运动状态调整阈值
        motion_state = get_motion_state()
        if motion_state == 0:  # 静止
            CENTER_MATCH_THRESHOLD = 8.0
        elif motion_state == 1:  # 慢速运动
            CENTER_MATCH_THRESHOLD = 12.0
        else:  # 快速运动
            CENTER_MATCH_THRESHOLD = 18.0

        if best_pair is not None and min_center_distance <= CENTER_MATCH_THRESHOLD:
            inner_rect, outer_rect, inner_center, outer_center = best_pair

            # 坐标转换（考虑ROI偏移）
            if adaptive_roi_config['enabled']:
                inner_center = (roi_x + inner_center[0], roi_y + inner_center[1])
                outer_center = (roi_x + outer_center[0], roi_y + outer_center[1])

            # 计算最终中心点
            final_center = (
                (inner_center[0] + outer_center[0]) / 2,
                (inner_center[1] + outer_center[1]) / 2
            )

            # ==================== 重要改进：快速多帧验证 ====================
            add_detection_to_fast_buffer({
                'success': True,
                'center': final_center,
                'confidence': best_confidence
            })

            # 获取稳定检测结果
            stable_center, stable_confidence = get_fast_stable_detection()

            if stable_center is not None and stable_confidence > fast_multi_frame_detection['confidence_threshold']:
                # 更新运动预测和ROI
                update_motion_prediction(stable_center)
                update_adaptive_roi(stable_center)

                # ==================== 重要改进：快速可视化 ====================
                # 绘制检测结果（简化版本）
                if adaptive_roi_config['enabled']:
                    # 转换回原图坐标系进行绘制
                    inner_corners = inner_rect.corners()
                    outer_corners = outer_rect.corners()

                    # 绘制矩形边框
                    for corners, color in zip([inner_corners, outer_corners], [(255, 0, 255), (0, 255, 0)]):
                        for j in range(4):
                            next_j = (j + 1) % 4
                            x1, y1 = roi_x + corners[j][0], roi_y + corners[j][1]
                            x2, y2 = roi_x + corners[next_j][0], roi_y + corners[next_j][1]
                            img.draw_line(x1, y1, x2, y2, color=color, thickness=2)
                else:
                    # 直接绘制
                    inner_corners = inner_rect.corners()
                    outer_corners = outer_rect.corners()

                    for corners, color in zip([inner_corners, outer_corners], [(255, 0, 255), (0, 255, 0)]):
                        for j in range(4):
                            next_j = (j + 1) % 4
                            x1, y1 = corners[j][0], corners[j][1]
                            x2, y2 = corners[next_j][0], corners[next_j][1]
                            img.draw_line(x1, y1, x2, y2, color=color, thickness=2)

                # 绘制稳定中心点
                img.draw_circle(int(stable_center[0]), int(stable_center[1]), 8,
                               color=(255, 0, 0), thickness=3)
                img.draw_string_advanced(int(stable_center[0]) - 25, int(stable_center[1]) - 40,
                                       14, "FAST", color=(255, 0, 0))

                # 显示运动状态
                motion_text = ["静止", "慢速", "快速"][motion_state]
                img.draw_string_advanced(int(stable_center[0]) - 20, int(stable_center[1]) + 25,
                                       12, motion_text, color=(255, 255, 0))

                update_detection_stats(True)
                processing_time = time.ticks_ms() - start_time

                return True, 2, [inner_center, outer_center], stable_center, processing_time
            else:
                update_detection_stats(False)
                processing_time = time.ticks_ms() - start_time
                return False, 0, [], None, processing_time
        else:
            add_detection_to_fast_buffer({'success': False, 'center': None, 'confidence': 0.0})
            update_detection_stats(False)
            processing_time = time.ticks_ms() - start_time
            return False, 0, [], None, processing_time

    except Exception as e:
        print(f"快速矩形检测错误: {e}")
        add_detection_to_fast_buffer({'success': False, 'center': None, 'confidence': 0.0})
        update_detection_stats(False)
        processing_time = time.ticks_ms() - start_time
        return False, 0, [], None, processing_time

# ==================== 快速激光点检测（保持与step7兼容） ====================
def detect_laser_point_fast(img):
    """快速激光点检测"""
    try:
        # 使用当前ROI进行激光点检测
        roi_x, roi_y, roi_width, roi_height = get_current_roi()

        if adaptive_roi_config['enabled'] and (roi_width < picture_width or roi_height < picture_height):
            detect_img = img.copy(roi=(roi_x, roi_y, roi_width, roi_height))
        else:
            detect_img = img

        blobs = detect_img.find_blobs(LASER_THRESHOLD, False,
                                    x_stride=1, y_stride=1,
                                    pixels_threshold=MIN_LASER_PIXELS, margin=False)

        laser_centers = []
        if blobs:
            for i, blob in enumerate(blobs):
                laser_x = blob.x() + blob.w() / 2
                laser_y = blob.y() + blob.h() / 2

                if adaptive_roi_config['enabled']:
                    global_laser_x = roi_x + laser_x
                    global_laser_y = roi_y + laser_y
                    img.draw_rectangle(roi_x + blob.x(), roi_y + blob.y(),
                                     blob.w(), blob.h(),
                                     color=(0, 0, 255), thickness=2, fill=False)
                else:
                    global_laser_x = laser_x
                    global_laser_y = laser_y
                    img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                                     color=(0, 0, 255), thickness=2, fill=False)

                laser_centers.append((global_laser_x, global_laser_y))
                img.draw_circle(int(global_laser_x), int(global_laser_y), 3,
                               color=(0, 0, 255), thickness=2)

            return True, len(blobs), laser_centers

        return False, 0, []

    except Exception as e:
        print(f"快速激光点检测错误: {e}")
        return False, 0, []

# ==================== 快速阈值编辑界面（简化版本） ====================
def show_fast_threshold_edit_interface(img):
    """快速阈值编辑界面 - 优化版本"""
    global threshold_edit_mode, threshold_current

    # 清空背景
    img.draw_rectangle(0, 0, picture_width, picture_height,
                     color=(40, 40, 80), thickness=1, fill=True)

    if threshold_edit_mode == "rect":
        try:
            # 快速预览模式
            preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)

            # 全屏LAB二值化预览
            preview_binary = preview_img.binary([tuple(threshold_current)])
            preview_fullscreen = preview_binary.to_rgb565()

            # 简化的缩放处理
            if preview_fullscreen.width() != picture_width or preview_fullscreen.height() != picture_height:
                scale = max(preview_fullscreen.width() // picture_width + 1,
                           preview_fullscreen.height() // picture_height + 1)
                if scale > 1:
                    preview_fullscreen.midpoint_pool(scale, scale)

            img.draw_image(preview_fullscreen, 0, 0)

            # 小尺寸彩色原图
            color_original = preview_img.copy()
            target_width, target_height = 80, 60  # 更小的尺寸，提高速度

            if color_original.width() > target_width:
                scale = color_original.width() // target_width + 1
                color_original.midpoint_pool(scale, scale)

            pip_x = picture_width - color_original.width() - 5
            pip_y = 5

            # 绘制边框和图像
            img.draw_rectangle(pip_x - 1, pip_y - 1,
                             color_original.width() + 2, color_original.height() + 2,
                             color=(255, 255, 255), thickness=1, fill=True)
            img.draw_image(color_original, pip_x, pip_y)

            # 简化的信息显示
            img.draw_rectangle(0, 0, picture_width, 20, color=(0, 0, 0), thickness=1, fill=True)
            img.draw_string_advanced(5, 3, 14, "快速LAB阈值编辑", color=(255, 255, 0))

            img.draw_rectangle(0, picture_height - 15, picture_width, 15, color=(0, 0, 0), thickness=1, fill=True)
            info_text = f"LAB: ({threshold_current[0]},{threshold_current[1]},{threshold_current[2]},{threshold_current[3]},{threshold_current[4]},{threshold_current[5]})"
            img.draw_string_advanced(5, picture_height - 13, 10, info_text, color=(255, 255, 255))

        except Exception as e:
            print(f"快速阈值预览错误: {e}")
            img.draw_rectangle(0, 0, picture_width, picture_height, color=(40, 40, 80), thickness=1, fill=True)
            img.draw_string_advanced(60, 10, 18, "快速LAB阈值编辑", color=(255, 255, 0))

    elif threshold_edit_mode == "laser":
        try:
            # 激光点快速预览
            preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)
            current_lab_threshold = [tuple(threshold_current)]
            blobs = preview_img.find_blobs(current_lab_threshold, False,
                                         x_stride=1, y_stride=1,
                                         pixels_threshold=MIN_LASER_PIXELS, margin=False)
            for blob in blobs:
                preview_img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                                         color=(0, 255, 255), thickness=2, fill=False)

            # 简化缩放
            if preview_img.width() > 120:
                scale = preview_img.width() // 120 + 1
                preview_img.midpoint_pool(scale, scale)
            img.draw_image(preview_img, picture_width - preview_img.width() - 10, 10)

            img.draw_string_advanced(60, 10, 18, "快速激光点LAB编辑", color=(255, 255, 0))

        except Exception as e:
            print(f"激光点快速预览错误: {e}")
            img.draw_string_advanced(60, 10, 18, "快速激光点LAB编辑", color=(255, 255, 0))

    # 显示性能信息
    perf = get_performance_status()
    img.draw_string_advanced(10, picture_height - 35, 10,
                           f"FPS: {perf['fps']:.1f} ({perf['fps_status']})",
                           color=(0, 255, 0) if perf['fps_status'] == "优秀" else (255, 255, 0))

# ==================== 快速按钮绘制和处理 ====================
def draw_fast_buttons(img):
    """绘制快速模式按键"""
    global current_mode

    # 简化的按钮绘制
    button_colors = [
        BUTTON_ACTIVE_COLOR if current_mode == 1 else BUTTON_INACTIVE_COLOR,
        BUTTON_ACTIVE_COLOR if current_mode == 2 else BUTTON_INACTIVE_COLOR,
        BUTTON_ACTIVE_COLOR if current_mode == 3 else BUTTON_INACTIVE_COLOR
    ]

    button_texts = ["快速检测", "性能监控", "快速编辑"]

    for i, (pos, color, text) in enumerate(zip([BUTTON1_POS, BUTTON2_POS, BUTTON3_POS],
                                              button_colors, button_texts)):
        img.draw_rectangle(pos[0], pos[1], BUTTON_WIDTH, BUTTON_HEIGHT,
                          color=color, thickness=2, fill=True)
        img.draw_string_advanced(pos[0] + 20, pos[1] + 20, 18, text, color=BUTTON_TEXT_COLOR)

def check_button_click(x, y):
    """检测按键点击"""
    if (BUTTON1_POS[0] <= x <= BUTTON1_POS[0] + BUTTON_WIDTH and
        BUTTON1_POS[1] <= y <= BUTTON1_POS[1] + BUTTON_HEIGHT):
        return 1
    if (BUTTON2_POS[0] <= x <= BUTTON2_POS[0] + BUTTON_WIDTH and
        BUTTON2_POS[1] <= y <= BUTTON2_POS[1] + BUTTON_HEIGHT):
        return 2
    if (BUTTON3_POS[0] <= x <= BUTTON3_POS[0] + BUTTON_WIDTH and
        BUTTON3_POS[1] <= y <= BUTTON3_POS[1] + BUTTON_HEIGHT):
        return 3
    return None

# ==================== 快速阈值编辑处理（简化版本） ====================
def draw_fast_threshold_edit_buttons(img):
    """绘制优化的阈值编辑按钮 - 扩大可操作区域"""
    global threshold_edit_mode, threshold_current

    # ==================== 重要改进：优化的按钮配色和尺寸 ====================
    button_color = (80, 120, 160)      # 更醒目的蓝色
    button_active = (0, 180, 0)        # 活跃状态绿色
    button_hover = (100, 140, 180)     # 悬停状态
    text_color = (255, 255, 255)       # 白色文字
    text_color_dark = (0, 0, 0)        # 深色文字

    # ==================== 重要改进：顶部控制按钮区域扩大 ====================
    # 返回按钮 - 左上角，增大尺寸
    img.draw_rectangle(30, 30, 120, 50, color=button_color, thickness=3, fill=True)
    img.draw_rectangle(28, 28, 124, 54, color=(255, 255, 255), thickness=2, fill=False)
    img.draw_string_advanced(70, 45, 20, "返回", color=text_color)

    # 切换按钮 - 右上角，增大尺寸
    switch_color = button_active if threshold_edit_mode == "laser" else button_color
    img.draw_rectangle(650, 30, 120, 50, color=switch_color, thickness=3, fill=True)
    img.draw_rectangle(648, 28, 124, 54, color=(255, 255, 255), thickness=2, fill=False)
    mode_text = "激光点" if threshold_edit_mode == "laser" else "矩形"
    img.draw_string_advanced(685, 45, 20, mode_text, color=text_color)

    # ==================== 重要改进：底部控制按钮区域优化 ====================
    # 重置按钮 - 左下角，增大尺寸
    img.draw_rectangle(50, 400, 140, 60, color=(200, 100, 100), thickness=3, fill=True)
    img.draw_rectangle(48, 398, 144, 64, color=(255, 255, 255), thickness=2, fill=False)
    img.draw_string_advanced(100, 420, 20, "重置", color=text_color)

    # 保存按钮 - 中下，增大尺寸
    img.draw_rectangle(220, 400, 140, 60, color=(100, 180, 100), thickness=3, fill=True)
    img.draw_rectangle(218, 398, 144, 64, color=(255, 255, 255), thickness=2, fill=False)
    img.draw_string_advanced(270, 420, 20, "保存", color=text_color)

    # 保存到字典按钮 - 右下，新增功能
    img.draw_rectangle(390, 400, 140, 60, color=(100, 100, 180), thickness=3, fill=True)
    img.draw_rectangle(388, 398, 144, 64, color=(255, 255, 255), thickness=2, fill=False)
    img.draw_string_advanced(425, 420, 18, "保存字典", color=text_color)

    # 从字典加载按钮 - 最右下，新增功能
    img.draw_rectangle(560, 400, 140, 60, color=(180, 100, 180), thickness=3, fill=True)
    img.draw_rectangle(558, 398, 144, 64, color=(255, 255, 255), thickness=2, fill=False)
    img.draw_string_advanced(595, 420, 18, "加载字典", color=text_color)

    # ==================== 重要改进：6参数编辑区域大幅优化 ====================
    param_names = ["L最小", "L最大", "A最小", "A最大", "B最小", "B最大"]
    param_colors = [(255, 100, 100), (255, 150, 150), (100, 255, 100), (150, 255, 150), (100, 100, 255), (150, 150, 255)]

    # 计算布局参数
    start_y = 120           # 起始Y位置
    row_height = 45         # 每行高度，大幅增加
    button_width = 80       # 按钮宽度，增大
    button_height = 35      # 按钮高度，增大

    for i in range(6):
        y_pos = start_y + i * row_height
        param_color = param_colors[i]

        # ==================== 重要改进：减少按钮 - 左侧，大幅增大 ====================
        img.draw_rectangle(40, y_pos, button_width, button_height, color=param_color, thickness=3, fill=True)
        img.draw_rectangle(38, y_pos-2, button_width+4, button_height+4, color=(255, 255, 255), thickness=2, fill=False)
        img.draw_string_advanced(65, y_pos + 8, 24, "－", color=text_color)

        # ==================== 重要改进：参数显示区域 - 中央，增大字体 ====================
        param_value = threshold_current[i] if i < len(threshold_current) else 0

        # 参数名称和数值背景
        img.draw_rectangle(140, y_pos, 320, button_height, color=(60, 60, 60), thickness=2, fill=True)
        img.draw_rectangle(138, y_pos-2, 324, button_height+4, color=(200, 200, 200), thickness=1, fill=False)

        # 参数名称
        img.draw_string_advanced(150, y_pos + 3, 16, f"{param_names[i]}:", color=(255, 255, 0))

        # 参数数值 - 突出显示
        value_text = f"{param_value:4d}"
        img.draw_string_advanced(350, y_pos + 3, 20, value_text, color=(0, 255, 255))

        # 参数范围提示
        if i < 2:  # L参数
            range_text = "(0-100)"
        else:  # A,B参数
            range_text = "(-128~127)"
        img.draw_string_advanced(280, y_pos + 22, 10, range_text, color=(180, 180, 180))

        # ==================== 重要改进：增加按钮 - 右侧，大幅增大 ====================
        img.draw_rectangle(480, y_pos, button_width, button_height, color=param_color, thickness=3, fill=True)
        img.draw_rectangle(478, y_pos-2, button_width+4, button_height+4, color=(255, 255, 255), thickness=2, fill=False)
        img.draw_string_advanced(505, y_pos + 8, 24, "＋", color=text_color)

        # ==================== 重要改进：快速调整按钮 - 最右侧，新增功能 ====================
        # 快速减少按钮 (减10)
        img.draw_rectangle(580, y_pos, 60, button_height, color=(150, 100, 100), thickness=2, fill=True)
        img.draw_string_advanced(595, y_pos + 8, 16, "--", color=text_color)

        # 快速增加按钮 (加10)
        img.draw_rectangle(650, y_pos, 60, button_height, color=(100, 150, 100), thickness=2, fill=True)
        img.draw_string_advanced(665, y_pos + 8, 16, "++", color=text_color)

    # ==================== 重要改进：当前模式和状态显示 ====================
    # 当前编辑模式显示
    mode_bg_color = (0, 150, 0) if threshold_edit_mode == "rect" else (150, 0, 150)
    img.draw_rectangle(300, 90, 200, 25, color=mode_bg_color, thickness=2, fill=True)
    mode_text = f"当前编辑: {mode_text}阈值"
    img.draw_string_advanced(320, 95, 16, mode_text, color=(255, 255, 255))

def check_fast_threshold_edit_click(x, y):
    """优化的阈值编辑点击检测 - 支持扩大的可操作区域"""
    global threshold_edit_mode, threshold_current, current_mode, RECT_THRESHOLD, LASER_THRESHOLD, threshold_dict

    # ==================== 重要改进：顶部控制按钮 - 扩大点击区域 ====================
    # 返回按钮 - 扩大的点击区域
    if 30 <= x <= 150 and 30 <= y <= 80:
        current_mode = 1
        print("🔄 返回主界面")
        return True

    # 切换按钮 - 扩大的点击区域
    if 650 <= x <= 770 and 30 <= y <= 80:
        if threshold_edit_mode == "rect":
            threshold_edit_mode = "laser"
            threshold_current = list(LASER_THRESHOLD[0])
            print("🔄 切换到激光点阈值编辑")
        else:
            threshold_edit_mode = "rect"
            threshold_current = list(RECT_THRESHOLD[0])
            print("🔄 切换到矩形阈值编辑")
        return True

    # ==================== 重要改进：底部控制按钮 - 扩大点击区域 ====================
    # 重置按钮
    if 50 <= x <= 190 and 400 <= y <= 460:
        if threshold_edit_mode == "rect":
            threshold_current = [13, 35, 20, -5, -44, 16]
            print("🔄 重置矩形阈值到默认值")
        else:
            threshold_current = [47, 80, 9, 91, -55, 63]
            print("🔄 重置激光点阈值到默认值")
        return True

    # 保存按钮
    if 220 <= x <= 360 and 400 <= y <= 460:
        if threshold_edit_mode == "rect":
            RECT_THRESHOLD[0] = tuple(threshold_current)
            print(f"💾 保存矩形阈值: {threshold_current}")
        else:
            LASER_THRESHOLD[0] = tuple(threshold_current)
            print(f"💾 保存激光点阈值: {threshold_current}")
        return True

    # 保存到字典按钮 - 新增功能
    if 390 <= x <= 530 and 400 <= y <= 460:
        if threshold_edit_mode == "rect":
            threshold_dict['rect'].append(tuple(threshold_current))
            print(f"📚 保存矩形阈值到字典: {threshold_current}")
        else:
            threshold_dict['laser'].append(tuple(threshold_current))
            print(f"📚 保存激光点阈值到字典: {threshold_current}")
        return True

    # 从字典加载按钮 - 新增功能
    if 560 <= x <= 700 and 400 <= y <= 460:
        if threshold_edit_mode == "rect" and threshold_dict['rect']:
            threshold_current = list(threshold_dict['rect'][-1])  # 加载最后一个
            print(f"📖 从字典加载矩形阈值: {threshold_current}")
        elif threshold_edit_mode == "laser" and threshold_dict['laser']:
            threshold_current = list(threshold_dict['laser'][-1])  # 加载最后一个
            print(f"📖 从字典加载激光点阈值: {threshold_current}")
        return True

    # ==================== 重要改进：6参数调整按钮 - 大幅扩大点击区域 ====================
    start_y = 120
    row_height = 45
    button_width = 80
    button_height = 35

    for i in range(6):
        y_pos = start_y + i * row_height

        # 减少按钮 - 扩大的点击区域
        if 40 <= x <= 120 and y_pos <= y <= y_pos + button_height:
            if i < 2:  # L参数
                threshold_current[i] = max(0, threshold_current[i] - 2)
            else:  # A,B参数
                threshold_current[i] = max(-128, threshold_current[i] - 2)
            print(f"➖ 参数{i+1}减少: {threshold_current[i]}")
            return True

        # 增加按钮 - 扩大的点击区域
        if 480 <= x <= 560 and y_pos <= y <= y_pos + button_height:
            if i < 2:  # L参数
                threshold_current[i] = min(100, threshold_current[i] + 2)
            else:  # A,B参数
                threshold_current[i] = min(127, threshold_current[i] + 2)
            print(f"➕ 参数{i+1}增加: {threshold_current[i]}")
            return True

        # ==================== 重要改进：快速调整按钮 - 新增功能 ====================
        # 快速减少按钮 (减10)
        if 580 <= x <= 640 and y_pos <= y <= y_pos + button_height:
            if i < 2:  # L参数
                threshold_current[i] = max(0, threshold_current[i] - 10)
            else:  # A,B参数
                threshold_current[i] = max(-128, threshold_current[i] - 10)
            print(f"⏬ 参数{i+1}快速减少: {threshold_current[i]}")
            return True

        # 快速增加按钮 (加10)
        if 650 <= x <= 710 and y_pos <= y <= y_pos + button_height:
            if i < 2:  # L参数
                threshold_current[i] = min(100, threshold_current[i] + 10)
            else:  # A,B参数
                threshold_current[i] = min(127, threshold_current[i] + 10)
            print(f"⏫ 参数{i+1}快速增加: {threshold_current[i]}")
            return True

    return False

# ==================== 主程序 ====================
try:
    print("第八步：动态快速矩形识别系统测试开始")
    print("🚀 性能目标：")
    print(f"   帧率: ≥{TARGET_FPS}FPS")
    print(f"   识别率: ≥{TARGET_SUCCESS_RATE}%")
    print(f"   延迟: ≤{TARGET_LATENCY}ms")
    print("🔧 优化特性：")
    print("   1. 自适应ROI动态调整")
    print("   2. 运动预测算法")
    print("   3. 快速多帧稳定性检测")
    print("   4. 高频串口通信")

    # 初始化串口
    if not init_uart():
        print("⚠️  串口初始化失败，程序继续运行但无通信功能")

    # ==================== 重要修复：传感器初始化优化 ====================
    print("🔧 开始传感器初始化...")

    # 传感器初始化 - 添加错误处理和重试机制
    max_init_retries = 3
    sensor_init_success = False

    for retry in range(max_init_retries):
        try:
            print(f"📷 传感器初始化尝试 {retry + 1}/{max_init_retries}")

            # 创建传感器实例
            sensor = Sensor(id=sensor_id)

            # 重置传感器
            sensor.reset()
            time.sleep_ms(100)  # 等待重置完成

            # 设置帧大小 - 使用标准分辨率
            sensor.set_framesize(width=picture_width, height=picture_height, chn=CAM_CHN_ID_0)
            time.sleep_ms(50)

            # 设置像素格式
            sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)
            time.sleep_ms(50)

            print(f"✅ 传感器配置完成: {picture_width}x{picture_height}, RGB565")
            sensor_init_success = True
            break

        except Exception as e:
            print(f"❌ 传感器初始化失败 (尝试 {retry + 1}): {e}")
            if retry < max_init_retries - 1:
                print("🔄 等待重试...")
                time.sleep_ms(500)
            else:
                print("💥 传感器初始化彻底失败，程序退出")
                sys.exit(1)

    if not sensor_init_success:
        print("💥 传感器初始化失败，程序退出")
        sys.exit(1)

    # 显示器初始化
    print("🖥️ 初始化显示器...")
    try:
        Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
        MediaManager.init()
        print("✅ 显示器初始化成功")
    except Exception as e:
        print(f"❌ 显示器初始化失败: {e}")
        sys.exit(1)

    # 启动传感器 - 添加错误处理
    print("🚀 启动传感器...")
    try:
        sensor.run()
        time.sleep_ms(200)  # 等待传感器稳定
        print("✅ 传感器启动成功")
    except Exception as e:
        print(f"❌ 传感器启动失败: {e}")
        sys.exit(1)

    # 触摸屏初始化
    tp = TOUCH(0)
    clock = time.clock()

    current_mode = 1
    last_send_time = 0
    SEND_INTERVAL = 30  # 提高发送频率（从100ms降到30ms）

    print("✅ 快速检测系统初始化完成")
    print(f"   矩形LAB阈值: {RECT_THRESHOLD[0]}")
    print(f"   激光点LAB阈值: {LASER_THRESHOLD[0]}")

    frame_count = 0
    total_processing_time = 0
    snapshot_error_count = 0
    max_snapshot_errors = 10  # 最大连续错误次数

    # ==================== 重要修复：添加传感器稳定性检查 ====================
    print("🔍 进行传感器稳定性测试...")
    test_success = False
    for test_attempt in range(3):
        try:
            test_img = sensor.snapshot(chn=CAM_CHN_ID_0)
            if test_img is not None:
                print(f"✅ 传感器测试成功 (尝试 {test_attempt + 1})")
                test_success = True
                break
        except Exception as e:
            print(f"⚠️ 传感器测试失败 (尝试 {test_attempt + 1}): {e}")
            time.sleep_ms(200)

    if not test_success:
        print("💥 传感器稳定性测试失败，程序退出")
        sys.exit(1)

    print("🎉 系统初始化完成，开始主循环...")

    while True:
        clock.tick()
        os.exitpoint()

        # ==================== 重要修复：增强的图像获取错误处理 ====================
        img = None
        snapshot_success = False

        # 尝试获取图像，最多重试3次
        for snapshot_retry in range(3):
            try:
                img = sensor.snapshot(chn=CAM_CHN_ID_0)
                if img is not None:
                    snapshot_success = True
                    snapshot_error_count = 0  # 重置错误计数
                    break
                else:
                    print(f"⚠️ 图像为空 (重试 {snapshot_retry + 1}/3)")
                    time.sleep_ms(10)

            except Exception as e:
                print(f"❌ 图像获取失败 (重试 {snapshot_retry + 1}/3): {e}")
                time.sleep_ms(20)

        # 检查图像获取是否成功
        if not snapshot_success or img is None:
            snapshot_error_count += 1
            print(f"💥 连续图像获取失败: {snapshot_error_count}/{max_snapshot_errors}")

            # 如果连续错误过多，尝试重新初始化传感器
            if snapshot_error_count >= max_snapshot_errors:
                print("🔄 尝试重新初始化传感器...")
                try:
                    sensor.stop()
                    time.sleep_ms(100)
                    sensor.reset()
                    time.sleep_ms(200)
                    sensor.set_framesize(width=picture_width, height=picture_height, chn=CAM_CHN_ID_0)
                    sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)
                    sensor.run()
                    time.sleep_ms(300)
                    snapshot_error_count = 0
                    print("✅ 传感器重新初始化完成")
                except Exception as reinit_e:
                    print(f"💥 传感器重新初始化失败: {reinit_e}")
                    print("程序退出")
                    break

            # 跳过当前帧处理
            continue

        # 成功获取图像，继续处理
        frame_count += 1
        frame_start_time = time.ticks_ms()

        try:

            # 处理触摸输入
            points = tp.read()
            if len(points) > 0:
                touch_x, touch_y = points[0].x, points[0].y

                if current_mode == 3:
                    threshold_clicked = check_fast_threshold_edit_click(touch_x, touch_y)
                    if threshold_clicked:
                        time.sleep_ms(200)  # 减少延迟
                else:
                    clicked_button = check_button_click(touch_x, touch_y)
                    if clicked_button:
                        old_mode = current_mode
                        current_mode = clicked_button
                        if current_mode == 3:
                            threshold_edit_mode = "rect"
                            threshold_current = list(RECT_THRESHOLD[0])
                        print(f"🔄 界面切换: {old_mode} → {current_mode}")
                        time.sleep_ms(200)

            # ==================== 重要改进：快速界面处理 ====================
            if current_mode == 1:
                # 快速检测界面
                img.draw_string_advanced(10, 10, 20, "K230快速矩形识别", color=(255, 255, 0))
                img.draw_string_advanced(10, 30, 16, "动态环境优化版", color=(0, 255, 255))

                # 性能信息显示
                perf = get_performance_status()
                img.draw_string_advanced(10, 50, 12, f"FPS: {perf['fps']:.1f} ({perf['fps_status']})",
                                       color=(0, 255, 0) if perf['fps_status'] == "优秀" else (255, 255, 0))

                # 串口状态
                uart_status = "✅连接" if uart else "❌断开"
                img.draw_string_advanced(300, 10, 12, f"串口: {uart_status}", color=(0, 255, 0))

                # ==================== 核心：快速矩形检测 ====================
                rect_success, rect_count, rect_centers, rect_total_center, processing_time = detect_rectangles_fast(img)
                total_processing_time += processing_time

                # 更新性能统计
                current_fps = clock.fps()
                update_performance_stats(processing_time, current_fps)

                # 显示检测统计
                stats = get_detection_stats_text()
                img.draw_string_advanced(10, 70, 12, stats['total_text'], color=(255, 255, 255))
                img.draw_string_advanced(10, 85, 12, stats['success_rate_text'],
                                       color=(0, 255, 0) if rect_detection_stats['success_rate'] > TARGET_SUCCESS_RATE else (255, 255, 0))
                img.draw_string_advanced(10, 100, 12, stats['recent_text'],
                                       color=(0, 255, 0) if rect_detection_stats['recent_success_rate'] > TARGET_SUCCESS_RATE else (255, 255, 0))

                # 显示ROI信息
                if adaptive_roi_config['enabled']:
                    roi_x, roi_y, roi_w, roi_h = get_current_roi()
                    img.draw_string_advanced(10, 115, 10, f"ROI: ({roi_x},{roi_y}) {roi_w}x{roi_h}", color=(200, 200, 200))

                    # 绘制ROI边框
                    if roi_w < picture_width or roi_h < picture_height:
                        img.draw_rectangle(roi_x, roi_y, roi_w, roi_h, color=(255, 255, 0), thickness=1, fill=False)

                # 显示运动预测
                predicted_pos, pred_confidence = get_predicted_position()
                if predicted_pos and pred_confidence > 0.3:
                    img.draw_circle(int(predicted_pos[0]), int(predicted_pos[1]), 5,
                                   color=(0, 255, 255), thickness=2)
                    img.draw_string_advanced(int(predicted_pos[0]) - 20, int(predicted_pos[1]) - 20,
                                           10, "预测", color=(0, 255, 255))

                # 显示检测结果
                if rect_success:
                    img.draw_string_advanced(10, 130, 12, f"快速检测成功! 用时: {processing_time}ms", color=(0, 255, 0))
                else:
                    img.draw_string_advanced(10, 130, 12, f"检测失败 用时: {processing_time}ms", color=(255, 0, 0))

                # ==================== 重要改进：高频智能数据发送 ====================
                current_time = time.ticks_ms()
                if current_time - last_send_time >= SEND_INTERVAL:
                    if rect_success and rect_total_center is not None:
                        stable_center, stable_confidence = get_fast_stable_detection()

                        if stable_center is not None and should_send_fast_data(stable_center, stable_confidence):
                            motion_state = get_motion_state()
                            send_success = send_fast_coordinates(stable_center, (SCREEN_CENTER_X, SCREEN_CENTER_Y), motion_state)
                            if send_success:
                                fast_multi_frame_detection['last_send_center'] = stable_center
                                # ==================== 优化：减少调试输出 ====================
                                # 只在特定条件下输出发送信息
                                if frame_count % 50 == 0:  # 每50帧输出一次
                                    print(f"📡 发送 - 置信度: {stable_confidence:.3f}, 运动: {motion_state}")
                            last_send_time = current_time
                        else:
                            # 减少暂停发送的输出频率
                            if frame_count % 100 == 0:
                                print(f"⏸️ 数据不稳定，暂停发送")
                    else:
                        # 检测失败时可选择发送预测位置
                        predicted_pos, pred_confidence = get_predicted_position()
                        if predicted_pos and pred_confidence > 0.7:
                            motion_state = get_motion_state()
                            send_fast_coordinates(predicted_pos, (SCREEN_CENTER_X, SCREEN_CENTER_Y), motion_state)
                            # 减少预测位置发送的输出频率
                            if frame_count % 50 == 0:
                                print(f"📡 预测位置 - 置信度: {pred_confidence:.3f}")
                        last_send_time = current_time

            elif current_mode == 2:
                # 性能监控界面
                img.draw_string_advanced(10, 10, 20, "性能监控面板", color=(255, 255, 0))

                perf = get_performance_status()

                # FPS监控
                img.draw_string_advanced(10, 40, 16, f"当前FPS: {perf['fps']:.1f}",
                                       color=(0, 255, 0) if perf['fps'] >= TARGET_FPS else (255, 0, 0))
                img.draw_string_advanced(10, 60, 14, f"目标FPS: {TARGET_FPS}", color=(255, 255, 255))

                # 处理时间监控
                img.draw_string_advanced(10, 80, 16, f"处理时间: {perf['processing_time']:.1f}ms",
                                       color=(0, 255, 0) if perf['processing_time'] <= TARGET_LATENCY else (255, 0, 0))
                img.draw_string_advanced(10, 100, 14, f"目标延迟: {TARGET_LATENCY}ms", color=(255, 255, 255))

                # 识别率监控
                img.draw_string_advanced(10, 120, 16, f"识别率: {perf['success_rate']:.1f}%",
                                       color=(0, 255, 0) if perf['success_rate'] >= TARGET_SUCCESS_RATE else (255, 0, 0))
                img.draw_string_advanced(10, 140, 14, f"目标识别率: {TARGET_SUCCESS_RATE}%", color=(255, 255, 255))

                # 系统状态
                img.draw_string_advanced(10, 160, 14, f"总帧数: {frame_count}", color=(255, 255, 255))
                avg_processing = total_processing_time / max(1, frame_count)
                img.draw_string_advanced(10, 175, 14, f"平均处理时间: {avg_processing:.1f}ms", color=(255, 255, 255))

                # ROI状态
                if adaptive_roi_config['enabled']:
                    roi_x, roi_y, roi_w, roi_h = get_current_roi()
                    img.draw_string_advanced(10, 190, 12, f"当前ROI: ({roi_x},{roi_y}) {roi_w}x{roi_h}", color=(0, 255, 255))
                else:
                    img.draw_string_advanced(10, 190, 12, "ROI: 全图模式", color=(0, 255, 255))

                # 运动状态
                motion_state = get_motion_state()
                motion_text = ["静止", "慢速运动", "快速运动"][motion_state]
                img.draw_string_advanced(10, 205, 12, f"运动状态: {motion_text}", color=(255, 255, 0))

                # 预测信息
                predicted_pos, pred_confidence = get_predicted_position()
                if predicted_pos:
                    img.draw_string_advanced(10, 220, 12, f"预测置信度: {pred_confidence:.3f}", color=(0, 255, 255))

            elif current_mode == 3:
                # 快速阈值编辑界面
                show_fast_threshold_edit_interface(img)

            # 图像放大和显示
            img = img.scale(SCALE_FACTOR, SCALE_FACTOR)
            if current_mode == 3:
                draw_fast_threshold_edit_buttons(img)
            else:
                draw_fast_buttons(img)
            Display.show_image(img, x=0, y=0)

            # 性能监控输出
            if frame_count % 100 == 0:
                mode_names = {1: "快速检测", 2: "性能监控", 3: "快速编辑"}
                mode_name = mode_names.get(current_mode, "未知")
                perf = get_performance_status()
                stats = get_detection_stats_text()

                print(f"📊 快速检测状态 - 帧数: {frame_count}")
                print(f"📱 当前模式: {current_mode} ({mode_name})")
                print(f"🚀 性能指标: FPS={perf['fps']:.1f} 延迟={perf['processing_time']:.1f}ms")
                print(f"📈 识别统计: {stats['success_rate_text']}, {stats['recent_text']}")

                # 性能评估
                if perf['fps'] >= TARGET_FPS and perf['processing_time'] <= TARGET_LATENCY and perf['success_rate'] >= TARGET_SUCCESS_RATE:
                    print("🎉 性能目标达成！")
                else:
                    print("⚠️ 性能需要优化")

                print("=" * 60)

        except Exception as e:
            print(f"主循环错误: {e}")
            time.sleep_ms(50)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    print("清理资源...")

    # 输出最终性能报告
    if frame_count > 0:
        final_perf = get_performance_status()
        final_stats = get_detection_stats_text()
        avg_processing = total_processing_time / frame_count

        print("📊 最终性能报告:")
        print(f"   总帧数: {frame_count}")
        print(f"   平均FPS: {final_perf['fps']:.1f} (目标: ≥{TARGET_FPS})")
        print(f"   平均延迟: {avg_processing:.1f}ms (目标: ≤{TARGET_LATENCY}ms)")
        print(f"   {final_stats['success_rate_text']} (目标: ≥{TARGET_SUCCESS_RATE}%)")
        print(f"   {final_stats['recent_text']}")

        # 性能评级
        fps_ok = final_perf['fps'] >= TARGET_FPS
        latency_ok = avg_processing <= TARGET_LATENCY
        rate_ok = final_perf['success_rate'] >= TARGET_SUCCESS_RATE

        if fps_ok and latency_ok and rate_ok:
            print("🏆 性能评级: 优秀 - 所有目标达成")
        elif (fps_ok and latency_ok) or (fps_ok and rate_ok) or (latency_ok and rate_ok):
            print("🥈 性能评级: 良好 - 部分目标达成")
        else:
            print("🥉 性能评级: 需改进 - 多项指标未达标")

    if isinstance(sensor, Sensor):
        sensor.stop()
    if uart:
        uart.deinit()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()

    print("第八步动态快速矩形识别系统测试完成")
    print("🎉 重要改进总结:")
    print("   ✅ 自适应ROI动态调整")
    print("   ✅ 运动预测算法")
    print("   ✅ 快速多帧稳定性检测")
    print("   ✅ 高频串口通信")
    print("   ✅ 性能监控和优化")
