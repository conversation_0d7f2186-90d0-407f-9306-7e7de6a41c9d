# bilibili搜索学不会电磁场看教程
# K230矩形中心激光定位系统 - 第二步：矩形检测功能
# 基于step1的成功显示配置，添加矩形检测和中心点计算功能
# 参考：step1_基础显示功能.py, 12_PID激光点回中.py, 14_脱机调整阈值.py

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *

sensor = None

try:
    print("第二步：矩形检测功能测试开始")
    print("基于step1的成功显示配置")

    # ==================== 检测参数设置 ====================
    # 矩形检测阈值 - 用户可以调整这些参数
    # 格式：[(最小灰度值, 最大灰度值)]
    # 用于检测黑色矩形框，可以根据实际情况调整
    RECT_THRESHOLD = [(0, 80)]  # 检测深色/黑色矩形，可调节
    
    # 矩形检测的最小面积（像素）
    MIN_RECT_AREA = 500  # 最小矩形面积，过滤小噪声
    
    # ROI区域设置（可选功能）
    # 如果需要限制检测区域，可以启用ROI
    USE_ROI = False  # 是否使用ROI限制检测区域
    ROI_X = 50       # ROI起始X坐标
    ROI_Y = 30       # ROI起始Y坐标  
    ROI_WIDTH = 300  # ROI宽度
    ROI_HEIGHT = 180 # ROI高度
    # ====================================================

    # 图像参数设置 - 完全继承step1的成功配置
    picture_width = 400
    picture_height = 240
    sensor_id = 2

    # 显示模式设置 - 完全继承step1的成功配置
    DISPLAY_MODE = "LCD"

    # 根据模式设置显示宽高 - 完全继承step1的成功配置
    if DISPLAY_MODE == "VIRT":
        DISPLAY_WIDTH = ALIGN_UP(1920, 16)
        DISPLAY_HEIGHT = 1080
    elif DISPLAY_MODE == "LCD":
        DISPLAY_WIDTH = 800
        DISPLAY_HEIGHT = 480
    elif DISPLAY_MODE == "HDMI":
        DISPLAY_WIDTH = 1920
        DISPLAY_HEIGHT = 1080
    else:
        raise ValueError("未知的 DISPLAY_MODE，请选择 'VIRT', 'LCD' 或 'HDMI'")

    print(f"显示模式: {DISPLAY_MODE}")
    print(f"图像分辨率: {picture_width}x{picture_height}")
    print(f"显示分辨率: {DISPLAY_WIDTH}x{DISPLAY_HEIGHT}")
    print(f"矩形检测阈值: {RECT_THRESHOLD}")
    print(f"最小矩形面积: {MIN_RECT_AREA}")
    print(f"使用ROI: {USE_ROI}")
    if USE_ROI:
        print(f"ROI区域: ({ROI_X}, {ROI_Y}, {ROI_WIDTH}, {ROI_HEIGHT})")

    # 传感器初始化 - 完全继承step1的成功配置
    sensor = Sensor(id=sensor_id)
    sensor.reset()
    sensor.set_framesize(width=picture_width, height=picture_height, chn=CAM_CHN_ID_0)
    sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)

    # 根据模式初始化显示器 - 完全继承step1的成功配置
    if DISPLAY_MODE == "VIRT":
        Display.init(Display.VIRT, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, fps=60)
    elif DISPLAY_MODE == "LCD":
        Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
    elif DISPLAY_MODE == "HDMI":
        Display.init(Display.LT9611, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)

    # 初始化媒体管理器 - 完全继承step1的成功配置
    MediaManager.init()
    sensor.run()

    # 时钟对象用于FPS计算
    clock = time.clock()

    # 矩形检测函数
    def detect_rectangles(img):
        """
        检测图像中的矩形并计算中心点
        参数: img - 输入图像
        返回: (检测成功标志, 矩形数量, 中心点列表, 总中心点)
        """
        try:
            # 选择检测区域
            if USE_ROI:
                # 使用ROI区域
                roi_img = img.copy(roi=(ROI_X, ROI_Y, ROI_WIDTH, ROI_HEIGHT))
                detect_img = roi_img
            else:
                # 使用整个图像
                detect_img = img
                
            # 转换为灰度图像
            gray_img = detect_img.to_grayscale()
            
            # 二值化处理
            binary_img = gray_img.binary(RECT_THRESHOLD)
            
            # 查找矩形
            rects = binary_img.find_rects(threshold=MIN_RECT_AREA)
            
            centers = []
            if rects:
                print(f"检测到 {len(rects)} 个矩形")
                
                for i, rect in enumerate(rects):
                    # 获取矩形的四个顶点坐标
                    corners = rect.corners()
                    
                    # 计算矩形中心点
                    center_x = sum([corner[0] for corner in corners]) / 4
                    center_y = sum([corner[1] for corner in corners]) / 4
                    
                    # 如果使用ROI，需要转换坐标到原图坐标系
                    if USE_ROI:
                        global_center_x = ROI_X + center_x
                        global_center_y = ROI_Y + center_y
                        # 绘制矩形边框（转换坐标）
                        for j in range(4):
                            next_j = (j + 1) % 4
                            x1, y1 = ROI_X + corners[j][0], ROI_Y + corners[j][1]
                            x2, y2 = ROI_X + corners[next_j][0], ROI_Y + corners[next_j][1]
                            img.draw_line(x1, y1, x2, y2, color=(0, 255, 0), thickness=2)
                    else:
                        global_center_x = center_x
                        global_center_y = center_y
                        # 绘制矩形边框
                        for j in range(4):
                            next_j = (j + 1) % 4
                            img.draw_line(corners[j][0], corners[j][1], 
                                        corners[next_j][0], corners[next_j][1], 
                                        color=(0, 255, 0), thickness=2)
                    
                    centers.append((global_center_x, global_center_y))
                    
                    # 绘制矩形中心点
                    img.draw_circle(int(global_center_x), int(global_center_y), 4, 
                                   color=(255, 0, 0), thickness=2)
                    
                    # 显示矩形编号和坐标
                    img.draw_string_advanced(int(global_center_x) - 15, int(global_center_y) - 25, 
                                           12, f"R{i+1}", color=(255, 255, 0))
                    img.draw_string_advanced(int(global_center_x) - 25, int(global_center_y) + 10, 
                                           10, f"({int(global_center_x)},{int(global_center_y)})", 
                                           color=(255, 255, 0))
                
                # 计算总中心点
                if len(centers) > 1:
                    total_center_x = sum([cx for cx, cy in centers]) / len(centers)
                    total_center_y = sum([cy for cx, cy in centers]) / len(centers)
                    total_center = (total_center_x, total_center_y)
                    
                    # 绘制总中心点（更大的圆）
                    img.draw_circle(int(total_center_x), int(total_center_y), 8, 
                                   color=(0, 255, 255), thickness=3)
                    img.draw_string_advanced(int(total_center_x) - 20, int(total_center_y) - 35, 
                                           14, "总中心", color=(0, 255, 255))
                else:
                    total_center = centers[0] if centers else None
                
                return True, len(rects), centers, total_center
            
            return False, 0, [], None
            
        except Exception as e:
            print(f"矩形检测错误: {e}")
            return False, 0, [], None

    print("系统初始化完成，开始矩形检测...")

    # 统计变量
    frame_count = 0
    detection_count = 0
    last_centers = []
    last_total_center = None

    # 主循环 - 基于step1的成功显示方式
    while True:
        clock.tick()
        os.exitpoint()

        try:
            # 捕获图像 - 完全继承step1的成功方式
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            frame_count += 1

            # 绘制基础信息 - 继承step1的显示方式
            img.draw_string_advanced(10, 10, 25, "K230激光定位系统", color=(255, 255, 0))
            img.draw_string_advanced(10, 35, 20, "第二步：矩形检测功能", color=(0, 255, 255))
            img.draw_string_advanced(10, 60, 16, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
            img.draw_string_advanced(10, 80, 14, f"帧数: {frame_count}", color=(255, 255, 255))
            
            # 绘制ROI区域边界（如果启用）
            if USE_ROI:
                img.draw_rectangle(ROI_X, ROI_Y, ROI_WIDTH, ROI_HEIGHT, 
                                 color=(255, 0, 255), thickness=2, fill=False)
                img.draw_string_advanced(ROI_X + 5, ROI_Y - 15, 12, "检测区域", color=(255, 0, 255))

            # 执行矩形检测
            success, rect_count, centers, total_center = detect_rectangles(img)

            if success:
                detection_count += 1
                last_centers = centers
                last_total_center = total_center
                
                # 显示检测成功信息
                img.draw_string_advanced(10, 100, 16, f"检测成功! 矩形数量: {rect_count}", color=(0, 255, 0))
                img.draw_string_advanced(10, 120, 14, f"检测次数: {detection_count}", color=(0, 255, 0))
                
                # 显示总中心点坐标
                if total_center:
                    img.draw_string_advanced(10, 140, 14, 
                                           f"总中心: ({int(total_center[0])}, {int(total_center[1])})", 
                                           color=(0, 255, 255))
                
                # 显示状态
                img.draw_string_advanced(10, 160, 12, "状态: 矩形检测正常", color=(0, 255, 0))
            else:
                # 显示检测失败信息
                img.draw_string_advanced(10, 100, 16, "未检测到矩形", color=(255, 0, 0))
                img.draw_string_advanced(10, 120, 12, "请调整目标位置或阈值", color=(255, 0, 0))
                img.draw_string_advanced(10, 140, 12, "状态: 等待检测目标", color=(255, 0, 0))
                
                # 显示上次检测结果
                if last_total_center:
                    img.draw_string_advanced(10, 160, 10, 
                                           f"上次中心: ({int(last_total_center[0])}, {int(last_total_center[1])})", 
                                           color=(255, 255, 0))

            # 显示参数信息
            img.draw_string_advanced(10, picture_height - 60, 10, f"阈值: {RECT_THRESHOLD[0]}", color=(255, 255, 255))
            img.draw_string_advanced(10, picture_height - 45, 10, f"最小面积: {MIN_RECT_AREA}", color=(255, 255, 255))
            img.draw_string_advanced(10, picture_height - 30, 10, "参数调整: 修改代码中的RECT_THRESHOLD", color=(0, 255, 255))
            img.draw_string_advanced(10, picture_height - 15, 10, "下一步: 激光点检测功能", color=(255, 255, 0))

            # 显示图像 - 完全继承step1的成功显示方式
            Display.show_image(img, x=int((DISPLAY_WIDTH - picture_width) / 2), y=int((DISPLAY_HEIGHT - picture_height) / 2))

            # 每100帧输出一次状态信息
            if frame_count % 100 == 0:
                print(f"检测状态 - 帧数: {frame_count}, FPS: {clock.fps():.1f}, 检测次数: {detection_count}")

        except Exception as e:
            print(f"主循环错误: {e}")
            time.sleep_ms(100)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
    import traceback
    traceback.print_exc()
finally:
    print("清理资源...")
    # 完全继承step1的成功清理方式
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
    print("第二步测试完成")
