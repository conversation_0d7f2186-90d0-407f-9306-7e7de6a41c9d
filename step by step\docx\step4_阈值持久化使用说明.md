# Step4 阈值持久化功能使用说明

## 功能概述

基于`14_脱机调整阈值.py`的保存机制，为step4程序添加了完整的阈值持久化功能，实现阈值参数的文件保存和自动加载。

## 新增功能

### 1. 文件保存功能 📁

#### **保存内容**：
- **矩形阈值** (RECT_THRESHOLD)：灰度阈值范围
- **激光点阈值** (LASER_THRESHOLD)：LAB颜色空间参数
- **检测参数**：最小矩形面积、最小激光点像素数

#### **保存格式**：
```
# K230激光定位系统阈值配置文件
# 格式: 参数类型=值
# 矩形阈值格式: RECT_THRESHOLD=min,max
# 激光点阈值格式: LASER_THRESHOLD=L_min,L_max,A_min,A_max,B_min,B_max

RECT_THRESHOLD=0,80
LASER_THRESHOLD_0=47,80,9,91,-55,63
LASER_THRESHOLD_1=16,37,23,74,-48,52
MIN_RECT_AREA=500
MIN_LASER_PIXELS=20
```

#### **配置文件位置**：
- **文件名**：`threshold_config.txt`
- **位置**：程序运行目录
- **格式**：纯文本，便于手动编辑

### 2. 文件加载功能 📖

#### **自动加载**：
- 程序启动时自动检测配置文件
- 如果文件存在，自动加载保存的阈值
- 如果文件不存在，使用默认阈值

#### **手动加载**：
- 阈值编辑界面提供"从文件加载"按钮
- 可以随时重新加载配置文件
- 加载后立即更新当前编辑参数

### 3. 用户界面集成 🎯

#### **新增按钮布局**：
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ [返回]                                              [切换]                  │
│                                                                             │
│ 🔧 矩形阈值编辑                           [实时预览图像]                    │
│ 当前阈值: (0, 80)                                                          │
│ 📝 编辑模式: RECT                                                          │
│ 📁 配置文件: threshold_config.txt ✅                                       │
│                                                                             │
│ [-] 最小值: 0                                                          [+] │
│                                                                             │
│ [-] 最大值: 80                                                         [+] │
│                                                                             │
│                                                                             │
│ [重置] [保存] [保存到文件] [从文件加载]                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### **按钮功能**：
- **重置** (20, 420, 100x40)：恢复当前模式的默认阈值
- **保存** (140, 420, 100x40)：保存到内存变量
- **保存到文件** (540, 420, 120x40)：保存到配置文件
- **从文件加载** (680, 420, 120x40)：从配置文件加载

## 操作指南

### 1. 程序启动自动加载

#### **启动流程**：
```bash
python "step by step/step4_交互式按键界面.py"
```

#### **启动日志示例**：
```
📁 正在加载阈值配置...
📖 加载矩形阈值: (0, 80)
📖 加载激光点阈值: (47, 80, 9, 91, -55, 63)
📖 加载激光点阈值: (16, 37, 23, 74, -48, 52)
📖 加载最小矩形面积: 500
📖 加载最小激光点像素: 20
✅ 阈值配置加载成功

📊 当前阈值配置:
   矩形阈值: (0, 80)
   激光点阈值: [(47, 80, 9, 91, -55, 63), (16, 37, 23, 74, -48, 52)]
   最小矩形面积: 500
   最小激光点像素: 20
```

### 2. 阈值编辑和保存

#### **步骤1：进入阈值编辑界面**
1. 点击底部"阈值编辑"按钮
2. 进入阈值编辑界面

#### **步骤2：调整阈值参数**
1. 选择编辑模式（矩形/激光点）
2. 使用"-"和"+"按钮调整参数
3. 观察右上角实时预览效果

#### **步骤3：保存阈值**
- **保存到内存**：点击"保存"按钮
- **保存到文件**：点击"保存到文件"按钮

#### **保存操作日志**：
```
✅ 点击保存到文件按钮
💾 保存矩形阈值到内存: (5, 85)
✅ 阈值已保存到文件: threshold_config.txt
📁 阈值已成功保存到文件
```

### 3. 从文件加载阈值

#### **手动加载操作**：
1. 在阈值编辑界面点击"从文件加载"按钮
2. 系统自动读取配置文件
3. 更新当前编辑参数

#### **加载操作日志**：
```
✅ 点击从文件加载按钮
📖 加载矩形阈值: (5, 85)
📖 加载激光点阈值: (50, 85, 10, 95, -50, 70)
✅ 阈值配置加载成功
🔄 更新当前编辑的矩形阈值: [5, 85]
📁 阈值已从文件加载
```

### 4. 配置文件管理

#### **手动编辑配置文件**：
```
# 可以直接编辑 threshold_config.txt 文件
RECT_THRESHOLD=10,90
LASER_THRESHOLD_0=50,85,10,95,-50,70
MIN_RECT_AREA=600
MIN_LASER_PIXELS=25
```

#### **配置文件验证**：
- 程序启动时自动验证文件格式
- 无效参数会被忽略，使用默认值
- 错误信息会在控制台显示

## 技术实现

### 1. 文件保存机制

```python
def save_threshold_to_file():
    """保存阈值参数到文件"""
    try:
        with open(THRESHOLD_CONFIG_FILE, 'w') as f:
            # 保存矩形阈值
            rect_threshold = RECT_THRESHOLD[0]
            f.write(f"RECT_THRESHOLD={rect_threshold[0]},{rect_threshold[1]}\n")
            
            # 保存激光点阈值
            for i, laser_threshold in enumerate(LASER_THRESHOLD):
                f.write(f"LASER_THRESHOLD_{i}={laser_threshold[0]},{laser_threshold[1]},{laser_threshold[2]},{laser_threshold[3]},{laser_threshold[4]},{laser_threshold[5]}\n")
            
            return True
    except Exception as e:
        print(f"❌ 保存阈值文件失败: {e}")
        return False
```

### 2. 文件加载机制

```python
def load_threshold_from_file():
    """从文件加载阈值参数"""
    global RECT_THRESHOLD, LASER_THRESHOLD, MIN_RECT_AREA, MIN_LASER_PIXELS
    
    try:
        with open(THRESHOLD_CONFIG_FILE, 'r') as f:
            lines = f.readlines()
        
        for line in lines:
            if '=' in line:
                key, value = line.split('=', 1)
                if key == 'RECT_THRESHOLD':
                    values = [int(x.strip()) for x in value.split(',')]
                    RECT_THRESHOLD[0] = tuple(values)
        
        return True
    except Exception as e:
        print(f"❌ 加载阈值文件失败: {e}")
        return False
```

### 3. 界面集成

```python
# 保存到文件按钮检测
if 540 <= x <= 660 and 420 <= y <= 460:
    # 先保存到内存
    if threshold_edit_mode == "rect":
        RECT_THRESHOLD[0] = tuple(threshold_current[:2])
    
    # 然后保存到文件
    if save_threshold_to_file():
        print("📁 阈值已成功保存到文件")
    return True
```

## 错误处理

### 1. 文件权限问题

```python
try:
    with open(THRESHOLD_CONFIG_FILE, 'w') as f:
        # 保存操作
except PermissionError:
    print("❌ 文件写入权限不足")
except Exception as e:
    print(f"❌ 文件操作失败: {e}")
```

### 2. 配置文件格式错误

```python
# 参数验证
if len(values) == 2:
    RECT_THRESHOLD[0] = tuple(values)
else:
    print("⚠️ 矩形阈值格式错误，使用默认值")
```

### 3. 文件不存在处理

```python
if not os.path.exists(THRESHOLD_CONFIG_FILE):
    print(f"⚠️ 配置文件不存在，使用默认阈值")
    return False
```

## 使用场景

### 1. 不同环境适配

#### **室内环境**：
```
RECT_THRESHOLD=0,60
LASER_THRESHOLD_0=40,75,5,85,-60,50
```

#### **室外环境**：
```
RECT_THRESHOLD=20,100
LASER_THRESHOLD_0=55,90,15,100,-40,80
```

### 2. 多套配置管理

#### **创建多个配置文件**：
- `threshold_config_indoor.txt`：室内配置
- `threshold_config_outdoor.txt`：室外配置
- `threshold_config_night.txt`：夜间配置

#### **快速切换**：
```bash
# 复制配置文件
cp threshold_config_indoor.txt threshold_config.txt
```

## 测试验证

### 1. 运行测试版本

```bash
python "step by step/step4_阈值持久化测试版.py"
```

### 2. 测试功能

- ✅ 文件保存功能测试
- ✅ 文件加载功能测试
- ✅ 参数恢复验证
- ✅ 配置文件内容检查

### 3. 预期输出

```
🧪 开始测试阈值文件操作功能...
📝 测试1: 保存阈值到文件
✅ 保存测试通过
📖 测试3: 从文件加载阈值
✅ 加载测试通过
✅ 矩形阈值恢复正确
✅ 激光点阈值恢复正确
🎯 文件操作测试完成!
```

## 总结

阈值持久化功能为K230激光定位系统提供了：

- ✅ **完整的文件保存和加载机制**
- ✅ **自动启动加载配置**
- ✅ **用户友好的界面集成**
- ✅ **多环境配置支持**
- ✅ **错误处理和容错机制**
- ✅ **基于14_脱机调整阈值.py的成熟实现**

现在用户可以根据不同的环境光照条件调整阈值，保存配置，并在下次使用时自动加载个性化的阈值设置！
