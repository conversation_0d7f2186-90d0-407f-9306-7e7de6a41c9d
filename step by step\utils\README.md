# Utils 辅助文件夹

## 📁 文件夹说明

这个文件夹用于存放所有辅助性代码文件和工具文件，包括：

### 🔧 辅助代码文件
- 修复版本代码
- 测试代码
- 工具函数
- 示例配置文件

### 📄 文件类型
- `.py` - Python辅助代码
- `.c` - C语言代码文件
- `.txt` - 配置文件和示例文件
- 其他辅助性文件

### 📋 组织规则
1. **修复版代码**: 以`stepX_修复版_`开头的文件
2. **工具代码**: 通用工具函数和辅助脚本
3. **配置文件**: 示例配置和模板文件
4. **测试代码**: 用于测试和调试的代码

### 🎯 使用说明
- 主要的stepX代码文件保留在上级目录
- 辅助性和工具性文件统一存放在此文件夹
- 便于项目文件的分类管理和维护

## 📚 相关文档
详细的技术文档和说明文档请查看 `../docx/` 文件夹。
